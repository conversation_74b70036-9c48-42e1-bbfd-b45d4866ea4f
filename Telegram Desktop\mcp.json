{"mcpServers": {"@smithery-ai/server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--config", "{}"]}, "@smithery-ai/fetch": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/fetch", "--config", "{}"]}, "files": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop\\mcp-server-filesystem"]}, "@wopal/mcp-server-hotnews": {"command": "cmd", "args": ["/c", "npx", "@wopal/mcp-server-hotnews"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "hn-server": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@pskill9/hn-server"]}, "duckduckgo-mcp-server": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@nickclyde/duckduckgo-mcp-server"]}}}