window['twitter-adsDeps'] = ["/integrations/vendor/commons.59560acdd69ed701c941.js"];window['twitter-adsLoader'] = function() { return window["twitter-adsIntegration"]=function(t){function e(e){for(var i,a,s=e[0],p=e[1],c=e[2],d=0,f=[];d<s.length;d++)a=s[d],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&f.push(o[a][0]),o[a]=0;for(i in p)Object.prototype.hasOwnProperty.call(p,i)&&(t[i]=p[i]);for(u&&u(e);f.length;)f.shift()();return r.push.apply(r,c||[]),n()}function n(){for(var t,e=0;e<r.length;e++){for(var n=r[e],i=!0,s=1;s<n.length;s++){var p=n[s];0!==o[p]&&(i=!1)}i&&(r.splice(e--,1),t=a(a.s=n[0]))}return t}var i={},o={142:0},r=[];function a(e){if(i[e])return i[e].exports;var n=i[e]={i:e,l:!1,exports:{}};return t[e].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=t,a.c=i,a.d=function(t,e,n){a.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)a.d(n,i,function(e){return t[e]}.bind(null,i));return n},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,"a",e),e},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.p="";var s=window.webpackJsonp_name_Integration=window.webpackJsonp_name_Integration||[],p=s.push.bind(s);s.push=e,s=s.slice();for(var c=0;c<s.length;c++)e(s[c]);var u=p;return r.push(["qU88",0]),n()}({qU88:function(t,e,n){"use strict";var i=n("hjHq"),o=n("mTd2"),r=n("LUFQ"),a=n("IWyO"),s=n("WiAo"),p=n("NGGi").Track,c=n("ge09"),u=t.exports=i("Twitter Ads").option("page","").option("universalTagPixelId","").option("identifier","productId").tag("singleTag",'<img src="//analytics.twitter.com/i/adsct?txn_id={{ pixelId }}&p_id=Twitter&tw_sale_amount={{ revenue }}&tw_order_quantity={{ quantity }}"/>').tag("universalTag",'<script src="//static.ads-twitter.com/uwt.js">').mapping("events");function d(t){return t.status?{status:t.status}:{}}u.prototype.initialize=function(){var t,e,n,i,o,r,a=this;this.options.universalTagPixelId?(t=window,e=document,n="script",t.twq||((i=t.twq=function(){i.exe?i.exe.apply(i,arguments):i.queue.push(arguments)}).version="1.1",i.queue=[],(o=e.createElement(n)).async=!0,o.src="https://static.ads-twitter.com/uwt.js",(r=e.getElementsByTagName(n)[0]).parentNode.insertBefore(o,r)),this.load("universalTag",(function(){window.twq("config",a.options.universalTagPixelId),a.ready()}))):this.ready()},u.prototype.page=function(t){this.options.universalTagPixelId&&window.twq("track","PageView"),this.options.page&&this.load("singleTag",{pixelId:this.options.page,revenue:0,quantity:0})},u.prototype.track=function(t){this.fireLegacyConversionTags(t)},u.prototype.productsSearched=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=d(t.properties());window.twq("track","Search",e)}},u.prototype.productViewed=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n={content_ids:[t[this.options.identifier]()],content_type:"product",content_name:t.name(),content_category:t.category()};n=c(n,d(e)),window.twq("track","ViewContent",n)}},u.prototype.productAdded=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n={content_ids:[t[this.options.identifier]()],content_type:"product",content_name:t.name()};n=c(n,d(e)),window.twq("track","AddToCart",n)}},u.prototype.orderCompleted=function(t){var e=this.options.identifier,n=r((function(t,e){return t+(s(e,"quantity")||0)}),0,t.products());if(this.fireLegacyConversionTags(t,{quantity:n}),this.options.universalTagPixelId){var i={currency:t.currency(),content_type:"product",order_id:t.orderId(),num_items:n.toString()};t.revenue()&&(i.value=t.revenue().toFixed(2)),i=c(i,d(t.properties()));var o=r((function(t,n){var i=new p({properties:n}),o=i[e]();return t.ids.push(o),t.names.push(i.name()),t}),{ids:[],names:[]},t.products());i.content_ids=o.ids.sort(),i.content_name=o.names.sort().join(", "),window.twq("track","Purchase",i)}},u.prototype.productAddedToWishlist=function(t){if(this.fireLegacyConversionTags(t),this.options.universalTagPixelId){var e=t.properties(),n=this.options.identifier,i={content_name:t.name(),content_category:t.category(),content_ids:[t[n]()]};i=c(i,d(e)),window.twq("track","AddToWishlist",i)}},u.prototype.checkoutStarted=function(t){var e=r((function(t,e){return t+(s(e,"quantity")||0)}),0,t.products());if(this.fireLegacyConversionTags(t,{quantity:e}),this.options.universalTagPixelId){var n=this.options.identifier,i=r((function(t,e){var i=new p({properties:e}),o=i[n]();return t.ids.push(o),t.names.push(i.name()),t.categories.push(i.category()),t}),{ids:[],names:[],categories:[]},t.products()),o={content_ids:i.ids.sort(),content_name:i.names.sort().join(", "),content_category:i.categories.join(", ")};o=c(o,d(t.properties())),window.twq("track","InitiateCheckout",o)}},u.prototype.paymentInfoEntered=function(t){this.fireLegacyConversionTags(t);var e=c({},d(t.properties()));this.options.universalTagPixelId&&window.twq("track","AddPaymentInfo",e)},u.prototype.fireLegacyConversionTags=function(t,e){var n=this.events(t.event()),i=this;a(n,(function(n){var r={pixelId:n,quantity:t.proxy("properties.quantity")||0,revenue:t.revenue()||0};e&&(r=o(e,r)),i.load("singleTag",r)}))}}});
//# sourceMappingURL=twitter-ads.js.map
};