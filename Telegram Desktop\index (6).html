<!DOCTYPE html>
<html>
<head>
    <title>Multi-Agent Chat</title>
    <style>
        body { margin: 20px; font-family: Arial; }
        #container { max-width: 1200px; margin: 0 auto; }
        #user-info {
            background: #f0f0f0;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        #userInput {
            width: 90%;
            height: 80px;
            margin: 10px 0;
            padding: 10px;
        }
        #submitBtn {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #responses {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .card {
            flex: 0 0 300px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #0066cc;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="user-info">
            <strong>Current Time (UTC):</strong> 2025-02-20 08:44:11<br>
            <strong>User:</strong> fud114514
        </div>
        <textarea id="userInput" placeholder="请输入您的问题..."></textarea><br>
        <button id="submitBtn" onclick="getResponses()">提交问题</button>
        <div id="responses"></div>
    </div>

    <script>
        const API_KEY = "sk-VqeLb5tZ86GhwCznTou1kXfEmZVEwGzia5wtVFHFZmxD2zMC";
        const API_URL = "https://api.chatanywhere.tech/v1/chat/completions";

        // 精简版 agents 列表
        const agents = {
            "PrincipleBot": "你是一个专家，专注于揭示事物运作的核心原理与基本机制，帮助理解问题的根本。", 
            "EssenceBot": "你是一个哲学家，善于分析事物的本质，探索其内在特性和根本属性。", 
            "CausalityBot": "你是一个因果关系分析师，专注于识别并阐明事物间的因果链条与相互作用。", 
            "ProcessBot": "你是一个任务流程专家，擅长设计、优化和分析复杂任务的执行步骤及决策节点。", 
            "SystemicBot": "你是一个系统思维专家，能够从整体视角解析问题，揭示系统内各部分的相互依赖与作用。", 
            "FrameworkBot": "你是一个框架设计师，专注于构建解决问题的结构化框架，分析各要素的相互关系。", 
            "StructureBot": "你是一个结构分析师，擅长识别并解析问题的层次结构，理解各部分的组织方式与相互影响。", 
            "AssociationBot": "你是一个关联分析专家，专注于揭示不同因素之间的相互关联，分析其交织与交互效应。", 
            "CommonSenseBot": "你是一个常识专家，能够提供基于常识的简单、直接的解决方案。", 
            "PreKnowledgeBot": "你是一个知识背景专家，负责识别并总结为解决问题所需的前置知识与背景。", 
            "SystematicThinkerBot": "你是一个系统性思维专家，能够从多维度角度全局分析问题，识别各因素间的相互影响与依赖。", 
            "CriticalThinkerBot": "你是一个批判性思维专家，擅长质疑现有假设，全面审视问题并提出创新的解决方案。", 
            "FlexibleThinkerBot": "你是一个灵活思维专家，能够迅速适应变化，在不确定性中找到灵活且创新的解决方案。", 
            "InterdisciplinaryThinkerBot": "你是一个跨学科思维专家，善于融合多个领域的知识，提供全面、多角度的解决方案。", 
            "DataDrivenThinkerBot": "你是一个数据驱动思维专家，专注于通过数据分析做出精准决策，优化解决方案。", 
            "LongTermThinkerBot": "你是一个长期思维专家，擅长平衡短期与长期目标，提出面向未来的战略规划与决策。"
        };

        async function getResponses() {
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) {
                alert('请输入问题');
                return;
            }

            document.getElementById('responses').innerHTML = ''; // 清空之前的响应

            for (const [agentName, prompt] of Object.entries(agents)) {
                // 创建卡片
                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `<h3>${agentName}</h3><p>思考中...</p>`;
                document.getElementById('responses').appendChild(card);

                // 调用 API
                try {
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${API_KEY}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: "gpt-4o-mini",
                            messages: [
                                { role: "system", content: prompt },
                                { role: "user", content: userInput }
                            ],
                            temperature: 0.7,
                            max_tokens: 1000
                        })
                    });

                    const data = await response.json();
                    const reply = data.choices[0].message.content;
                    card.innerHTML = `<h3>${agentName}</h3><p>${reply}</p>`;
                } catch (error) {
                    card.innerHTML = `<h3>${agentName}</h3><p>Error: ${error.message}</p>`;
                }
            }
        }
    </script>
</body>
</html>