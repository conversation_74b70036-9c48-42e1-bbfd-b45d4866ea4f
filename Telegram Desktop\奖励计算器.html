<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分与奖励计算器</title>
    <style>
        body {
            font-family: sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 600px;
            margin: auto;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h2, h3 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        h2:first-child {
            margin-top: 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="number"] {
            width: calc(100% - 12px); /* Adjust width for padding */
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box; /* Include padding in width */
        }
        .input-group label {
             display: inline-block; /* Radio labels inline */
             margin-right: 15px;
             font-weight: normal;
        }
        .input-group input[type="radio"] {
             margin-right: 5px;
        }
        .result-section p {
            margin: 8px 0;
            background-color: #e9f5ff;
            padding: 10px;
            border-left: 3px solid #007bff;
            border-radius: 4px;
        }
        .result-section strong {
            display: inline-block;
            min-width: 180px; /* Align values */
            color: #0056b3;
        }
        .result-value {
            font-weight: bold;
            color: #d9534f; /* Highlight result value */
        }
        .note {
            font-size: 0.9em;
            color: #777;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<div class="container">
    <h2>积分与奖励计算器</h2>

    <!-- 输入区域 -->
    <h3>基础设置</h3>
    <label for="dailyBaseCap">每日基础积分上限:</label>
    <input type="number" id="dailyBaseCap" value="0" min="0" oninput="calculate()">

    <label for="dailySignInPoints">每日签到积分:</label>
    <input type="number" id="dailySignInPoints" value="0" min="0" oninput="calculate()">

    <p class="note">邀请积分上限固定为: <span id="invitationCapDisplay">777</span></p>

    <!-- 奖励百分比输入 -->
    <h3>奖励设置</h3>
    <label for="reward1Percent">奖励1 百分比 (x%):</label>
    <input type="number" id="reward1Percent" value="0" min="0" max="100" step="0.1" oninput="calculate()">
     <div class="input-group">
        计算基准:
        <label><input type="radio" name="reward1Base" value="monthly" checked onchange="calculate()"> 单月上限</label>
        <label><input type="radio" name="reward1Base" value="overall" onchange="calculate()"> 综合上限</label>
    </div>
    <br>
    <label for="reward2Percent">奖励2 百分比 (y%):</label>
    <input type="number" id="reward2Percent" value="0" min="0" max="100" step="0.1" oninput="calculate()">
     <div class="input-group">
        计算基准:
        <label><input type="radio" name="reward2Base" value="monthly" checked onchange="calculate()"> 单月上限</label>
        <label><input type="radio" name="reward2Base" value="overall" onchange="calculate()"> 综合上限</label>
    </div>

    <!-- 结果显示区域 -->
    <h3>计算结果 (按30天/月)</h3>
    <div class="result-section">
        <p><strong>基础活动积分月上限:</strong> <span id="monthlyBaseCap" class="result-value">0</span></p>
        <p><strong>签到积分月上限:</strong> <span id="monthlySignInCap" class="result-value">0</span></p>
        <p><strong>用户单月积分上限:</strong> <span id="userMonthlyTotalCap" class="result-value">0</span></p>
        <p><strong>用户综合积分上限:</strong> <span id="userOverallTotalCap" class="result-value">0</span></p>
        <p class="note">*综合上限 = 单月上限 + 777 (请确认此额外777的业务含义)</p>
        <hr>
        <p><strong>奖励1:</strong> <span id="reward1Value" class="result-value">0</span></p>
        <p><strong>奖励2:</strong> <span id="reward2Value" class="result-value">0</span></p>
    </div>

</div>

<script>
    function calculate() {
        // --- 获取输入值 ---
        const dailyBaseCap = parseFloat(document.getElementById('dailyBaseCap').value) || 0;
        const dailySignInPoints = parseFloat(document.getElementById('dailySignInPoints').value) || 0;
        const reward1Percent = parseFloat(document.getElementById('reward1Percent').value) || 0;
        const reward2Percent = parseFloat(document.getElementById('reward2Percent').value) || 0;

        const reward1BaseType = document.querySelector('input[name="reward1Base"]:checked').value;
        const reward2BaseType = document.querySelector('input[name="reward2Base"]:checked').value;

        // --- 固定值 ---
        const invitationCap = 777;
        const daysInMonth = 30; // 按30天计算

        // --- 计算月上限 ---
        const monthlyBaseCap = dailyBaseCap * daysInMonth;
        const monthlySignInCap = dailySignInPoints * daysInMonth;
        const userMonthlyTotalCap = monthlyBaseCap + monthlySignInCap + invitationCap;
        // 综合上限 = 单月上限 + 邀请上限(777) + 额外777
        // 根据原始公式: 每人综合积分上限 = 积分上限 + 签到上限 + 邀请上限 + 777
        // 即: 每人综合积分上限 = 每人单月积分上限 + 777
        const userOverallTotalCap = userMonthlyTotalCap + 777;

        // --- 计算奖励 ---
        let reward1BaseValue = 0;
        if (reward1BaseType === 'monthly') {
            reward1BaseValue = userMonthlyTotalCap;
        } else { // overall
            reward1BaseValue = userOverallTotalCap;
        }
        const reward1Value = reward1BaseValue * (reward1Percent / 100);

        let reward2BaseValue = 0;
        if (reward2BaseType === 'monthly') {
            reward2BaseValue = userMonthlyTotalCap;
        } else { // overall
            reward2BaseValue = userOverallTotalCap;
        }
        const reward2Value = reward2BaseValue * (reward2Percent / 100);


        // --- 显示结果 ---
        document.getElementById('invitationCapDisplay').textContent = invitationCap;
        document.getElementById('monthlyBaseCap').textContent = monthlyBaseCap.toFixed(2); // Use toFixed for consistency if decimals are possible
        document.getElementById('monthlySignInCap').textContent = monthlySignInCap.toFixed(2);
        document.getElementById('userMonthlyTotalCap').textContent = userMonthlyTotalCap.toFixed(2);
        document.getElementById('userOverallTotalCap').textContent = userOverallTotalCap.toFixed(2);
        document.getElementById('reward1Value').textContent = reward1Value.toFixed(2);
        document.getElementById('reward2Value').textContent = reward2Value.toFixed(2);
    }

    // --- 页面加载时计算一次初始值 ---
    window.onload = calculate;
</script>

</body>
</html>