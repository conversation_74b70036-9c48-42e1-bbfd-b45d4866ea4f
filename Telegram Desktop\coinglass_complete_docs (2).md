# CoinGlass API v4.0 中文文档

**爬取时间:** 2025-05-26 01:59:48

---

## 币种爆仓历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/aggregated-liquidation-history
币种爆仓历史
https://open-api-v4.coinglass.com
/api/futures/liquidation/aggregated-history
该接口提供币种聚合多个交易所的多空爆仓历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
   {
      "aggregated_long_liquidation_usd": "4611285.1141", // 聚合多单爆仓金额（美元）
      "aggregated_short_liquidation_usd": "4788636.51145", // 聚合空单爆仓金额（美元）
      "time": 1636588800 // 时间戳
   },
    {
      "aggregated_long_liquidation_usd": "4611285.1141", // 聚合多单爆仓金额（美元）
      "aggregated_short_liquidation_usd": "4788636.51145", // 聚合空单爆仓金额（美元）
      "time": 1636588800 // 时间戳
   },
    ..
  ]
}


---

## 币种主动买卖历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/aggregated-taker-buysell-volume-history
币种主动买卖历史
https://open-api-v4.coinglass.com
/api/futures/aggregated-taker-buy-sell-volume/history
该接口提供合约市场中币种的主动买入与卖出成交量的历史数据
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741622400000, // 时间戳（毫秒）
      "aggregated_buy_volume_usd": 968834542.3787, // 聚合买单成交量（美元）
      "aggregated_sell_volume_usd": 1054582654.8138 // 聚合卖单成交量（美元）
    },
    {
      "time": 1741626000000,
      "aggregated_buy_volume_usd": 1430620763.2041,
      "aggregated_sell_volume_usd": 1559166911.2821
    },
    {
      "time": 1741629600000,
      "aggregated_buy_volume_usd": 1897261721.0129,
      "aggregated_sell_volume_usd": 2003812276.7812
    }
  ]
}


---

## 比特币Ahr999指标

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ahr999
比特币Ahr999指标
https://open-api-v4.coinglass.com
/api/index/ahr999
该接口提供比特币 AHR999 指标相关数据，包括历史均值、AHR999 当前值，以及不同日期对应的具体数据值。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "date_string": "2011/02/01",            // 日期字符串（格式：YYYY/MM/DD）
      "average_price": 0.1365,                // 当日的平均价格
      "ahr999_value": 4.441692296429609,      // AHR999 指数值
      "current_value": 0.626                  // 当日的当前值
    },
    {
      "date_string": "2011/02/02",            // 日期字符串（格式：YYYY/MM/DD）
      "average_price": 0.1383,                // 当日的平均价格
      "ahr999_value": 5.642181244439729,      // AHR999 指数值
      "current_value": 0.713                  // 当日的当前值
    }
    // 更多数据条目...
  ]
}


---

## 🔐 身份验证

**URL:** https://docs.coinglass.com/v4.0-zh/reference/authentication
🔐 身份验证
所有对 CoinGlass API 的请求都需要使用唯一且与用户绑定的 API Key 进行身份验证。
如果请求中缺少有效的 API Key 或请求头不完整，将会被拒绝，并返回身份验证错误。
🧾
如何获取 API Key
请登录您的账号，并在
API Key 管理页面
生成您的 API Key 以开始使用。.
✅
示例用法
curl
curl -request GET
--url  'https://open-api-v4.coinglass.com/api/futures/supported-coins'
--header 'accept: application/json'\
--header 'CG-API-KEY: API\_KEY'
📦
请求头要求：
每一个请求必须包含以下 HTTP 请求头：
CG-API-KEY: '你的API Key'
如果缺少该请求头，或提供的 API Key 无效，请求将被拒绝，并返回 401 Unauthorized（未授权）错误。❗

---

## 合约基差

**URL:** https://docs.coinglass.com/v4.0-zh/reference/basis
合约基差
https://open-api-v4.coinglass.com
/api/futures/basis/history
该接口提供合约基差的历史数据，包括开盘与收盘时的基差数值
缓存/ 更新频率 :
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741629600000,            // 时间戳（毫秒）
      "open_basis": 0.0504,             // 开盘基差（单位：%）
      "close_basis": 0.0445,            // 收盘基差（单位：%）
      "open_change": 39.5,              // 开盘时基差相较前一时段变化幅度（单位：%）
      "close_change": 34.56             // 收盘时基差相较前一时段变化幅度（单位：%）
    },
    {
      "time": 1741633200000,            // 时间戳（毫秒）
      "open_basis": 0.0446,             // 开盘基差（单位：%）
      "close_basis": 0.03,              // 收盘基差（单位：%）
      "open_change": 34.65,             // 开盘时基差变化幅度（单位：%）
      "close_change": 23.74             // 收盘时基差变化幅度（单位：%）
    }
  ]
}


---

## 比特币泡沫指数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-bubble-index
比特币泡沫指数
https://open-api-v4.coinglass.com
/api/index/bitcoin/bubble-index
该接口提供比特币泡沫指数接口
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "price": 0.0495,                          // 当前价格
      "bubble_index": -29.59827206,             // 泡沫指数
      "google_trend_percent": 0.0287,           // Google 趋势百分比
      "mining_difficulty": 181.543,             // 挖矿难度
      "transaction_count": 235,                 // 交易数量
      "address_send_count": 390,                // 地址发送数量
      "tweet_count": 0,                         // 推特数量
      "date_string": "2010-07-17"               // 日期字符串
    },
    {
      "price": 0.0726,                          // 当前价格
      "bubble_index": -29.30591863,             // 泡沫指数
      "google_trend_percent": 0.0365,           // Google 趋势百分比
      "mining_difficulty": 181.543,             // 挖矿难度
      "transaction_count": 248,                 // 交易数量
      "address_send_count": 424,                // 地址发送数量
      "tweet_count": 0,                         // 推特数量
      "date_string": "2010-07-18"               // 日期字符串
    }
  ]
}


---

## ETF净资产历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-etf-netassets-history
ETF净资产历史
https://open-api-v4.coinglass.com
/api/etf/bitcoin/net-assets/history
该接口提供比特币交易所交易基金（ETF）的历史净资产数据。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "net_assets_usd": 51671409241.39,         // 净资产总额（USD）
      "change_usd": 655300000,                  // 当日资金变化（USD）
      "timestamp": 1704931200000,               // 日期（时间戳，单位毫秒）
      "price_usd": 46337.8                      // 当日BTC价格（USD）
    },
    {
      "net_assets_usd": 51874409241.39,         // 净资产总额（USD）
      "change_usd": 203000000,                  // 当日资金变化（USD）
      "timestamp": 1705017600000,               // 日期（时间戳，单位毫秒）
      "price_usd": 42788.9                      // 当日BTC价格（USD）
    }
  ]
}


---

## ETF溢价/折扣历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-etf-premium-discount-history
ETF溢价/折扣历史
https://open-api-v4.coinglass.com
/api/etf/bitcoin/premium-discount/history
该接口提供比特币ETF的溢价或折扣率的历史数据，包括各ETF代码的净资产价值（NAV）、市场价格，以及对应的溢价/折扣百分比。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                   
  "msg": "success",                              
  "data": [
    {
      "timestamp": 1706227200000,                 // 日期（时间戳，单位为毫秒）
      "list": [
        {
          "ticker": "GBTC",                       // ETF代码
          "nav_usd": 37.51,                        // 净资产价值（USD）
          "market_price_usd": 37.51,               // 市场价格（USD）
          "premium_discount_details": 0            // 溢价/折扣百分比
        },
        {
          "ticker": "IBIT",                       // ETF代码
          "nav_usd": 23.94,                        // 净资产价值（USD）
          "market_price_usd": 23.99,               // 市场价格（USD）
          "premium_discount_details": 0.22         // 溢价/折扣百分比
        },
        {
          "ticker": "FBTC",                       // ETF代码
          "nav_usd": 36.720807,                    // 净资产价值（USD）
          "market_price_usd": 36.75,               // 市场价格（USD）
          "premium_discount_details": 0.0795       // 溢价/折扣百分比
        }
      ]
    }
  ]
}


---

## 比特币ETF列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-etfs
比特币ETF列表
https://open-api-v4.coinglass.com
/api/etf/bitcoin/list
该接口提供比特币交易型基金（ETF）的关键信息列表
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "ticker": "GBTC",                                // ETF标识符
      "fund_name": "Grayscale Bitcoin Trust ETF",      // 基金名称
      "region": "us",                                  // 所属地区
      "market_status": "early_trading",                // 市场状态 (open/closed/early_trading等)
      "primary_exchange": "ARCX",                      // 主交易所
      "cik_code": "0001588489",                        // CIK代码 (唯一标识符)
      "fund_type": "Spot",                             // 基金类型 (Spot/ETF/Futures等)
      "list_date": 1424822400000,                      // 上市日期 (时间戳，单位毫秒)
      "shares_outstanding": "240750100",               // 流通股数
      "aum_usd": "16137543152.34",                     // 管理资产总值 (USD)
      "management_fee_percent": "1.5",                 // 管理费率 (%)
      "last_trade_time": 1745225312958,                // 最新成交时间 (时间戳，单位毫秒)
      "last_quote_time": 1745225389483,                // 最新计价时间 (时间戳，单位毫秒)
      "volume_quantity": 1068092,                      // 成交量
      "volume_usd": 71485902.2312,                     // 成交量 (USD)
      "price_change_usd": 0.47,                        // 价格变化 (USD)
      "price_change_percent": 0.71,                    // 价格变化 (%)
      "asset_details": {
        "net_asset_value_usd": 67.03,                  // 净值 (USD)
        "premium_discount_percent": 0.09,              // 溢价/折价率 (%)
        "btc_holding": 190124.5441,                    // BTC余额
        "btc_change_percent_24h": 0,                   // 24小时BTC变化 (%)
        "btc_change_24h": -7.8136,                     // 24小时BTC变化量
        "btc_change_percent_7d": -0.32,                // 7天BTC变化 (%)
        "btc_change_7d": -615.563,                     // 7天BTC变化量
        "update_date": "2025-04-17"                    // 更新时间
      },
      "update_timestamp": 1745224505000                // 数据更新时间 (时间戳，单位毫秒)
    }
  ]
}


---

## 比特币盈利日期

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-profitable-days
比特币盈利日期
https://open-api-v4.coinglass.com
/api/index/bitcoin/profitable-days
该接口提供比特币历史上所有盈利的日期
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                              
  "msg": "success",                        
  "data": [
    {
      "side": 1,                              // 交易方向，1表示买入，0表示卖出
      "timestamp": 1282003200000,             // 时间戳（毫秒）
      "price": 0.07                           // 价格
    },
    {
      "timestamp": 1282089600000,             // 时间戳（毫秒）
      "price": 0.068,                         // 价格
      "side": 1                               // 交易方向，1表示买入，0表示卖出
    }
  ]
}


---

## 比特币彩虹图

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitcoin-rainbow-chart
比特币彩虹图
https://open-api-v4.coinglass.com
/api/index/bitcoin/rainbow-chart
该接口提供比特币彩虹图数据
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                              
  "msg": "success",                        
  "data": [
    [
      0.07,                                 // 当前价格
      0.033065,                             // 阴影最低值
      0.044064,                             // 第一层
      0.059892,                             // 第二层
      0.082219,                             // 第三层
      0.110996,                             // 第四层
      0.149845,                             // 第五层
      0.205865,                             // 第六层
      0.283454,                             // 第七层
      0.380525,                             // 第八层
      0.517626,                             // 第九层
      1282003200000                        // 时间戳
    ]
  ]
}


---

## Bitfinex杠杆多空仓位

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bitfinex-margin-long-short
Bitfinex杠杆多空仓位
https://open-api-v4.coinglass.com
/api/bitfinex-margin-long-short
该接口提供来自 Bitfinex 的杠杆多头和空头仓位数据。
缓存 / 更新频率:
实时更新.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1658880000,              // 时间戳，表示数据对应的时间点
      "long_quantity": 104637.94,       // 多头持仓数量
      "short_quantity": 2828.53        // 空头持仓数量
    },
    {
      "time": 1658966400,              // 时间戳，表示数据对应的时间点
      "long_quantity": 105259.46,       // 多头持仓数量
      "short_quantity": 2847.84        // 空头持仓数量
    }
    // 更多数据项...
  ]
}


---

## 借贷利率

**URL:** https://docs.coinglass.com/v4.0-zh/reference/borrow-interest-rate
借贷利率
https://open-api-v4.coinglass.com
/api/borrow-interest-rate/history
该接口提供加密货币的借贷利率数据
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                             
  "msg": "success",                         
  "data": [
    {
      "time": 1741636800,                  // 时间戳 (秒)
      "interest_rate": 0.002989            // 利率
    },
    {
      "time": 1741640400,                  // 时间戳 (秒)
      "interest_rate": 0.002989            // 利率
    }
  ]
}


---

## 牛市逃顶指数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/bull-market-peak-indicator
牛市逃顶指数
https://open-api-v4.coinglass.com
/api/bull-market-peak-indicator
该接口提供牛市逃顶指数数据
缓存 / 更新频率:
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "indicator_name": "Bitcoin Ahr999 Index",  // 指标名称
      "current_value": "0.78",                  // 当前值
      "target_value": "4",                      // 目标值
      "previous_value": "0.77",                 // 前一值
      "change_value": "0.0009811160359081",     // 变动值
      "comparison_type": ">=",                  // 比较类型
      "hit_status": false                       // 是否满足目标条件
    },
    {
      "indicator_name": "Pi Cycle Top Indicator",  // 指标名称
      "current_value": "85073.0",                  // 当前值
      "target_value": "154582",                    // 目标值
      "previous_value": "85127.0",                 // 前一值
      "change_value": "-54.0",                     // 变动值
      "comparison_type": ">=",                     // 比较类型
      "hit_status": false                          // 是否满足目标条件
    }
  ]
}


---

## 📝 更新日志

**URL:** https://docs.coinglass.com/v4.0-zh/reference/change-log
📝 更新日志
随时了解 CoinGlass API 的最新更新、优化和变更。我们持续改进服务，以提供更高的数据准确性、可靠性以及更优的开发者体验。
2025-3-13
新增接口：
Hyperliquid Whale Alert
/api/hyperliquid/whale-alert
Hyperliquid Whale Position
/api/hyperliquid/whale-position
2024-10-14
新增接口：
Futures Trade Order
/futures-trade-order
2024-10-10
新增接口：
Exchange On-chain Transfers
/exchange-onchain-transfers
2024-9-29
新增接口：
Price OHLC History
/price-ohlc-history
ETF NetAssets History
/bitcoin-etf-netassets-history
2024-9-05
新增接口：
Liquidation Order - WebSocket
Channel: liquidationOrders
2024-8-14
新增接口：
Orderbook Heatmap
/api/orderbook/history
Large Orderbook
/api/orderbook/large-limit-order
Large Orderbook History
/api/orderbook/large-limit-order-history
2024-8-2
新增接口：
Bitcoin ETF List
•/api/bitcoin/etf/list
Hong Kong ETF Flows History
•/api/bitcoin/hk/etf/flow-history
Bitcoin ETF Flows History
•/api/bitcoin/etf/flow-history
Ethereum ETF List
•/api/ethereum/etf/list
Ethereum ETF Flows History
•/api/ethereum/etf/flow-history
2024-5-11
新增接口：
Futures OrderBook Bid&Ask
• /api/futures/orderbook/history
Futures Aggregated OrderBook Bid&Ask
• /api/futures/orderbook/aggregated-history
Futures RSI List
• /api/futures/rsi/list
Spot Taker Buy/Sell History
• /api/spot/takerBuySellVolume/history
Spot Aggregated Taker Buy/Sell History
• /api/spot/aggregatedTakerBuySellVolume/history
Spot OrderBook Bid&Ask
• /api/spot/orderbook/history
Spot Aggregated OrderBook Bid&Ask
• /api/spot/orderbook/aggregated-history
Spot Coins Markets
• /api/spot/coins-markets
Spot Pairs Markets
• /api/spot/pairs-markets
Table of Contents
2025-3-13
2024-10-14
2024-10-10
2024-9-29
2024-9-05
2024-8-14
2024-8-2
2024-5-11

---

## Coinbase溢价指数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/coinbase-premium-index
Coinbase溢价指数
https://open-api-v4.coinglass.com
/api/coinbase-premium-index
该接口提供 Coinbase 比特币溢价指数，反映 Coinbase Pro 与 Binance 之间的比特币价格差异。
缓存/ 更新频率 :
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
"time": 1658880000,         // 时间戳（秒）
  "premium": 5.55,            // 溢价金额（USD）
  "premium_rate": 0.0261      // 溢价率（例如 0.0261 表示 2.61%）
    },
    {
"time": 1658880000,         // 时间戳（秒）
  "premium": 5.55,            // 溢价金额（USD）
  "premium_rate": 0.0261      // 溢价率（例如 0.0261 表示 2.61%）
    }
  ]
}


---

## 支持的币种

**URL:** https://docs.coinglass.com/v4.0-zh/reference/coins
支持的币种
https://open-api-v4.coinglass.com
/api/futures/supported-coins
该接口用于查询 CoinGlass 支持的所有合约币种。
缓存 / 更新频率:
每 1 分钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
      "BTC",
      "ETH",
      "SOL",
      "XRP",
      ...
  ]
}


---

## 币种市场列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/coins-markets
币种市场列表
https://open-api-v4.coinglass.com
/api/futures/coins-markets
该接口提供合约币种的相关指标信息。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",  
  "msg": "success",  
  "data": [
    {
      "symbol": "BTC",  // 币种符号
      "current_price": 84773.6,  // 当前价格（单位：USD）
      "avg_funding_rate_by_oi": 0.00196,  // 按持仓量加权的平均资金费率
      "avg_funding_rate_by_vol": 0.002647,  // 按成交量加权的平均资金费率
      "market_cap_usd": 1683310500117.051,  // 市值（单位：USD）
      "open_interest_market_cap_ratio": 0.0327,  // 未平仓量与市值的比值
      "open_interest_usd": 55002072334.9376,  // 未平仓量（单位：USD）
      "open_interest_quantity": 648525.0328,  // 未平仓合约数量
      "open_interest_volume_ratio": 0.7936,  // 未平仓量与成交量的比值
      "price_change_percent_5m": -0.02,  // 价格5分钟变化百分比
      "price_change_percent_15m": 0.06,  // 价格15分钟变化百分比
      "price_change_percent_30m": 0.03,  // 价格30分钟变化百分比
      "price_change_percent_1h": -0.1,  // 价格1小时变化百分比
      "price_change_percent_4h": -0.15,  // 价格4小时变化百分比
      "price_change_percent_12h": 0.15,  // 价格12小时变化百分比
      "price_change_percent_24h": 1.06,  // 价格24小时变化百分比
      "open_interest_change_percent_5m": 0,  // 未平仓量5分钟变化百分比
      "open_interest_change_percent_15m": 0.04,  // 未平仓量15分钟变化百分比
      "open_interest_change_percent_30m": 0,  // 未平仓量30分钟变化百分比
      "open_interest_change_percent_1h": -0.01,  // 未平仓量1小时变化百分比
      "open_interest_change_percent_4h": 0.17,  // 未平仓量4小时变化百分比
      "open_interest_change_percent_24h": 4.58,  // 未平仓量24小时变化百分比
      "volume_change_percent_5m": -0.03,  // 成交量5分钟变化百分比
      "volume_change_percent_15m": -0.73,  // 成交量15分钟变化百分比
      "volume_change_percent_30m": -1.13,  // 成交量30分钟变化百分比
      "volume_change_percent_1h": -2.33,  // 成交量1小时变化百分比
      "volume_change_percent_4h": -5.83,  // 成交量4小时变化百分比
      "volume_change_percent_24h": -26.38,  // 成交量24小时变化百分比
      "volume_change_usd_1h": 69310404660.3795,  // 成交量1小时变化（单位：USD）
      "volume_change_usd_4h": -4290959532.4414644,  // 成交量4小时变化（单位：USD）
      "volume_change_usd_24h": -24835757605.82467,  // 成交量24小时变化（单位：USD）
      "long_short_ratio_5m": 1.2523,  // 多空比（近5分钟）
      "long_short_ratio_15m": 0.9928,  // 多空比（近15分钟）
      "long_short_ratio_30m": 1.0695,  // 多空比（近30分钟）
      "long_short_ratio_1h": 1.0068,  // 多空比（近1小时）
      "long_short_ratio_4h": 1.0504,  // 多空比（近4小时）
      "long_short_ratio_12h": 1.0317,  // 多空比（近12小时）
      "long_short_ratio_24h": 1.0313,  // 多空比（近24小时）
      "liquidation_usd_1h": 33621.85192,  // 总爆仓金额（近1小时，单位：USD）
      "long_liquidation_usd_1h": 22178.4681,  // 多头爆仓金额（近1小时，单位：USD）
      "short_liquidation_usd_1h": 11443.38382,  // 空头爆仓金额（近1小时，单位：USD）
      "liquidation_usd_4h": 222210.47117,  // 总爆仓金额（近4小时，单位：USD）
      "long_liquidation_usd_4h": 179415.77249,  // 多头爆仓金额（近4小时，单位：USD）
      "short_liquidation_usd_4h": 42794.69868,  // 空头爆仓金额（近4小时，单位：USD）
      "liquidation_usd_12h": 11895453.392145,  // 总爆仓金额（近12小时，单位：USD）
      "long_liquidation_usd_12h": 10223351.23772,  // 多头爆仓金额（近12小时，单位：USD）
      "short_liquidation_usd_12h": 1672102.154425,  // 空头爆仓金额（近12小时，单位：USD）
      "liquidation_usd_24h": 27519292.973646,  // 总爆仓金额（近24小时，单位：USD）
      "long_liquidation_usd_24h": 17793322.595016,  // 多头爆仓金额（近24小时，单位：USD）
      "short_liquidation_usd_24h": 9725970.37863  // 空头爆仓金额（近24小时，单位：USD）
    },
    {
      "symbol": "ETH",  // 币种符号
      "current_price": 1582.55,  // 当前价格（单位：USD）
      "avg_funding_rate_by_oi": 0.001631,  // 持仓量加权平均资金费率
      "avg_funding_rate_by_vol": -0.000601,  // 成交量加权平均资金费率
      "market_cap_usd": 190821695398.62064,  // 市值（单位：USD）
      "open_interest_market_cap_ratio": 0.0925,  // 未平仓量与市值比值
      "open_interest_usd": 17657693967.0459,  // 未平仓量（单位：USD）
      "open_interest_quantity": 11160428.5065,  // 未平仓合约数量
      "open_interest_volume_ratio": 0.5398,  // 未平仓量与成交量比值
      "price_change_percent_5m": 0.07,  // 价格5分钟变化百分比
      "price_change_percent_15m": 0.25,  // 价格15分钟变化百分比
      "price_change_percent_30m": 0.07,  // 价格30分钟变化百分比
      "price_change_percent_1h": -0.11,  // 价格1小时变化百分比
      "price_change_percent_4h": -0.05,  // 价格4小时变化百分比
      "price_change_percent_12h": -0.02,  // 价格12小时变化百分比
      "price_change_percent_24h": 0.16  // 价格24小时变化百分比
      // ...后续字段依此类推
    }
  ]
}


---

## 币种涨跌幅

**URL:** https://docs.coinglass.com/v4.0-zh/reference/coins-price-change
币种涨跌幅
https://open-api-v4.coinglass.com
/api/futures/coins-price-change
该接口提供币种在多个时间周期内的涨跌幅和振幅数据。
缓存/ 更新频率 :
实时更新.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "BTC", // 币种
      "current_price": 84518.4, // 当前价格

      "price_change_percent_5m": -0.04, // 5分钟价格涨跌幅 (%)
      "price_change_percent_15m": -0.09, // 15分钟价格涨跌幅 (%)
      "price_change_percent_30m": -0.11, // 30分钟价格涨跌幅 (%)
      "price_change_percent_1h": -0.17, // 1小时价格涨跌幅 (%)
      "price_change_percent_4h": -0.54, // 4小时价格涨跌幅 (%)
      "price_change_percent_12h": -0.6, // 12小时价格涨跌幅 (%)
      "price_change_percent_24h": 0.24, // 24小时价格涨跌幅 (%)

      "price_amplitude_percent_5m": 0.07, // 5分钟价格振幅 (%)
      "price_amplitude_percent_15m": 0.16, // 15分钟价格振幅 (%)
      "price_amplitude_percent_30m": 0.18, // 30分钟价格振幅 (%)
      "price_amplitude_percent_1h": 0.26, // 1小时价格振幅 (%)
      "price_amplitude_percent_4h": 0.63, // 4小时价格振幅 (%)
      "price_amplitude_percent_12h": 1.17, // 12小时价格振幅 (%)
      "price_amplitude_percent_24h": 2.06 // 24小时价格振幅 (%)
    },
    {
      "symbol": "ETH", // 币种
      "current_price": 1573.45, // 当前价格

      "price_change_percent_5m": -0.04, // 5分钟价格涨跌幅 (%)
      "price_change_percent_15m": -0.34, // 15分钟价格涨跌幅 (%)
      "price_change_percent_30m": -0.38, // 30分钟价格涨跌幅 (%)
      "price_change_percent_1h": -0.54, // 1小时价格涨跌幅 (%)
      "price_change_percent_4h": -0.77, // 4小时价格涨跌幅 (%)
      "price_change_percent_12h": -1.99, // 12小时价格涨跌幅 (%)
      "price_change_percent_24h": -1.41, // 24小时价格涨跌幅 (%)

      "price_amplitude_percent_5m": 0.13, // 5分钟价格振幅 (%)
      "price_amplitude_percent_15m": 0.4, // 15分钟价格振幅 (%)
      "price_amplitude_percent_30m": 0.47, // 30分钟价格振幅 (%)
      "price_amplitude_percent_1h": 0.66, // 1小时价格振幅 (%)
      "price_amplitude_percent_4h": 0.9, // 4小时价格振幅 (%)
      "price_amplitude_percent_12h": 2.74, // 12小时价格振幅 (%)
      "price_amplitude_percent_24h": 3.47 // 24小时价格振幅 (%)
    }
  ]
}


---

## 恐惧与贪婪指数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/cryptofear-greedindex
恐惧与贪婪指数
https://open-api-v4.coinglass.com
/api/index/fear-greed-history
该接口提供恐惧与贪婪指数数据
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                             
  "msg": "success",                        
  "data": [
    {
      "values": [4611285.1141, ...],         // 恐惧与贪婪指数的数值
      "price": [4788636.51145, ...],         // 对应的价格数据
      "time_list": [1636588800, ...]        // 时间戳
    }
  ]
}


---

## 累计资金费率-交易所列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/cumulative-exchange-list
累计资金费率-交易所列表
https://open-api-v4.coinglass.com
/api/futures/funding-rate/accumulated-exchange-list
该接口提供各交易所的币种累计资金费率数据。
缓存 / 更新频率:
1小时一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "BTC", // 币种
      "stablecoin_margin_list": [ // USDT/USD 保证金模式的累计资金费率
        {
          "exchange": "BINANCE", // 交易所名称
          "funding_rate": 0.001873 // 累计资金费率
        },
        {
          "exchange": "OKX", // 交易所名称
          "funding_rate": 0.00775484 // 累计资金费率
        }
      ],
      "token_margin_list": [ // 币本位保证金模式的累计资金费率
        {
          "exchange": "BINANCE", // 交易所名称
          "funding_rate": -0.003149 // 累计资金费率
        }
      ]
    }
  ]
}


---

## 🔍 接口总览

**URL:** https://docs.coinglass.com/v4.0-zh/reference/endpoint-overview
🔍 接口总览
CoinGlass API 提供了丰富的接口，涵盖了广泛的加密市场数据类型，包括合约（Futures）、现货（Spot）、期权（Options）、ETF、链上数据（On-Chain）以及各类指标数据（Index）。
以下是所有可用接口的分类概览，帮助你更高效地查阅与集成相关数据：
API 接口列表
API 地址
描述
交易市场
/futures/supported-coins
获取支持的合约币种
/futures/supported-exchange-pairs
获取支持的交易所和交易对
/api/futures/pairs-markets
合约交易对详情
/api/futures/coins-markets
合约币种市场行情
/futures/price-change-list
币种价格变化列表
/api/price/ohlc-history
交易对价格 K 线历史
持仓
/api/futures/openInterest/ohlc-history
合约持仓量 K 线历史
/api/futures/openInterest/ohlc-aggregated-history
合约聚合持仓量 K 线历史
/api/futures/openInterest/ohlc-aggregated-stablecoin
聚合稳定币保证金合约持仓量 K 线
/api/futures/openInterest/ohlc-aggregated-coin-margin-history
聚合币本位保证金合约持仓量 K 线
/api/futures/openInterest/exchange-list
各交易所持仓量列表
/api/futures/openInterest/exchange-history-chart
各交易所持仓量图表历史
资金费率
/api/futures/fundingRate/ohlc-history
资金费率 K 线历史
/api/futures/fundingRate/oi-weight-ohlc-history
OI 加权资金费率 K 线
/api/futures/fundingRate/vol-weight-ohlc-history
成交量加权资金费率 K 线
/api/futures/fundingRate/exchange-list
各交易所资金费率
/api/futures/fundingRate/accumulated-exchange-list
各交易所累计资金费率
/api/futures/fundingRate/arbitrage
资金费率套利机会
多空比
/api/futures/global-long-short-account-ratio/history
全网账户多空比历史
/api/futures/top-long-short-account-ratio/history
大户账户多空比历史
/api/futures/top-long-short-position-ratio/history
大户账户持仓多空比
/api/futures/taker-buy-sell-volume/exchange-list
各交易所主动买卖比
爆仓
/api/futures/liquidation/history
合约交易对爆仓历史
/api/futures/liquidation/aggregated-history
合约币种爆仓历史
/api/futures/liquidation/coin-list
币种爆仓列表
/api/futures/liquidation/exchange-list
交易所爆仓列表
/api/futures/liquidation/order
爆仓订单
/api/futures/liquidation/heatmap/model1
合约交易对爆仓热力图（模型1）
/api/futures/liquidation/heatmap/model2
合约交易对爆仓热力图（模型2）
/api/futures/liquidation/heatmap/model3
合约交易对爆仓热力图（模型3）
/api/futures/liquidation/aggregated-heatmap/model1
合约币种爆仓热力图（模型1）
/api/futures/liquidation/aggregated-heatmap/model2
合约币种爆仓热力图（模型2）
/api/futures/liquidation/aggregated-heatmap/model3
合约币种爆仓热力图（模型3）
/api/futures/liquidation/map
合约交易对爆仓地图
/api/futures/liquidation/aggregated-map
合约币种爆仓地图
订单薄
/api/futures/orderbook/ask-bids-history
合约交易对挂单深度历史（±范围）
/api/futures/orderbook/aggregated-ask-bids-history
合约交易对挂单深度历史（±范围）
/api/futures/orderbook/history
合约订单簿热力图
/api/futures/orderbook/large-limit-order
大额挂单数据
/api/futures/orderbook/large-limit-order-history
大额挂单历史
Hyperliquid鲸鱼
/api/hyperliquid/whale-alert
Hyperliquid 巨鲸预警
/api/hyperliquid/whale-position
Hyperliquid 巨鲸仓位
主动买卖
/api/futures/taker-buy-sell-volume/history
合约交易对主动买卖成交历史
/api/futures/aggregated-taker-buy-sell-volume/history
合约币种主动买卖成交历史
现货
/api/spot/supported-coins
支持的现货币种列表
/api/spot/supported-exchange-pairs
支持的现货交易所和交易对
/api/spot/coins-markets
现货币种市场
/api/spot/pairs-markets
现货交易对市场
/api/spot/price/history
现货价格历史
订单薄
/api/spot/orderbook/ask-bids-history
现货交易对挂单深度历史（±范围）
/api/spot/orderbook/aggregated-ask-bids-history
现货币种挂单深度历史（±范围）
/api/spot/orderbook/history
现货订单簿热力图
/api/spot/orderbook/large-limit-order
现货大额挂单数据
/api/spot/orderbook/large-limit-order-history
现货大额挂单历史
主动买卖
/api/spot/taker-buy-sell-volume/history
现货交易对主动买卖成交历史
/api/spot/aggregated-taker-buy-sell-volume/history
现货币种主动买卖成交历史
期权
/api/option/max-pain
最大痛点价数据
/api/option/info
期权信息
/api/option/exchange-oi-history
各交易所期权持仓量历史
/api/option/exchange-vol-history
各交易所期权成交量历史
链上
/api/exchange/assets
交易所资产透明度概况
/api/exchange/balance/list
交易所余额列表
/api/exchange/balance/chart
交易所余额变化图表
/api/exchange/chain/tx/list
交易所链上转账记录（ERC-20）
ETF
/api/etf/bitcoin/list
比特币 ETF 列表
/api/hk-etf/bitcoin/flow-history
香港比特币 ETF 流入流出历史
/api/etf/bitcoin/net-assets/history
比特币 ETF 净资产历史
/api/etf/bitcoin/flow-history
比特币 ETF 流入流出历史
/api/etf/bitcoin/premium-discount/history
比特币 ETF 溢价/折价历史
/api/etf/bitcoin/history
比特币 ETF 历史
/api/etf/bitcoin/price/history
比特币 ETF 价格历史
/api/etf/bitcoin/detail
比特币 ETF 详情
/api/etf/ethereum/net-assets-history
以太坊 ETF 净资产历史
/api/etf/ethereum/list
以太坊 ETF 列表
/api/etf/ethereum/flow-history
以太坊 ETF 流入流出历史
/api/grayscale/holdings-list
灰度持仓列表
/api/grayscale/premium-history
灰度溢价历史
指标
/api/futures/rsi/list
RSI 相对强弱指标列表
/api/futures/basis/history
合约基差历史
/api/borrow-interest-rate/history
借贷利率历史
/api/coinbase-premium-index
Coinbase 溢价指数
/api/bitfinex-margin-long-short
Bitfinex 杠杆多空比
/api/index/ahr999
AHR999 指标
/api/index/puell-multiple
Puell 多重指标
/api/index/stock-flow
Stock-to-Flow 模型
/api/index/pi-cycle-indicator
Pi Cycle 顶部指标
/api/index/golden-ratio-multiplier
黄金比例乘数
/api/index/bitcoin/profitable-days
比特币盈利天数
/api/index/bitcoin/rainbow-chart
比特币彩虹图
/api/index/fear-greed-history
加密恐惧与贪婪指数
/api/index/stableCoin-marketCap-history
稳定币市值历史
/api/index/bitcoin/bubble-index
比特币泡沫指数
/api/bull-market-peak-indicator
牛市顶部指标
/api/index/2-year-ma-multiplier
2 年均线乘数
/api/index/200-week-moving-average-heatmap
200 周移动均线热力图
Table of Contents
API 接口列表

---

## 比特币ETF列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/etf
比特币ETF列表
https://open-api-v4.coinglass.com
/api/etf/bitcoin/list
该接口提供比特币交易型基金（ETF）的关键信息列表
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "ticker": "GBTC",                                // ETF标识符
      "fund_name": "Grayscale Bitcoin Trust ETF",      // 基金名称
      "region": "us",                                  // 所属地区
      "market_status": "early_trading",                // 市场状态 (open/closed/early_trading等)
      "primary_exchange": "ARCX",                      // 主交易所
      "cik_code": "0001588489",                        // CIK代码 (唯一标识符)
      "fund_type": "Spot",                             // 基金类型 (Spot/ETF/Futures等)
      "list_date": 1424822400000,                      // 上市日期 (时间戳，单位毫秒)
      "shares_outstanding": "240750100",               // 流通股数
      "aum_usd": "16137543152.34",                     // 管理资产总值 (USD)
      "management_fee_percent": "1.5",                 // 管理费率 (%)
      "last_trade_time": 1745225312958,                // 最新成交时间 (时间戳，单位毫秒)
      "last_quote_time": 1745225389483,                // 最新计价时间 (时间戳，单位毫秒)
      "volume_quantity": 1068092,                      // 成交量
      "volume_usd": 71485902.2312,                     // 成交量 (USD)
      "price_change_usd": 0.47,                        // 价格变化 (USD)
      "price_change_percent": 0.71,                    // 价格变化 (%)
      "asset_details": {
        "net_asset_value_usd": 67.03,                  // 净值 (USD)
        "premium_discount_percent": 0.09,              // 溢价/折价率 (%)
        "btc_holding": 190124.5441,                    // BTC余额
        "btc_change_percent_24h": 0,                   // 24小时BTC变化 (%)
        "btc_change_24h": -7.8136,                     // 24小时BTC变化量
        "btc_change_percent_7d": -0.32,                // 7天BTC变化 (%)
        "btc_change_7d": -615.563,                     // 7天BTC变化量
        "update_date": "2025-04-17"                    // 更新时间
      },
      "update_timestamp": 1745224505000                // 数据更新时间 (时间戳，单位毫秒)
    }
  ]
}


---

## 比特币ETF详情

**URL:** https://docs.coinglass.com/v4.0-zh/reference/etf-detail
比特币ETF详情
https://open-api-v4.coinglass.com
/api/etf/bitcoin/detail
该接口提供某只比特币交易所交易基金（ETF）的详细信息
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "ticker_info": {
      "id": 1,                                         // ETF ID
      "ticker": "GBTC",                                // ETF 代码
      "name": "Grayscale Bitcoin Trust ETF",           // ETF 名称
      "market": "stocks",                              // 市场类型
      "region": "us",                                  // 地区
      "primary_exchange": "ARCX",                      // 主要交易所
      "fund_type": "ETV",                              // 产品类型
      "active": "true",                                // 是否活跃
      "currency_name": "usd",                          // 货币名称
      "cik_code": "0001588489",                        // CIK 编号
      "composite_figi": "BBG008748J88",                // 综合 FIGI
      "share_class_figi": "BBG008748J97",              // 股份类 FIGI
      "phone_number": "************",                  // 联系电话
      "tag": "BTC",                                    // 资产标签
      "type2": "Spot",                                 // 产品类型2
      "address": {
        "address_1": "{\"address2\":\"4TH FLOOR\",\"city\":\"STAMFORD\",\"address1\":\"290 HARBOR DRIVE\",\"state\":\"CT\",\"postal_code\":\"06902\"}"
      },                                               // 公司地址（JSON 字符串，需解析）
      "sic_code": "6221",                              // SIC 编码
      "sic_description": "COMMODITY CONTRACTS BROKERS & DEALERS", // 行业描述
      "ticker_root": "GBTC",                           // Ticker Root
      "list_date": 1424822400000,                      // 上市日期（时间戳，毫秒）
      "share_class_shares_outstanding": "240750100",   // 流通份额
      "round_lot": "100",                              // 每手份额
      "status": 1,                                     // 状态
      "update_time": 1745224505000                     // 更新时间（时间戳，毫秒）
    },
    "market_status": "early_trading",                  // 市场状态
    "name": "Grayscale Bitcoin Trust ETF",             // ETF 名称
    "ticker": "GBTC",                                  // ETF 代码
    "type": "stocks",                                  // 市场类型
    "session": {
      "change": 2.22,                                  // 涨跌
      "change_percent": 3.309,                         // 涨跌幅（%）
      "early_trading_change": 2.22,                    // 盘前涨跌
      "early_trading_change_percent": 3.309,           // 盘前涨跌幅（%）
      "close": 67.09,                                  // 收盘价
      "high": 67.56,                                   // 最高价
      "low": 66.15,                                    // 最低价
      "open": 66.86,                                   // 开盘价
      "volume": 1068092,                               // 成交量
      "previous_close": 67.09,                         // 前收盘价
      "price": 69.31                                   // 最新价
    },
    "last_quote": {
      "last_updated": 1745226801708029700,             // 更新时间（纳秒级时间戳）
      "timeframe": "REAL-TIME",                        // 时间类型（如实时）
      "ask": 69.29,                                    // 卖价
      "ask_size": 34,                                  // 卖量
      "ask_exchange": 8,                               // 卖方交易所代码
      "bid": 69.18,                                    // 买价
      "bid_size": 3,                                   // 买量
      "bid_exchange": 11                               // 买方交易所代码
    },
    "last_trade": {
      "last_updated": 1745226730467043600,             // 更新时间（纳秒级时间戳）
      "timeframe": "REAL-TIME",                        // 时间类型（如实时）
      "id": "62879131651684",                          // 交易ID
      "price": 69.31,                                  // 成交价
      "size": 30,                                      // 成交量
      "exchange": 12,                                  // 交易所代码
      "conditions": [12, 37]                           // 交易条件数组
    },
    "performance": {
      "low_price_52week": 39.56,                       // 52周最低价
      "high_price_52week": 86.11,                      // 52周最高价
      "high_price_52week_date": 1734238800000,         // 52周最高价日期（时间戳）
      "low_price_52week_date": 1722744000000,          // 52周最低价日期（时间戳）
      "ydt_change_percent": -12.23,                    // 年初至今涨跌幅（%）
      "year_change_percent": 13.98,                    // 年涨跌幅（%）
      "avg_vol_usd_10d": 518227                        // 过去10日平均交易金额（美元）
    }
  }
}


---

## ETF流入流出历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/etf-flows-history
ETF流入流出历史
https://open-api-v4.coinglass.com
/api/etf/bitcoin/flow-history
该接口提供比特币ETF的历史资金流入流出数据，包括每日的净流入和净流出（以美元计）、收盘价格，以及按ETF代码划分的资金流明细。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "timestamp": 1704931200000,                   // 日期（时间戳，单位毫秒）
      "flow_usd": 655300000,                         // 当日总资金流入（USD）
      "price_usd": 46663,                            // 当日BTC现价（USD）
      "etf_flows": [                                 // 各ETF的资金流入详情
        {
          "etf_ticker": "GBTC",                      // ETF代码
          "flow_usd": -95100000                      // 资金流出（USD）
        },
        {
          "etf_ticker": "IBIT",                      // ETF代码
          "flow_usd": 111700000                      // 资金流入（USD）
        },
        {
          "etf_ticker": "FBTC",                      // ETF代码
          "flow_usd": 227000000                      // 资金流入（USD）
        }
      ]
    }
  ]
}


---

## 比特币ETF历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/etf-history
比特币ETF历史
https://open-api-v4.coinglass.com
/api/etf/bitcoin/history
该接口提供比特币ETF的历史数据，涵盖每个ETF代码的关键指标信息，包括市场价格、净资产价值（NAV）、溢价/折扣百分比、流通股数以及总净资产等。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                
  "msg": "success",                           
  "data": [
    {
      "assets_date": 1706486400000,           // 净资产对应日期（时间戳，毫秒）
      "btc_holdings": 496573.8166,            // BTC 持仓数量
      "market_date": 1706486400000,           // 市场价格对应日期（时间戳，毫秒）
      "market_price": 38.51,                  // 市场价格（USD）
      "name": "Grayscale Bitcoin Trust",      // ETF 名称
      "nav": 38.57,                           // 单位净值（Net Asset Value，USD）
      "net_assets": 21431132778.35,           // 总净资产（USD）
      "premium_discount": -0.16,              // 溢价/折扣百分比（负值为折扣）
      "shares_outstanding": 555700100,        // 已发行份额
      "ticker": "GBTC"                        // ETF 代码
    }
  ]
}


---

## ETF价格历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/etf-price-ohlc-history
ETF价格历史
https://open-api-v4.coinglass.com
/api/etf/bitcoin/price/history
该接口提供比特币ETF的历史价格数据，包括每个数据点的开盘价、最高价、最低价、收盘价（OHLC）以及交易量信息。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                         
  "msg": "success",                   
  "data": [
    {
      "time": 1731056460000,     // 时间戳（毫秒）
      "open": 60.47,                  // 开盘价
      "high": 60.47,                  // 最高价
      "low": 60.47,                   // 最低价
      "close": 60.47,                 // 收盘价
      "volume": 100                   // 成交量
    },
    ...
  ]
}


---

## 以太坊ETF流入流出历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ethereum-etf-flows-history
以太坊ETF流入流出历史
https://open-api-v4.coinglass.com
/api/etf/ethereum/flow-history
该接口提供以太坊交易所交易基金（ETF）的流入流出历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "timestamp": 1721692800000,            // 时间戳
      "change_usd": 106600000,              // 资金流入/流出（USD）
      "price": 3438.09,                     // 当前价格
      "close_price": 3481.01,               // 收盘价
      "etf_flows": [                        // ETF 流动性列表
        {
          "ticker": "ETHA",             // ETF 代码
          "change_usd": 266500000       // 资金流动（USD）
        },
        {
          "ticker": "FETH",             // ETF 代码
          "change_usd": 71300000        // 资金流动（USD）
        }
      ]
    }
  ]
}


---

## 以太坊ETF列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ethereum-etf-list
以太坊ETF列表
https://open-api-v4.coinglass.com
/api/etf/ethereum/list
该接口提供以太坊交易所交易基金（ETF）的关键状态信息列表。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "ticker": "ETHA",                                  // ETF 代码
      "name": "iShares Ethereum Trust ETF",              // ETF 名称
      "region": "us",                                    // 地区
      "market_status": "closed",                         // 市场状态
      "primary_exchange": "XNAS",                        // 主要交易所
      "cik_code": "0002000638",                          // CIK 代码
      "type": "Spot",                                    // 类型
      "market_cap": "544896000.00",                      // 市值
      "list_date": 1721692800000,                        // 上市日期
      "shares_outstanding": "28800000",                  // 已发行份额
      "aum": "",                                         // 管理资产总值
      "management_fee_percent": "0.25",                  // 管理费
      "last_trade_time": 1722988779939,                  // 最新交易时间
      "last_quote_time": 1722988799379,                  // 最新计价时间
      "volume_quantity": 5592645,                        // 交易量
      "volume_usd": 106447049.343,                       // 美元交易量
      "price": 18.92,                                    // 市场价格
      "price_change": 0.67,                              // 价格变化
      "price_change_percent": 3.67,                      // 价格变化百分比
      "asset_info": {
        "nav": 18.11,                                  // 净值
        "premium_discount": 0.77,                      // 溢价/折扣
        "holding_quantity": 237882.8821,                 // 持仓数量
        "change_percent_1d": 0,                        // 1天变化百分比
        "change_quantity_1d": 0,                         // 1天变化值
        "change_percent_7d": 56.69,                    // 7天变化百分比
        "change_quantity_7d": 86060.9115,                // 7天变化值
        "date": "2024-08-05"                           // 数据日期
      },
      "update_time": 1722995656637                       // 更新时间
    }
  ]
}


---

## 以太坊ETF净资产历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ethereum-etf-netassets-history
以太坊ETF净资产历史
https://open-api-v4.coinglass.com
/api/etf/ethereum/net-assets/history
该接口提供以太坊为基础的交易所交易基金（ETF）的历史净资产数据。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "net_assets_usd": 51671409241.39,         // 净资产总额（USD）
      "change_usd": 655300000,                  // 当日资金变化（USD）
      "timestamp": 1704931200000,               // 日期（时间戳，单位毫秒）
      "price_usd": 1637.8                      // 当日ETH价格（USD）
    },
    {
      "net_assets_usd": 51874409241.39,         // 净资产总额（USD）
      "change_usd": 203000000,                  // 当日资金变化（USD）
      "timestamp": 1705017600000,               // 日期（时间戳，单位毫秒）
      "price_usd": 1637.8                      // 当日ETH价格（USD）
    }
  ]
}


---

## 交易所资产透明度

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-assets
交易所资产透明度
https://open-api-v4.coinglass.com
/api/exchange/assets
此接口提供交易所钱包的资产持有数据，包括钱包地址、资产余额、美元估值以及每种资产的实时价格信息。
缓存/ 更新频率 :
1小时一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "wallet_address": "**********************************",
      "balance": 248597.54,
      "balance_usd": 21757721869.92,
      "symbol": "BTC",
      "assets_name": "Bitcoin",
      "price": 87521.87117346626
    },
    {
      "wallet_address": "**********************************",
      "balance": 139456.08,
      "balance_usd": 12205457068.12,
      "symbol": "BTC",
      "assets_name": "Bitcoin",
      "price": 87521.87117346626
    },


---

## 交易所余额图表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-balance-chart
交易所余额图表
https://open-api-v4.coinglass.com
/api/exchange/balance/chart
该接口提供各交易所资产余额随时间变化的历史图表数据，并附带相应的币种价格数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                 
  "msg": "success",                            
  "data": [
    {
      "time_list": [1691460000000, ...],       // 时间戳数组（毫秒）
      "price_list": [29140.9, ...],            // 对应时间点的价格数组
      "data_map": {                            // 各交易所的余额数据
        "huobi": [15167.03527, ...],           // Huobi 交易所的余额数据
        "gate": [23412.723, ...],              // Gate 交易所的余额数据
        ...
      }
    }
  ]
}


---

## 交易所余额列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-balance-list
交易所余额列表
https://open-api-v4.coinglass.com
/api/exchange/balance/list
该接口提供各交易所的资产余额数据，包括 1 天、7 天和 30 天内的余额变化百分比信息。
缓存/ 更新频率 :
1小时一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                 
  "msg": "success",                            
  "data": [
    {
      "exchange_name": "Coinbase",               // 交易所名称
      "total_balance": 716590.351233,            // 总余额
      "balance_change_1d": 638.797302,           // 24小时余额变动
      "balance_change_percent_1d": 0.09,         // 24小时余额变动百分比 (%)
      "balance_change_7d": 799.967408,           // 7天余额变动
      "balance_change_percent_7d": 0.11,         // 7天余额变动百分比 (%)
      "balance_change_30d": -29121.977486,       // 30天余额变动
      "balance_change_percent_30d": -3.91        // 30天余额变动百分比 (%)
    },
    {
      "exchange_name": "Binance",                // 交易所名称
      "total_balance": 582344.497738,            // 总余额
      "balance_change_1d": 505.682778,           // 24小时余额变动
      "balance_change_percent_1d": 0.09,         // 24小时余额变动百分比 (%)
      "balance_change_7d": -3784.88544,          // 7天余额变动
      "balance_change_percent_7d": -0.65,        // 7天余额变动百分比 (%)
      "balance_change_30d": 3753.870055,         // 30天余额变动
      "balance_change_percent_30d": 0.65         // 30天余额变动百分比 (%)
    }
  ]
}


---

## 交易所链上转账（ERC-20）

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-onchain-transfers
交易所链上转账（ERC-20）
https://open-api-v4.coinglass.com
/api/exchange/chain/tx/list
该接口提供基于 ERC-20 协议的交易所链上转账数据。
缓存/ 更新频率 :
实时更新.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "transaction_hash": "0xb8d08182d2de176ac42dceba9ff82a7a5fe650c1e56285d6810daac9561ff9dc", // 交易哈希
      "asset_symbol": "USDT",                       // 资产符号
      "amount_usd": 9998.5,                         // 美元金额
      "asset_quantity": 9998.5,                     // 数量
      "exchange_name": "Coinbase",                 // 交易所名称
      "transfer_type": 1,                           // 转账类型：1: 转入, 2: 转出, 3: 内部转账
      "from_address": "0x16c6897438c4f0c7894862d884549e8564c4025f", // 转出地址
      "to_address": "0xa9d1e08c7793af67e9d92fe308d5697fb81d3e43",   // 转入地址
      "transaction_time": 1745224211                // 交易时间（时间戳，单位秒）
    },
    {
      "transaction_hash": "0x033c56cb05654f2c360235eff99c84f0eee9c6330fc7012930adfe0a88789c0f", // 交易哈希
      "asset_symbol": "MANA",                       // 资产符号
      "amount_usd": 6368.95196834,                  // 美元金额
      "asset_quantity": 20091.33113044,             // 数量
      "exchange_name": "Binance",                  // 交易所名称
      "transfer_type": 1,                           // 转账类型：1: 转入, 2: 转出, 3: 内部转账
      "from_address": "0x06fd4ba7973a0d39a91734bbc35bc2bcaa99e3b0", // 转出地址
      "to_address": "0x28c6c06298d514db089934071355e5743bf21d60",   // 转入地址
      "transaction_time": 1745224211                // 交易时间（时间戳，单位秒）
    }
  ]
}


---

## 交易所持仓历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-open-interest-history
交易所持仓历史
https://open-api-v4.coinglass.com
/api/option/exchange-oi-history
该接口提供不同交易所期权的历史持仓量数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                 
  "msg": "success",                            
  "data": [
    {
      "time_list": [1691460000000, ...],       // 时间戳数组（毫秒）
      "price_list": [29140.9, ...],            // 对应时间点的价格数组
      "data_map": {                            // 各交易所的未平仓合约数据
        "huobi": [15167.03527, ...],           // Huobi 交易所的未平仓合约  数据
        "gate": [23412.723, ...],              // Gate 交易所的 未平仓合约 数据
        ...
      }
    }
  ]
}


---

## 交易所成交量历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/exchange-volume-history
交易所成交量历史
https://open-api-v4.coinglass.com
/api/option/exchange-vol-history
该接口提供各大交易所期权的历史成交量数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                                 
  "msg": "success",                            
  "data": [
    {
      "time_list": [1691460000000, ...],       // 时间戳数组（毫秒）
      "price_list": [29140.9, ...],            // 对应时间点的价格数组
      "data_map": {                            // 各交易所的成交额数据
        "huobi": [15167.03527, ...],           // Huobi 交易所的 成交额数据
        "gate": [23412.723, ...],              // Gate 交易所的成交额数据
        ...
      }
    }
  ]
}


---

## 资金费率套利

**URL:** https://docs.coinglass.com/v4.0-zh/reference/fr-arbitrage
资金费率套利
https://open-api-v4.coinglass.com
/api/futures/funding-rate/arbitrage
该接口提供不同交易所间的合约币种资金费率套利数据。
缓存/ 更新频率 :
20秒一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "SUPRA", // 币种名称

      "buy": { // 做多的交易所信息（低资金费率）
        "exchange": "MEXC", // 交易所名称
        "open_interest_usd": 848218.2833, // 平台未平仓合约金额（美元）
        "funding_rate_interval": 4, // 资金费率结算间隔（小时）
        "funding_rate": -0.994 // 当前资金费率（%）
      },

      "sell": { // 做空的交易所信息（高资金费率）
        "exchange": "Gate.io", // 交易所名称
        "open_interest_usd": 448263.5072, // 平台未平仓合约金额（美元）
        "funding_rate_interval": 4, // 资金费率结算间隔（小时）
        "funding_rate": 0.005 // 当前资金费率（%）
      },

      "apr": 2187.81, // 年化收益率（APR，%）
      "funding": 0.999, // 资金费率收益差（long和short之间的资金费率差）
      "fee": 0.03, // 交易手续费率（双边总和）
      "spread": -0.09, // 跨平台价格差（%）
      "next_funding_time": 1745222400000 // 下一次资金费率结算时间（时间戳，毫秒）
    }
  ]
}


---

## 币种资金费率-交易所列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/fr-exchange-list
币种资金费率-交易所列表
https://open-api-v4.coinglass.com
/api/futures/funding-rate/exchange-list
该接口提供各交易所的币种资金费率数据。
缓存 / 更新频率:
20秒一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "BTC", // 币种
      "stablecoin_margin_list": [ // USDT/USD 保证金模式
        {
          "exchange": "Binance", // 交易所
          "funding_rate_interval": 8, // 资金费率结算周期（小时）
          "funding_rate": 0.007343, // 当前资金费率
          "next_funding_time": 1745222400000 // 下次资金费率结算时间（毫秒）
        },
        {
          "exchange": "OKX", // 交易所
          "funding_rate_interval": 8, // 资金费率结算周期（小时）
          "funding_rate": 0.00736901950628, // 当前资金费率
          "next_funding_time": 1745222400000 // 下次资金费率结算时间（毫秒）
        }
      ],
      "token_margin_list": [ // 币本位保证金模式
        {
          "exchange": "Binance", // 交易所
          "funding_rate_interval": 8, // 资金费率结算周期（小时）
          "funding_rate": -0.001829, // 当前资金费率
          "next_funding_time": 1745222400000 // 下次资金费率结算时间（毫秒）
        }
      ]
    }
  ]
}


---

## 资金费率历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/fr-ohlc-histroy
资金费率历史(K线)
https://open-api-v4.coinglass.com
/api/futures/funding-rate/history
该接口提供合约交易对的资金费率 K 线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1658880000000, // 时间戳（毫秒）
      "open": "0.004603",     // 开盘资金费率
      "high": "0.009388",     // 最高资金费率
      "low": "-0.005063",     // 最低资金费率
      "close": "0.009229"     // 收盘资金费率
    },
    {
      "time": 1658966400000, // 时间戳（毫秒）
      "open": "0.009229",     // 开盘资金费率
      "high": "0.01",         // 最高资金费率
      "low": "0.007794",      // 最低资金费率
      "close": "0.01"         // 收盘资金费率
    }
  ]
}


---

## 币种聚合挂单深度历史(±范围)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/futures-aggregated-orderbook-history
币种聚合挂单深度历史(±范围)
https://open-api-v4.coinglass.com
/api/futures/orderbook/aggregated-ask-bids-history
该接口提供合约币种聚合历史订单簿数据，包含指定价格范围内的买盘和卖盘挂单总量。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "aggregated_bids_usd": 12679537.0806,         // 聚合多单金额（美元）
      "aggregated_bids_quantity": 197.99861,        // 聚合多单数量
      "aggregated_asks_usd": 10985519.9268,         // 聚合空单金额（美元）
      "aggregated_asks_quantity": 170.382,          // 聚合空单数量
      "time": 1714003200000                         // 时间戳（毫秒）
    },
    {
      "aggregated_bids_usd": 18423845.1947,
      "aggregated_bids_quantity": 265.483,
      "aggregated_asks_usd": 17384271.5521,
      "aggregated_asks_quantity": 240.785,
      "time": 1714089600000
    }
  ]
}


---

## 交易对挂单深度历史(±范围)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/futures-orderbook-history
交易对挂单深度历史(±范围)
https://open-api-v4.coinglass.com
/api/futures/orderbook/ask-bids-history
该接口提供合约交易对的历史订单簿数据，包含指定价格范围内的买盘和卖盘挂单总量。
缓存 / 更新频率:
5秒一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "bids_usd": 81639959.9338,        // 多单总金额（美元）
      "bids_quantity": 1276.645,        // 多单总数量
      "asks_usd": 78533053.6862,        // 空单总金额（美元）
      "asks_quantity": 1217.125,        // 空单总数量
      "time": 1714003200000             // 时间戳（毫秒）
    },
    {
      "bids_usd": 62345879.8821,
      "bids_quantity": 980.473,
      "asks_usd": 65918423.4715,
      "asks_quantity": 1021.644,
      "time": 1714089600000
    }
  ]
}


---

## 🔰 介绍

**URL:** https://docs.coinglass.com/v4.0-zh/reference/getting-started-with-your-api
🔰 介绍
CoinGlass API 是一套强大且全面的 API 系统，旨在聚合来自主流加密货币交易所的实时与历史数据，涵盖合约、现货、期权、ETF 及链上等多个市场。
CoinGlass 最初因其强大的合约数据聚合能力而闻名，如今已扩展支持更多关键市场指标，包括现货订单簿、大户持仓、ETF 净流入流出、链上储备和宏观情绪指数等。
作为专业级数据平台，CoinGlass API 支持数千个交易对和数百种数字资产，覆盖多种市场类型，深受量化交易员、机构、数据分析师与开发者的信赖，用于研究、策略制定与监控系统搭建。
✅ 核心亮点：
聚合主流合约与现货交易所数据，包括 Binance、OKX、Bybit、Coinbase 等
覆盖 2000+ 加密衍生品与全球期权产品
提供细粒度的历史与实时数据，涵盖资金费率、持仓量、价格 K 线、仓位数据、大户行为、多空比与爆仓流向
深度洞察 ETF 流向、链上储备、ERC20 转账，以及 Crypto 恐慌贪婪指数、S2F 模型与彩虹图等宏观指标
面向开发者友好的 RESTful 接口，支持分页、筛选与可扩展的数据结构
CoinGlass API 帮助用户高效监控市场趋势，识别策略机会，精准快速地进行加密市场量化分析。
接口需要 API Key，请参阅
此页面
.
1.
注册 CoinGlass 账号
2.
登录
3.
查看您的 API 密钥
API 接口地址:
<https://open-api-v4.coinglass.com>

---

## 比特币黄金比例乘数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/golden-ratio-multiplier
比特币黄金比例乘数
https://open-api-v4.coinglass.com
/api/index/golden-ratio-multiplier
该接口提供比特币黄金比例乘数（Golden Ratio Multiplier）的相关数据
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "low_bull_high_2": 0.14,                       // 牛市低高位比例系数 / Bull market low-high ratio coefficient
      "timestamp": 1282003200000,                    // 时间戳（毫秒）/ Timestamp (milliseconds)
      "price": 0.07,                                 // 当前价格 / Current price
      "ma_350": 0.07,                                // 350日移动平均 / 350-day moving average
      "accumulation_high_1_6": 0.11200000000000002,  // 累积高位比例（1/6倍黄金比例）/ Accumulation high ratio (1/6 golden ratio)
      "x_3": 0.21000000000000002,                    // 黄金比例倍数 x3 / Golden ratio multiple x3
      "x_5": 0.35000000000000003,                    // 黄金比例倍数 x5 / Golden ratio multiple x5
      "x_8": 0.56,                                   // 黄金比例倍数 x8 / Golden ratio multiple x8
      "x_13": 0.9100000000000001,                    // 黄金比例倍数 x13 / Golden ratio multiple x13
      "x_21": 1.4700000000000002                     // 黄金比例倍数 x21 / Golden ratio multiple x21
    },

  ]
}


---

## 香港ETF流入流出历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/hong-kong-bitcoin-etf-flow-history
香港ETF流入流出历史
https://open-api-v4.coinglass.com
/api/hk-etf/bitcoin/flow-history
该接口提供香港市场中比特币ETF的资金流入流出历史数据。
缓存/ 更新频率 :
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "timestamp": 1714435200000,                     // 日期（时间戳，单位毫秒）
      "flow_usd": 247866000,                          // 总资金流入（USD）
      "price_usd": 63842.4,                           // 当日BTC价格（USD）
      "etf_flows": [                                  // ETF资金流明细
        {
          "etf_ticker": "CHINAAMC",                   // ETF代码
          "flow_usd": 123610690                       // 该ETF资金流入（USD）
        },
        {
          "etf_ticker": "HARVEST",                    // ETF代码
          "flow_usd": 63138000                        // 该ETF资金流入（USD）
        },
        {
          "etf_ticker": "BOSERA&HASHKEY",             // ETF代码
          "flow_usd": 61117310                        // 该ETF资金流入（USD）
        }
      ]
    },
    {
      "timestamp": 1714608000000,                     // 日期（时间戳，单位毫秒）
      "flow_usd": 10095060,                           // 总资金流入（USD）
      "price_usd": 58307.7,                           // 当日BTC价格（USD）
      "etf_flows": [
        {
          "etf_ticker": "CHINAAMC",                   // ETF代码
          "flow_usd": 3053490                         // 该ETF资金流入（USD）
        },
        {
          "etf_ticker": "HARVEST",                    // ETF代码
          "flow_usd": 5022000                         // 该ETF资金流入（USD）
        },
        {
          "etf_ticker": "BOSERA&HASHKEY",             // ETF代码
          "flow_usd": 2019570                         // 该ETF资金流入（USD）
        }
      ]
    }
  ]
}


---

## Hyperliquid 鲸鱼提醒

**URL:** https://docs.coinglass.com/v4.0-zh/reference/hyperliquid-whale-alert
Hyperliquid 鲸鱼提醒
https://open-api-v4.coinglass.com
/api/hyperliquid/whale-alert
该接口提供 Hyperliquid 平台上实时的鲸鱼交易提醒，突出展示价值超过 100 万美元的大额持仓变动。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "user": "******************************************", // 用户地址
      "symbol": "ETH",                                     // 币种
      "position_size": 12700,                              // 仓位大小（正数为多，负数为空）
      "entry_price": 1611.62,                              // 开仓价格
      "liq_price": 527.2521,                               // 强平价格
      "position_value_usd": 21003260,                      // 仓位价值（美元）
      "position_action": 2,                                // 仓位操作类型（1: 开仓，2: 平仓）
      "create_time": 1745219517000                         // 开仓时间（时间戳，毫秒）
    },
    {
      "user": "******************************************",
      "symbol": "BTC",
      "position_size": 33.54032,
      "entry_price": 87486.2,
      "liq_price": 44836.8126,
      "position_value_usd": 2936421.4757,
      "position_action": 2,
      "create_time": 1745219477000
    }
  ]
}


---

## Hyperliquid 鲸鱼持仓

**URL:** https://docs.coinglass.com/v4.0-zh/reference/hyperliquid-whale-position
Hyperliquid 鲸鱼持仓
https://open-api-v4.coinglass.com
/api/hyperliquid/whale-position
该接口提供 Hyperliquid 平台上鲸鱼的持仓数据，筛选出价值超过 100 万美元的持仓信息。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "user": "******************************************", // 用户地址
      "symbol": "ETH",                                   // 币种
      "position_size": -44727.1273,                      // 仓位大小（正为做多，负为做空）
      "entry_price": 2249.7,                             // 开仓价格
      "mark_price": 1645.8,                              // 当前标记价格
      "liq_price": 2358.2766,                            // 强平价格
      "leverage": 25,                                    // 杠杆倍数
      "margin_balance": 2943581.7019,                    // 保证金余额（USD）
      "position_value_usd": 73589542.5467,               // 仓位价值（USD）
      "unrealized_pnl": 27033236.424,                    // 未实现盈亏（USD）
      "funding_fee": -3107520.7373,                      // 资金费用（USD）
      "margin_mode": "cross",                            // 保证金模式（cross 表示全仓）
      "create_time": 1741680802000,                      // 开仓时间（时间戳，毫秒）
      "update_time": 1745219966000                       // 更新时间（时间戳，毫秒）
    },
    {
      "user": "******************************************",
      "symbol": "BTC",
      "position_size": -800,
      "entry_price": 84931.3,
      "mark_price": 87427,
      "liq_price": 92263.798,
      "leverage": 15,
      "margin_balance": 4812076.3896,
      "position_value_usd": 69921600,
      "unrealized_pnl": -1976493.6819,
      "funding_fee": 14390.0346,
      "margin_mode": "isolated",                         // 保证金模式（isolated 表示逐仓）
      "create_time": 1743982804000,
      "update_time": 1745219969000
    }
  ]
}


---

## 期权相关数据

**URL:** https://docs.coinglass.com/v4.0-zh/reference/info
期权相关数据
https://open-api-v4.coinglass.com
/api/option/info
该接口提供不同交易所期权的持仓量与成交量等详细信息。
缓存/ 更新频率 :
30秒更新一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "exchange_name": "All",                           // 交易所名称
      "open_interest": 361038.78,                       // 未平仓合约数量（张）
      "oi_market_share": 100,                           // 持仓占有率（%）
      "open_interest_change_24h": 2.72,                 // 24 小时未平仓合约变化（%）
      "open_interest_usd": 31623069708.138245,          // 未平仓合约价值（美元）
      "volume_usd_24h": 2764676957.0569425,             // 24 小时交易量（美元）
      "volume_change_percent_24h": 303.1                // 24 小时交易量变化（%）
    },
    {
      "exchange_name": "Deribit",                       // 交易所名称
      "open_interest": 262641.9,                        // 未平仓合约数量（张）
      "oi_market_share": 72.74,                         // 持仓占有率（%）
      "open_interest_change_24h": 2.57,                 // 24 小时未平仓合约变化（%）
      "open_interest_usd": 23005403973.349,             // 未平仓合约价值（美元）
      "volume_usd_24h": 2080336672.709                  // 24 小时交易量（美元）
    }
  ]
}


---

## 支持的交易所和交易对

**URL:** https://docs.coinglass.com/v4.0-zh/reference/instruments
支持的交易所和交易对
https://open-api-v4.coinglass.com
/api/futures/supported-exchange-pairs
该接口允许你查询 CoinGlass 上支持的所有合约交易所及其对应的交易对。
缓存 / 更新频率:
每 1 分钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "Binance": [ // 交易所名称
      {
        "instrument_id": "BTCUSD_PERP",// 合约交易对
        "base_asset": "BTC",// 基础币种
        "quote_asset": "USD"// 计价币种
      },
      {
        "instrument_id": "BTCUSD_250627",
        "base_asset": "BTC",
        "quote_asset": "USD"
      },
      ....
      ],
    "Bitget": [
      {
        "instrument_id": "BTCUSDT_UMCBL",
        "base_asset": "BTC",
        "quote_asset": "USDT"
      },
      {
        "instrument_id": "ETHUSDT_UMCBL",
        "base_asset": "ETH",
        "quote_asset": "USDT"
      },
      ...
      ]
      ...
   }
}


---

## 大额订单薄

**URL:** https://docs.coinglass.com/v4.0-zh/reference/large-limit-order-1
大额订单薄
https://open-api-v4.coinglass.com
/api/spot/orderbook/large-limit-order
该接口提供现货交易中当前订单簿中的大额限价挂单数据。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
"code": "0",
  "msg": "success",
  "data":[
  {
    "id": 2536823422,
    "exchange_name": "Binance",            // 交易所名称
    "symbol": "BTCUSDT",                   // 交易对
    "base_asset": "BTC",                   // 基础币种
    "quote_asset": "USDT",                 // 计价币种

    "limit_price": 56932,                  // 委托价格
    "start_time": 1722964242000,           // 委托开始时间（毫秒）
    "start_quantity": 28.39774,            // 初始数量
    "start_usd_value": 1616740.1337,       // 初始价值（USD）

    "current_quantity": 18.77405,          // 当前剩余数量
    "current_usd_value": 1068844.21,       // 当前剩余价值（USD）
    "current_time": 1722964272000,         // 当前时间（毫秒）

    "executed_volume": 0,                  // 成交数量
    "executed_usd_value": 0,               // 成交价值（USD）
    "trade_count": 0,                      // 成交笔数

    "order_side": 2,                       // 委托方向：1-卖单，2-买单
    "order_state": 1                       // 委托状态：1-挂单中，2-已完成，3-已撤销
  }
]
}


---

## 大额订单薄

**URL:** https://docs.coinglass.com/v4.0-zh/reference/large-orderbook
大额订单薄
https://open-api-v4.coinglass.com
/api/futures/orderbook/large-limit-order
该接口提供合约交易中当前订单簿中的大额限价挂单数据。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
"code": "0",
  "msg": "success",
  "data":[
  {
    "id": 2912620961,
    "exchange_name": "Binance",            // 交易所名称
    "symbol": "BTCUSDT",                   // 交易对
    "base_asset": "BTC",                   // 基础币种
    "quote_asset": "USDT",                 // 计价币种

    "limit_price": 56932,                  // 委托价格
    "start_time": 1722964242000,           // 委托开始时间（毫秒）
    "start_quantity": 28.39774,            // 初始数量
    "start_usd_value": 1616740.1337,       // 初始价值（USD）

    "current_quantity": 18.77405,          // 当前剩余数量
    "current_usd_value": 1068844.21,       // 当前剩余价值（USD）
    "current_time": 1722964272000,         // 当前时间（毫秒）

    "executed_volume": 0,                  // 成交数量
    "executed_usd_value": 0,               // 成交价值（USD）
    "trade_count": 0,                      // 成交笔数

    "order_side": 2,                       // 委托方向：1-卖单，2-买单
    "order_state": 1                       // 委托状态：1-挂单中，2-已完成，3-已撤销
  }
]
}


---

## 大额订单薄历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/large-orderbook-history
大额订单薄历史
https://open-api-v4.coinglass.com
/api/futures/orderbook/large-limit-order-history
该接口提供合约交易中已完成的大额限价挂单的历史数据。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "id": 2895605135,
      "exchange_name": "Binance",               // 交易所名称
      "symbol": "BTCUSDT",                      // 交易对
      "base_asset": "BTC",                      // 基础币种
      "quote_asset": "USDT",                    // 计价币种
      "price": 89205.9,                         // 委托价格
      "start_time": 1745287309000,              // 委托开始时间 (毫秒)
      "start_quantity": 25.779,                 // 初始委托数量
      "start_usd_value": 2299638.8961,          // 初始委托价值 (USD)
      "current_quantity": 25.779,               // 当前剩余数量
      "current_usd_value": 2299638.8961,        // 当前剩余价值 (USD)
      "current_time": 1745287309000,            // 当前时间 (毫秒)
      "executed_volume": 0,                     // 累计成交量
      "executed_usd_value": 0,                  // 累计成交价值 (USD)
      "trade_count": 0,                         // 累计成交笔数
      "order_side": 1,                          // 委托方向：1-卖单，2-买单
      "order_state": 2,                         // 委托状态：0-未开始，1-挂单中，2-已完成，3-已撤销
      "order_end_time": 1745287328000           // 完成或撤销订单的结束时间 (毫秒)
    },
    ...
  ]
}


---

## 大额订单薄历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/large-orderbook-history-1
大额订单薄历史
https://open-api-v4.coinglass.com
/api/spot/orderbook/large-limit-order-history
该接口提供现货交易中已完成的大额限价挂单的历史数据。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "id": 2895605135,
      "exchange_name": "Binance",               // 交易所名称
      "symbol": "BTCUSDT",                      // 交易对
      "base_asset": "BTC",                      // 基础币种
      "quote_asset": "USDT",                    // 计价币种
      "price": 89205.9,                         // 委托价格
      "start_time": 1745287309000,              // 委托开始时间 (毫秒)
      "start_quantity": 25.779,                 // 初始委托数量
      "start_usd_value": 2299638.8961,          // 初始委托价值 (USD)
      "current_quantity": 25.779,               // 当前剩余数量
      "current_usd_value": 2299638.8961,        // 当前剩余价值 (USD)
      "current_time": 1745287309000,            // 当前时间 (毫秒)
      "executed_volume": 0,                     // 累计成交量
      "executed_usd_value": 0,                  // 累计成交价值 (USD)
      "trade_count": 0,                         // 累计成交笔数
      "order_side": 1,                          // 委托方向：1-卖单，2-买单
      "order_state": 2,                         // 委托状态：0-未开始，1-挂单中，2-已完成，3-已撤销
      "order_end_time": 1745287328000           // 完成或撤销订单的结束时间 (毫秒)
    },
    ...
  ]
}


---

## 币种爆仓热力图(模型1)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-aggregate-heatmap
币种爆仓热力图(模型1)
https://open-api-v4.coinglass.com
/api/futures/liquidation/aggregated-heatmap/model1
该接口提供币种的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 币种爆仓热力图(模型2)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-aggregate-heatmap-model2
币种爆仓热力图(模型2)
https://open-api-v4.coinglass.com
/api/futures/liquidation/aggregated-heatmap/model2
该接口提供币种的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 币种爆仓热力图(模型2)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-aggregated-heatmap-model3
币种爆仓热力图(模型2)
https://open-api-v4.coinglass.com
/api/futures/liquidation/aggregated-heatmap/model3
该接口提供币种的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 币种清算地图

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-aggregated-map
币种清算地图
https://open-api-v4.coinglass.com
/api/futures/liquidation/aggregated-map
该接口提供币种清算地图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": 
  {
    "data": 
    {
     {
    "48935": [
        [
            48935, // 清算价格
            1579370.77, // 清算强度
            25 // 杠杆倍数
        ]
    ],
      ...  
    }
  }
}


---

## 交易所币种爆仓列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-coin-list
交易所币种爆仓列表
https://open-api-v4.coinglass.com
/api/futures/liquidation/coin-list
该接口提供指定交易所内所有币种的爆仓数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "BTC", // 币种符号
      "liquidation_usd_24h": 82280481.50425325, // 过去24小时总爆仓金额（美元）
      "long_liquidation_usd_24h": 68437447.33734027, // 过去24小时多单爆仓金额（美元）
      "short_liquidation_usd_24h": 13843034.16691298, // 过去24小时空单爆仓金额（美元）

      "liquidation_usd_12h": 68331844.36224127, // 过去12小时总爆仓金额
      "long_liquidation_usd_12h": 66614158.47451427, // 过去12小时多单爆仓金额
      "short_liquidation_usd_12h": 1717685.887727, // 过去12小时空单爆仓金额

      "liquidation_usd_4h": 11381137.080643, // 过去4小时总爆仓金额
      "long_liquidation_usd_4h": 10921633.272973, // 过去4小时多单爆仓金额
      "short_liquidation_usd_4h": 459503.80767, // 过去4小时空单爆仓金额

      "liquidation_usd_1h": 3283635.95309, // 过去1小时总爆仓金额
      "long_liquidation_usd_1h": 3182915.16289, // 过去1小时多单爆仓金额
      "short_liquidation_usd_1h": 100720.7902 // 过去1小时空单爆仓金额
    },
    {
      "symbol": "ETH", // 币种
      "liquidation_usd_24h": 40690629.51728708,
      "long_liquidation_usd_24h": 23696395.11024007,
      "short_liquidation_usd_24h": 16994234.40704701,
      "liquidation_usd_12h": 24938270.19977256,
      "long_liquidation_usd_12h": 22675785.96343007,
      "short_liquidation_usd_12h": 2262484.23634249,
      "liquidation_usd_4h": 8034894.33790068,
      "long_liquidation_usd_4h": 7683841.24631068,
      "short_liquidation_usd_4h": 351053.09159,
      "liquidation_usd_1h": 6103879.82428372,
      "long_liquidation_usd_1h": 6098481.11604372,
      "short_liquidation_usd_1h": 5398.70824
    }
  ]
}


---

## 币种交易所爆仓列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-exchange-list
币种交易所爆仓列表
https://open-api-v4.coinglass.com
/api/futures/liquidation/exchange-list
该接口提供指定币种在各大交易所的爆仓数据列表。
缓存 / 更新频率:
10秒钟一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "exchange": "All", // 所有交易所的总数据
      "liquidation_usd": 14673519.81739075, // 总爆仓金额（美元）
      "long_liquidation_usd": 451394.17404598, // 多单爆仓金额（美元）
      "short_liquidation_usd": 14222125.64334477 // 空单爆仓金额（美元）
    },
    {
      "exchange": "Bybit", // 交易所名称
      "liquidation_usd": 4585290.13404, // 总爆仓金额（美元）
      "long_liquidation_usd": 104560.13885, // 多单爆仓金额（美元）
      "short_liquidation_usd": 4480729.99519 // 空单爆仓金额（美元）
    }
  ]
}


---

## 交易对爆仓热力图(模型1)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-heatmap
交易对爆仓热力图(模型1)
https://open-api-v4.coinglass.com
/api/futures/liquidation/heatmap/model1
该接口提供交易对的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 交易对爆仓热力图(模型2)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-heatmap-model2
交易对爆仓热力图(模型2)
https://open-api-v4.coinglass.com
/api/futures/liquidation/heatmap/model2
该接口提供交易对的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 交易对爆仓热力图(模型3)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-heatmap-model3
交易对爆仓热力图(模型3)
https://open-api-v4.coinglass.com
/api/futures/liquidation/heatmap/model3
该接口提供交易对的爆仓热力图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "y_axis": [47968.54, 48000.00, 48031.46], // Y轴坐标，价格范围
    "liquidation_leverage_data": [
      [5, 124, 2288867.26], // 每个数组表示：[X轴索引, Y轴索引, 该位置的爆仓金额（美元）]
      [6, 123, 318624.82],
      [7, 122, 1527940.12]
    ],
    "price_candlesticks": [
      [
        1722676500, // 时间戳（秒）
        "61486",    // 开盘价
        "61596.4",  // 最高价
        "61434.4",  // 最低价
        "61539.9",  // 收盘价
        "63753192.1129" // 成交量（美元）
      ],
      [
        1722676800,
        "61539.9",
        "61610.0",
        "61480.0",
        "61590.5",
        "42311820.8720"
      ]
    ]
  }
}


---

## 交易对爆仓历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-history
交易对爆仓历史
https://open-api-v4.coinglass.com
/api/futures/liquidation/history
该接口提供交易所中交易对的多空爆仓历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1658880000000, // 时间戳（毫秒）
      "long_liquidation_usd": "2369935.19562", // 多单爆仓金额（美元）
      "short_liquidation_usd": "6947459.43674" // 空单爆仓金额（美元）
    },
    {
      "time": 1658966400000, // 时间戳（毫秒）
      "long_liquidation_usd": "5118407.85124", // 多单爆仓金额（美元）
      "short_liquidation_usd": "8517330.44192" // 空单爆仓金额（美元）
    }
  ]
}


---

## 交易对清算地图

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-map
交易对清算地图
https://open-api-v4.coinglass.com
/api/futures/liquidation/map
该接口提供交易对清算地图数据
缓存 / 更新频率:
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
❌
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": 
  {
    "data": 
    {
     {
    "48935": [
        [
            48935, // 清算价格
            1579370.77, // 清算强度
            25 // 杠杆倍数
        ]
    ],
      ...  
    }
  }
}


---

## 爆仓订单

**URL:** https://docs.coinglass.com/v4.0-zh/reference/liquidation-order
爆仓订单
https://open-api-v4.coinglass.com
/api/futures/liquidation/order
该接口提供过去7天内的爆仓订单数据，包含交易所、交易对及爆仓金额等详细信息。
缓存 / 更新频率:
1秒钟一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "exchange_name": "BINANCE", // 交易所名称
      "symbol": "BTCUSDT", // 交易对符号
      "base_asset": "BTC", // 币种
      "price": 87535.9, // 爆仓价格
      "usd_value": 205534.2932, // 成交金额（美元）
      "side": 2, // 订单方向（1：买入，2：卖出）
      "time": 1745216319263 // 时间戳
    },
    {
      "exchange_name": "BINANCE", // 交易所名称
      "symbol": "BTCUSDT", // 交易对符号
      "base_asset": "BTC", // 币种
      "price": 87465.2, // 爆仓价格
      "usd_value": 15918.6664, // 成交金额（美元）
      "side": 2, // 订单方向（1：买入，2：卖出）
      "time": ************* // 时间戳
    }
  ]
}


---

## 交易对多空比历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/long-short-ratio
交易对多空比历史
https://open-api-v4.coinglass.com
/api/futures/global-long-short-account-ratio/history
该接口提供交易所交易对的多空账户比历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": *************, // 时间戳（毫秒）
      "global_account_long_percent": 73.88, // 账户多单比例（%）
      "global_account_short_percent": 26.12, // 账户空单比例（%）
      "global_account_long_short_ratio": 2.83 // 账户多空比（多/空）
    },
    {
      "time": *************, // 时间戳（毫秒）
      "global_account_long_percent": 73.24, // 账户多单比例（%）
      "global_account_short_percent": 26.76, // 账户空单比例（%）
      "global_account_long_short_ratio": 2.74 // 账户多空比（多/空）
    }
  ]
}


---

## 币种交易所持仓历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-exchange-history-chart
币种交易所持仓历史
https://open-api-v4.coinglass.com
/api/futures/open-interest/exchange-history-chart
该接口用于获取某一加密货币在多个交易所的历史持仓量数据，适用于图表展示。
缓存 / 更新频率:
10秒钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "time_list": [*************, ...], // 时间戳列表（毫秒）
    "price_list": [67490.3, ...], // 价格列表（与 time_list 对应）

    "data_map": { // 各交易所未平仓合约数据
      "Binance": [8018229234, ...], // Binance 未平仓合约（与 time_list 对应）
      "Bitmex": [395160842, ...] // BitMEX 未平仓合约（与 time_list 对应）
      // ...
    }
  }
}


---

## 币种交易所持仓

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-exchange-list
币种交易所持仓
https://open-api-v4.coinglass.com
/api/futures/open-interest/exchange-list
该接口提供某个币种在多个交易所的持仓量（Open Interest）数据
缓存 / 更新频率:
10秒钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "exchange": "All", // 交易所名称，"All"表示所有交易所汇总
      "symbol": "BTC", // 币种符号

      "open_interest_usd": 57437891724.5572, // 未平仓合约价值(USD)，表示所有未平仓合约的总价值
      "open_interest_quantity": 659557.3064, // 未平仓合约数量，表示所有未平仓合约的总数量

      "open_interest_by_stable_coin_margin": 48920274435.15, // 稳定币本位未平仓合约价值(USD)，合约采用稳定币作为保证金
      "open_interest_quantity_by_coin_margin": 97551.2547, // 币本位未平仓合约数量
      "open_interest_quantity_by_stable_coin_margin": 562006.0517, // 稳定币本位未平仓合约数量

      "open_interest_change_percent_5m": 0.34, // 5分钟内未平仓合约变化百分比
      "open_interest_change_percent_15m": 0.59, // 15分钟内未平仓合约变化百分比
      "open_interest_change_percent_30m": 1.42, // 30分钟内未平仓合约变化百分比
      "open_interest_change_percent_1h": 2.27, // 1小时内未平仓合约变化百分比
      "open_interest_change_percent_4h": 2.95, // 4小时内未平仓合约变化百分比
      "open_interest_change_percent_24h": 0.9 // 24小时内未平仓合约变化百分比
    },
    {
      "exchange": "CME", // 交易所名称
      "symbol": "BTC", // 币种符号

      "open_interest_usd": 12294999402.5, // 未平仓合约价值(USD)，表示所有未平仓合约的总价值
      "open_interest_quantity": 141275.5, // 未平仓合约数量，表示所有未平仓合约的总数量

      "open_interest_by_stable_coin_margin": 12294999402.5, // 稳定币本位未平仓合约价值(USD)，合约采用稳定币作为保证金
      "open_interest_quantity_by_coin_margin": 0, // 币本位未平仓合约数量
      "open_interest_quantity_by_stable_coin_margin": 141275.5, // 稳定币本位未平仓合约数量

      "open_interest_change_percent_5m": 0.08, // 5分钟内未平仓合约变化百分比
      "open_interest_change_percent_15m": 0.14, // 15分钟内未平仓合约变化百分比
      "open_interest_change_percent_30m": 0.49, // 30分钟内未平仓合约变化百分比
      "open_interest_change_percent_1h": 1.13, // 1小时内未平仓合约变化百分比
      "open_interest_change_percent_4h": 2.4, // 4小时内未平仓合约变化百分比
      "open_interest_change_percent_24h": 2.08 // 24小时内未平仓合约变化百分比
    }
  ]
}


---

## 聚合币本位保证金持仓(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-ohlc-aggregated-coin-margin-history
聚合币本位保证金持仓(K线)
https://open-api-v4.coinglass.com
/api/futures/open-interest/aggregated-coin-margin-history
该接口提供聚合多个交易所的币本位保证金持仓量（Open Interest）K线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 2644845344000, // 时间戳（毫秒）
      "open": "2644845344",   // 开始时的未平仓合约
      "high": "2692643311",   // 最高未平仓合约
      "low": "2576975597",    // 最低未平仓合约
      "close": "2608846475"   // 结束时的未平仓合约
    },
    {
      "time": 2608846475000, // 时间戳（毫秒）
      "open": "2608846475",  // 开始时的未平仓合约
      "high": "2620807645",  // 最高未平仓合约
      "low": "2327236202",   // 最低未平仓合约
      "close": "2340177420"  // 结束时的未平仓合约
    },
    ....
  ]
}


---

## 聚合持仓历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-ohlc-aggregated-history
聚合持仓历史(K线)
https://open-api-v4.coinglass.com
/api/futures/open-interest/aggregated-history
该接口提供聚合多个交易所的币种持仓量（Open Interest） K线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 2644845344000, // 时间戳（毫秒）
      "open": "2644845344",   // 开始时的未平仓合约
      "high": "2692643311",   // 最高未平仓合约
      "low": "2576975597",    // 最低未平仓合约
      "close": "2608846475"   // 结束时的未平仓合约
    },
    {
      "time": 2608846475000, // 时间戳（毫秒）
      "open": "2608846475",  // 开始时的未平仓合约
      "high": "2620807645",  // 最高未平仓合约
      "low": "2327236202",   // 最低未平仓合约
      "close": "2340177420"  // 结束时的未平仓合约
    },
    ....
  ]
}


---

## 聚合稳定币保证金持仓(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-ohlc-aggregated-stablecoin-margin-history
聚合稳定币保证金持仓(K线)
https://open-api-v4.coinglass.com
/api/futures/open-interest/aggregated-stablecoin-history
该接口用于返回聚合多个交易所的稳定币保证金持仓量（Open Interest）K 线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 2644845344000, // 时间戳（毫秒）
      "open": "2644845344",   // 开始时的未平仓合约
      "high": "2692643311",   // 最高未平仓合约
      "low": "2576975597",    // 最低未平仓合约
      "close": "2608846475"   // 结束时的未平仓合约
    },
    {
      "time": 2608846475000, // 时间戳（毫秒）
      "open": "2608846475",  // 开始时的未平仓合约
      "high": "2620807645",  // 最高未平仓合约
      "low": "2327236202",   // 最低未平仓合约
      "close": "2340177420"  // 结束时的未平仓合约
    },
    ....
  ]
}


---

## 持仓历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-ohlc-histroy
持仓历史(K线)
https://open-api-v4.coinglass.com
/api/futures/open-interest/history
该接口提供合约交易对的持仓量（Open Interest）K线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 2644845344000, // 时间戳（毫秒）
      "open": "2644845344",   // 开盘持仓量
      "high": "2692643311",   // 最高持仓量
      "low": "2576975597",    // 最低持仓量
      "close": "2608846475"   // 收盘持仓量
    },
    {
      "time": 2608846475000, // 时间戳（毫秒）
      "open": "2608846475",  // 开盘持仓量
      "high": "2620807645",  // 最高持仓量
      "low": "2327236202",   // 最低持仓量
      "close": "2340177420"  // 收盘持仓量
    },
    ....
  ]
}


---

## 持仓加权资金费率历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/oi-weight-ohlc-history
持仓加权资金费率历史(K线)
https://open-api-v4.coinglass.com
/api/futures/funding-rate/oi-weight-history
该接口提供按持仓量加权的合约币种资金费率 K 线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1658880000000, // 时间戳（毫秒）
      "open": "0.004603",     // 开盘资金费率
      "high": "0.009388",     // 最高资金费率
      "low": "-0.005063",     // 最低资金费率
      "close": "0.009229"     // 收盘资金费率
    },
    {
      "time": 1658966400000, // 时间戳（毫秒）
      "open": "0.009229",     // 开盘资金费率
      "high": "0.01",         // 最高资金费率
      "low": "0.007794",      // 最低资金费率
      "close": "0.01"         // 收盘资金费率
    }
  ]
}


---

## API 3.0版本

**URL:** https://docs.coinglass.com/v4.0-zh/reference/old-version-30
API 3.0版本
API 3.0版本 :
https://docs.coinglass.com/reference/coins#/versions

---

## 期权最大痛点

**URL:** https://docs.coinglass.com/v4.0-zh/reference/option-max-pain
期权最大痛点
https://open-api-v4.coinglass.com
/api/option/max-pain
该接口提供期权的最大痛点相关数据。
缓存/ 更新频率 :
1分钟一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "date": "250422",                                   // 日期（YYMMDD 格式）
      "call_open_interest_market_value": 1616749.22,      // 看涨期权市场价值（美元）
      "put_open_interest": 512.5,                         // 看跌期权未平仓合约数量（张）
      "put_open_interest_market_value": 49687.62,         // 看跌期权市场价值（美元）
      "max_pain_price": "84000",                          // 最大痛苦点价格
      "call_open_interest": 953.7,                        // 看涨期权未平仓合约数量（张）
      "call_open_interest_notional": 83519113.56,         // 看涨期权名义价值（美元）
      "put_open_interest_notional": 44881569.13           // 看跌期权名义价值（美元）
    },
    {
      "date": "250423",                                 
      "call_open_interest_market_value": 2274700.52,     ）
      "put_open_interest": 1204.3,                       
      "put_open_interest_market_value": 374536.01,       
      "max_pain_price": "85000",                         
      "call_open_interest": 1302.2,                      
      "call_open_interest_notional": 114040373.53,       
      "put_open_interest_notional": 105465691.73          
    }
  ]
}


---

## 订单薄历史(热力图)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/orderbook-heatmap
订单薄历史(热力图)
https://open-api-v4.coinglass.com
/api/futures/orderbook/history
该接口提供合约交易的历史挂单深度数据，支持以热力图形式可视化展示。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
    "code": "0",
    "msg": "success",
    "data": [
        [
            1723611600,
            [
                [
                    56420, //价格
                    4.777 //数量
                ],
                [
                    40300,
                    2.191
                ]
            ],
            [
                [
                    56420,
                    4.777
                ],
                [
                    40300,
                    2.191
                ]
            ]
        ]
    ],
    "success": true
}


---

## 订单薄历史(热力图)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/orderbook-history-1
订单薄历史(热力图)
https://open-api-v4.coinglass.com
/api/spot/orderbook/history
该接口提供现货交易的历史挂单深度数据，支持以热力图形式可视化展示。
缓存 / 更新频率:
实时更新
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
    "code": "0",
    "msg": "success",
    "data": [
        [
            1723611600,
            [
                [
                    56420, //价格
                    4.777 //数量
                ],
                [
                    40300,
                    2.191
                ]
            ],
            [
                [
                    56420,
                    4.777
                ],
                [
                    40300,
                    2.191
                ]
            ]
        ]
    ],
    "success": true
}


---

## 交易对市场列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/pairs-markets
交易对市场列表
https://open-api-v4.coinglass.com
/api/futures/pairs-markets
该接口提供合约交易对的相关指标信息。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "instrument_id": "BTCUSDT", // 合约交易对
      "exchange_name": "Binance", // 交易所名称
      "symbol": "BTC/USDT", // 币种对

      "current_price": 84604.3, // 当前价格
      "index_price": 84646.66222222, // 指数价格
      "price_change_percent_24h": 0.67, // 24小时价格变化(%)

      "volume_usd": 11317580109.5041, // 24小时交易量(USD)
      "volume_usd_change_percent_24h": -32.13, // 24小时交易量变化(%)

      "long_volume_usd": 5800829746.047, // 多单成交价值(USD)
      "short_volume_usd": 5516750363.4571, // 空单成交价值(USD)
      "long_volume_quantity": 1130850, // 多单成交笔数
      "short_volume_quantity": 1162710, // 空单成交笔数

      "open_interest_quantity": 77881.234, // 未平仓合约数量
      "open_interest_usd": 6589095073.8296, // 未平仓合约价值(USD)
      "open_interest_change_percent_24h": 1.9, // 24小时未平仓合约变化(%)

      "long_liquidation_usd_24h": 3654182.12, // 近24小时多单爆仓金额(USD)
      "short_liquidation_usd_24h": 4099047.79, // 近24小时空单爆仓金额(USD)

      "funding_rate": 0.002007, // 当前资金费率
      "next_funding_time": 1744963200000, // 下一次资金费率时间戳

      "open_interest_volume_radio": 0.5822, // 未平仓合约与交易量比率
      "oi_vol_ratio_change_percent_24h": 50.13 // 24小时比率变化(%)
    },
    {
      "instrument_id": "BTC_USDT", // 合约交易对
      "exchange_name": "Gate.io", // 交易所名称
      "symbol": "BTC/USDT", // 币种对

      "current_price": 84616.3, // 当前价格
      "index_price": 84643.36, // 指数价格
      "price_change_percent_24h": 0.69, // 24小时价格变化(%)

      "volume_usd": 1711484049.255, // 24小时交易量(USD)
      "volume_usd_change_percent_24h": -67.03, // 24小时交易量变化(%)

      "long_volume_usd": 870432407.5966, // 多单成交价值(USD)
      "short_volume_usd": 841051641.6584, // 空单成交价值(USD)
      "long_volume_quantity": 210027, // 多单成交笔数
      "short_volume_quantity": 218777, // 空单成交笔数

      "open_interest_quantity": 69477.278, // 未平仓合约数量
      "open_interest_usd": 5878785139.331, // 未平仓合约价值(USD)
      "open_interest_change_percent_24h": 3.82, // 24小时未平仓合约变化(%)

      "long_liquidation_usd_24h": 1502896.68, // 近24小时多单爆仓金额(USD)
      "short_liquidation_usd_24h": 1037959.7, // 近24小时空单爆仓金额(USD)

      "funding_rate": 0.0022, // 当前资金费率

      "open_interest_volume_radio": 3.4349, // 未平仓合约与交易量比率
      "oi_vol_ratio_change_percent_24h": 214.93 // 24小时比率变化(%)
    }
  ]
}


---

## Pi循环顶部指示器

**URL:** https://docs.coinglass.com/v4.0-zh/reference/pi
Pi循环顶部指示器
https://open-api-v4.coinglass.com
/api/index/pi-cycle-indicator
该接口提供 Pi Cycle Top 指标的相关数据，包括每个时间点的比特币价格、111 日移动平均线（ma110）、以及 350 日移动平均线乘以 2 的值（ma350Mu2）
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                           
  "msg": "success",                       
  "data": [
    {
      "ma_110": 0.07,                      // 110日移动平均价格
      "timestamp": 1282003200000,         // 时间戳（毫秒）
      "ma_350_mu_2": 0.14,                // 350日移动平均的2倍值
      "price": 0.07                        // 当日价格
    },
    {
      "ma_110": 0.069,                    // 110日移动平均价格
      "timestamp": 1282089600000,         // 时间戳（毫秒）
      "ma_350_mu_2": 0.138,               // 350日移动平均的2倍值
      "price": 0.068                      // 当日价格
    }
  ]
}


---

## 交易对价格历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/price-history-1
交易对价格历史(K线)
https://open-api-v4.coinglass.com
/api/spot/price/history
该接口提供加密货币在指定时间周期内的历史开盘价、最高价、最低价和收盘价（OHLC）数据。
缓存 / 更新频率:
实时更新.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741690800000,
      "open": 81808.25,//开盘价
      "high": 82092.34,//最高价
      "low": 81400,//最低价
      "close": 81720.34,//关盘价
      "volume_usd": 96823535.5724
    },
    {
      "time": 1741694400000,
      "open": 81720.33,
      "high": 81909.69,
      "low": 81017,
      "close": 81225.5,
      "volume_usd": 150660424.1863
    },


---

## 交易对K线历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/price-ohlc-history
交易对K线历史
https://open-api-v4.coinglass.com
/api/futures/price/history
该接口提供加密货币在指定时间周期内的历史开盘价、最高价、最低价和收盘价（OHLC）数据。
缓存 / 更新频率:
实时更新.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "data": [
    {
      "time": 1745366400000,
      "open": "93404.9",
      "high": "93864.9",
      "low": "92730",
      "close": "92858.2",
      "volume_usd": "1166471854.3026"
    },
    {
      "time": 1745370000000,
      "open": "92858.2",
      "high": "93464.8",
      "low": "92552",
      "close": "92603.8",
      "volume_usd": "871812560.3437"
    },
    ...
 ]
}


---

## 普尔倍数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/puell-multiple
普尔倍数
https://open-api-v4.coinglass.com
/api/index/puell-multiple
该接口提供 Puell Multiple 指标相关数据，包括在特定时间点的比特币价格、买入/卖出数量，以及对应的普尔倍数数值。
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                              
  "msg": "success",                        
  "data": [
    {
      "timestamp": 1282003200000,           // 时间戳（毫秒）
      "price": 0.07,                         // 当日价格
      "puell_multiple": 1                   // Puell Multiple 指标数值
    },
    {
      "timestamp": 1282089600000,           // 时间戳（毫秒）
      "price": 0.068,                        // 当日价格
      "puell_multiple": 1.0007745933384973  // Puell Multiple 指标数值
    }
  ]
}


---

## ❗ 错误码与频率限制

**URL:** https://docs.coinglass.com/v4.0-zh/reference/responses-error-codes
❗ 错误码与频率限制
📡
API 返回状态码
CoinGlass API 使用标准的 HTTP 状态码来指示请求的成功或失败。以下表格简要说明了常见的返回码含义:
状态码
说明
0
请求成功
400
错误的请求（参数有误等）
401
未授权（API Key 无效或缺失）
404
接口不存在
405
不支持的 HTTP 方法
408
请求超时（请求处理时间过长）
422
参数格式正确但内容不可接受
429
请求过于频繁（触发限流）
500
服务器内部错误
🚦
速率限制（Rate Limits）
请求频率限制将根据你所订阅的付费套餐而有所不同。不同等级的套餐对应不同的调用频率上限。
你可以前往
此页面
查看详细说明与限制条款。

---

## 币种聚合挂单深度历史(±范围)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-aggregated-history
币种聚合挂单深度历史(±范围)
https://open-api-v4.coinglass.com
/api/spot/orderbook/aggregated-ask-bids-history
该接口提供现货币种聚合历史订单簿数据，包含指定价格范围内的买盘和卖盘挂单总量。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "aggregated_bids_usd": 12679537.0806,         // 聚合多单金额（美元）
      "aggregated_bids_quantity": 197.99861,        // 聚合多单数量
      "aggregated_asks_usd": 10985519.9268,         // 聚合空单金额（美元）
      "aggregated_asks_quantity": 170.382,          // 聚合空单数量
      "time": 1714003200000                         // 时间戳（毫秒）
    },
    {
      "aggregated_bids_usd": 18423845.1947,
      "aggregated_bids_quantity": 265.483,
      "aggregated_asks_usd": 17384271.5521,
      "aggregated_asks_quantity": 240.785,
      "time": 1714089600000
    }
  ]
}


---

## 币种主动买卖历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-aggregated-taker-buysell-history
币种主动买卖历史
https://open-api-v4.coinglass.com
/api/spot/aggregated-taker-buy-sell-volume/history
该接口提供现货市场中币种的主动买入与卖出成交量的历史数据
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741622400000, // 时间戳（毫秒）
      "aggregated_buy_volume_usd": 968834542.3787, // 聚合买单成交量（美元）
      "aggregated_sell_volume_usd": 1054582654.8138 // 聚合卖单成交量（美元）
    },
    {
      "time": 1741626000000,
      "aggregated_buy_volume_usd": 1430620763.2041,
      "aggregated_sell_volume_usd": 1559166911.2821
    },
    {
      "time": 1741629600000,
      "aggregated_buy_volume_usd": 1897261721.0129,
      "aggregated_sell_volume_usd": 2003812276.7812
    }
  ]
}


---

## 币种市场列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-coins-markets
币种市场列表
https://open-api-v4.coinglass.com
/api/spot/coins-markets
该接口提供现货币种的相关指标信息。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
响应数据
JSON
{
  "code": "0", 
  "msg": "success", 
  "data": [
    {
      "symbol": "BTC", // 币种代号，如 BTC 表示比特币
      "current_price": 87500, // 当前币价（美元）

      "market_cap": 1735007745495.3037, // 当前市值（美元）

      // 各周期内价格变化（单位：美元）
      "price_change_5m": 101.5, // 最近5分钟价格变化
      "price_change_15m": 46.18, // 最近15分钟价格变化
      "price_change_30m": -77.22, // 最近30分钟价格变化
      "price_change_1h": 12.56, // 最近1小时价格变化
      "price_change_4h": 147.75, // 最近4小时价格变化
      "price_change_12h": 147.75, // 最近12小时价格变化
      "price_change_24h": 2799.99, // 最近24小时价格变化
      "price_change_1w": 2989.69, // 最近1周价格变化

      // 各周期内价格变化百分比（单位：%）
      "price_change_percent_5m": 0.12, // 最近5分钟价格涨跌幅
      "price_change_percent_15m": 0.05, // 最近15分钟价格涨跌幅
      "price_change_percent_30m": -0.09, // 最近30分钟价格涨跌幅
      "price_change_percent_1h": 0.01, // 最近1小时价格涨跌幅
      "price_change_percent_4h": 0.17, // 最近4小时价格涨跌幅
      "price_change_percent_12h": 0.17, // 最近12小时价格涨跌幅
      "price_change_percent_24h": 3.31, // 最近24小时价格涨跌幅
      "price_change_percent_1w": 3.54, // 最近1周价格涨跌幅

      // 各周期内成交量（单位：美元）
      "volume_usd_1h": 129491564.6994, // 最近1小时成交总量
      "volume_usd_5m": 11056683.8336, // 最近5分钟成交总量
      "volume_usd_15m": 50625331.2542, // 最近15分钟成交总量
      "volume_usd_30m": 80070296.0794, // 最近30分钟成交总量
      "volume_usd_4h": 580775143.5162, // 最近4小时成交总量
      "volume_usd_12h": 2663308247.353, // 最近12小时成交总量
      "volume_usd_24h": 3719093876.3834, // 最近24小时成交总量
      "volume_usd_1w": 16801739001.0272, // 最近1周成交总量

      // 各周期内成交量变化值（单位：美元，与上一个周期比较）
      "volume_change_usd_1h": -35317032.6336, // 成交量变化：最近1小时与前一小时相比
      "volume_change_usd_5m": -110911976.2243,
      "volume_change_usd_15m": -59017725.7105,
      "volume_change_usd_30m": -39864985.2519,
      "volume_change_usd_4h": -1084757627.3629,
      "volume_change_usd_12h": 1624238611.116,
      "volume_change_usd_24h": 1967797576.5416,
      "volume_change_usd_1w": -23396586539.5365,

      // 各周期内成交量变化百分比（单位：%）
      "volume_change_percent_1h": -21.43, // 成交量变化百分比：1小时
      "volume_change_percent_5m": -90.93,
      "volume_change_percent_15m": -53.83,
      "volume_change_percent_30m": -33.24,
      "volume_change_percent_4h": -65.13,
      "volume_change_percent_12h": 156.32,
      "volume_change_percent_24h": 112.36,
      "volume_change_percent_1w": 3.54,

      // 各周期内主动买入成交量（单位：美元）
      "buy_volume_usd_1h": 55687151.2164,
      "buy_volume_usd_5m": 4634327.6087,
      "buy_volume_usd_15m": 20222399.059,
      "buy_volume_usd_30m": 33140975.4441,
      "buy_volume_usd_4h": 278174843.6339,
      "buy_volume_usd_12h": 1410854413.4688,
      "buy_volume_usd_24h": 1923920264.0666,
      "buy_volume_usd_1w": 8116673333.4846,

      // 各周期内主动卖出成交量（单位：美元）
      "sell_volume_usd_1h": 73804413.4829,
      "sell_volume_usd_5m": 6422356.2248,
      "sell_volume_usd_15m": 30402932.1951,
      "sell_volume_usd_30m": 46929320.6352,
      "sell_volume_usd_4h": 302600299.8822,
      "sell_volume_usd_12h": 1252453833.8841,
      "sell_volume_usd_24h": 1795173612.3167,
      "sell_volume_usd_1w": 8685065667.5425,

      // 主动成交量净流入（= 买入 - 卖出），单位：美元
      "volume_flow_usd_1h": -18117262.2665,
      "volume_flow_usd_5m": -1788028.6161,
      "volume_flow_usd_15m": -10180533.1361,
      "volume_flow_usd_30m": -13788345.1911,
      "volume_flow_usd_4h": -24425456.2483,
      "volume_flow_usd_12h": 158400579.5847,
      "volume_flow_usd_24h": 128746651.7499,
      "volume_flow_usd_1w": -568392334.0579
    }
  ]
}


---

## 交易对挂单深度历史(±范围)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-orderbook-history
交易对挂单深度历史(±范围)
https://open-api-v4.coinglass.com
/api/spot/orderbook/ask-bids-history
该接口提供现货交易对的历史订单簿数据，包含指定价格范围内的买盘和卖盘挂单总量。
缓存 / 更新频率:
5秒一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "bids_usd": 81639959.9338,        // 多单总金额（美元）
      "bids_quantity": 1276.645,        // 多单总数量
      "asks_usd": 78533053.6862,        // 空单总金额（美元）
      "asks_quantity": 1217.125,        // 空单总数量
      "time": 1714003200000             // 时间戳（毫秒）
    },
    {
      "bids_usd": 62345879.8821,
      "bids_quantity": 980.473,
      "asks_usd": 65918423.4715,
      "asks_quantity": 1021.644,
      "time": 1714089600000
    }
  ]
}


---

## 交易对市场列表

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-pairs-markets
交易对市场列表
https://open-api-v4.coinglass.com
/api/spot/pairs-markets
该接口提供现货交易对的相关指标信息。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                         
  "msg": "success",                  
  "data": [
    {
      "symbol": "BTC/USDT",                // 交易对
      "exchange_name": "Binance",          // 交易所名称
      "current_price": 87503.55,           // 当前价格

      "price_change_1h": 99.55,            // 1小时价格变动
      "price_change_percent_1h": 0.11,     // 1小时价格变动百分比
      "volume_usd_1h": 54425251.2426,      // 1小时交易量 (USD)
      "buy_volume_usd_1h": 29304086.0661,  // 1小时买入交易量 (USD)
      "sell_volume_usd_1h": 25121165.1759, // 1小时卖出交易量 (USD)
      "volume_change_usd_1h": -24851651.918,   // 1小时交易量变动 (USD)
      "volume_change_percent_1h": -31.35,  // 1小时交易量变动百分比
      "net_flows_usd_1h": 4182920.8902,    // 1小时净流入 (USD)

      "price_change_4h": 209.56,           // 4小时价格变动
      "price_change_percent_4h": 0.24,     // 4小时价格变动百分比
      "volume_usd_4h": 264625587.5144,     // 4小时交易量 (USD)
      "buy_volume_usd_4h": 137768056.2376, // 4小时买入交易量 (USD)
      "sell_volume_usd_4h": 126857531.2762, // 4小时卖出交易量 (USD)
      "volume_change_4h": -526166190.0218, // 4小时交易量变动 (USD)
      "volume_change_percent_4h": -66.54,  // 4小时交易量变动百分比
      "net_flows_usd_4h": 10910524.9614,   // 4小时净流入 (USD)

      "price_change_12h": 2925.55,         // 12小时价格变动
      "price_change_percent_12h": 3.46,    // 12小时价格变动百分比
      "volume_usd_12h": 1212930000.2011,   // 12小时交易量 (USD)
      "buy_volume_usd_12h": 662857153.6506, // 12小时买入交易量 (USD)
      "sell_volume_usd_12h": 550072846.5499, // 12小时卖出交易量 (USD)
      "volume_change_12h": 842092388.1946, // 12小时交易量变动 (USD)
      "volume_change_percent_12h": 227.08, // 12小时交易量变动百分比
      "net_flows_usd_12h": 112784307.1007, // 12小时净流入 (USD)

      "price_change_24h": 2735.79,         // 24小时价格变动
      "price_change_percent_24h": 3.23,    // 24小时价格变动百分比
      "volume_usd_24h": 1585522232.603,    // 24小时交易量 (USD)
      "buy_volume_usd_24h": 843617569.7248, // 24小时买入交易量 (USD)
      "sell_volume_usd_24h": 741904662.8776, // 24小时卖出交易量 (USD)
      "volume_change_24h": 873336140.8197, // 24小时交易量变动 (USD)
      "volume_change_percent_24h": 122.63, // 24小时交易量变动百分比
      "net_flows_usd_24h": 101712906.8472, // 24小时净流入 (USD)

      "price_change_1w": 3057.83,          // 1周价格变动
      "price_change_percent_1w": 3.62,     // 1周价格变动百分比
      "volume_usd_1w": 6808077059.7062,    // 1周交易量 (USD)
      "buy_volume_usd_1w": 3374037733.8429, // 1周买入交易量 (USD)
      "sell_volume_usd_1w": 3434039325.8627, // 1周卖出交易量 (USD)
      "volume_change_usd_1w": -11208235126.1193, // 1周交易量变动 (USD)
      "volume_change_percent_1w": -62.21,  // 1周交易量变动百分比
      "net_flows_usd_1w": -60001592.0198   // 1周净流入 (USD)
    }
  ]
}


---

## 支持的交易所和交易对

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-suported-exchange-pairs
支持的交易所和交易对
https://open-api-v4.coinglass.com
/api/spot/supported-exchange-pairs
该接口允许你查询 CoinGlass 上支持的所有现货交易所及其对应的交易对。
缓存 / 更新频率:
每 1 分钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "Binance": [ // 交易所名称
      {
        "instrument_id": "BTCUSD_USDT",// 现货交易对
        "base_asset": "BTC",// 基础币种
        "quote_asset": "USDT"// 计价币种
      },
      {
        "instrument_id": "ETHUSD_USDT",
        "base_asset": "ETH",
        "quote_asset": "USDT"
      },
      ....
      ],
    "Bitget": [
      {
        "instrument_id": "AAVE:USD",
        "base_asset": "AAVE",
        "quote_asset": "USD"
      },
      {
        "instrument_id": "ADAUSD",
        "base_asset": "ADA",
        "quote_asset": "USD"
      },
      ...
      ]
      ...
   }
}


---

## 支持的币种

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-supported-coins
支持的币种
https://open-api-v4.coinglass.com
/api/spot/supported-coins
该接口用于查询 CoinGlass 支持的所有现货币种。
缓存 / 更新频率:
每 1 分钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    "BTC",
    "ETH",
    "USDT",
    "BNB",
    "SOL",
    "USDC",
    ...
  ]
}


---

## 交易对主动买卖历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spot-taker-buysell-ratio-history
交易对主动买卖历史
https://open-api-v4.coinglass.com
/api/spot/taker-buy-sell-volume/history
该接口提供现货市场中交易对的主动买入与卖出成交量的历史数据
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741622400000, // 时间戳（毫秒）
      "taker_buy_volume_usd": "10551.033", // 主动买入成交量（美元）
      "taker_sell_volume_usd": "11308" // 主动卖出成交量（美元）
    },
    {
      "time": 1741626000000,
      "taker_buy_volume_usd": "15484.245",
      "taker_sell_volume_usd": "16316.118"
    },
    {
      "time": 1741629600000,
      "taker_buy_volume_usd": "20340.501",
      "taker_sell_volume_usd": "18977.660"
    }
  ]
}


---

## 支持的币种

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spots
支持的币种
https://open-api-v4.coinglass.com
/api/spot/supported-coins
该接口用于查询 CoinGlass 支持的所有现货币种。
缓存 / 更新频率:
每 1 分钟更新一次
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    "BTC",
    "ETH",
    "USDT",
    "BNB",
    "SOL",
    "USDC",
    ...
  ]
}


---

## Coinbase溢价指数

**URL:** https://docs.coinglass.com/v4.0-zh/reference/spots-1
Coinbase溢价指数
https://open-api-v4.coinglass.com
/api/coinbase-premium-index
该接口提供 Coinbase 比特币溢价指数，反映 Coinbase Pro 与 Binance 之间的比特币价格差异。
缓存/ 更新频率 :
实时.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
"time": 1658880000,         // 时间戳（秒）
  "premium": 5.55,            // 溢价金额（USD）
  "premium_rate": 0.0261      // 溢价率（例如 0.0261 表示 2.61%）
    },
    {
"time": 1658880000,         // 时间戳（秒）
  "premium": 5.55,            // 溢价金额（USD）
  "premium_rate": 0.0261      // 溢价率（例如 0.0261 表示 2.61%）
    }
  ]
}


---

## 稳定币市值历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/stablecoin-marketcap-history
稳定币市值历史
https://open-api-v4.coinglass.com
/api/index/stableCoin-marketCap-history
该接口提供稳定币市值历史
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                             
  "msg": "success",                        
  "data": [
    {
      "data_list": [4611285.1141, ...],         // 数值列表
      "price_list": [, ...],                    // 价格列表
      "time_list": [1636588800, ...]            // 时间戳列表
    }
  ]
}


---

## 比特币流通量与年产量

**URL:** https://docs.coinglass.com/v4.0-zh/reference/stock-flow
比特币流通量与年产量
https://open-api-v4.coinglass.com
/api/index/stock-flow
该接口提供比特币 S2F（Stock-to-Flow）模型相关数据，包括特定时间点的价格、比特币的流通总量、年产量，以及距离下次减半的天数等信息。
缓存 / 更新频率:
每天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "timestamp": 1282003200000,       // 时间戳（毫秒）
      "price": 0.07,                    // 当日价格
      "next_halving": 834              // 距离下一次比特币减半的天数
    },
    {
      "timestamp": 1282089600000,       // 时间戳（毫秒）
      "price": 0.068,                   // 当日价格
      "next_halving": 833              // 距离下一次比特币减半的天数
    }
  ]
}


---

## 交易对主动买卖历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/taker-buysell
交易对主动买卖历史
https://open-api-v4.coinglass.com
/api/spot/taker-buy-sell-volume/history
该接口提供现货市场中交易对的主动买入与卖出成交量的历史数据
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1741622400000, // 时间戳（毫秒）
      "taker_buy_volume_usd": "10551.033", // 主动买入成交量（美元）
      "taker_sell_volume_usd": "11308" // 主动卖出成交量（美元）
    },
    {
      "time": 1741626000000,
      "taker_buy_volume_usd": "15484.245",
      "taker_sell_volume_usd": "16316.118"
    },
    {
      "time": 1741629600000,
      "taker_buy_volume_usd": "20340.501",
      "taker_sell_volume_usd": "18977.660"
    }
  ]
}


---

## 币种主动买卖比

**URL:** https://docs.coinglass.com/v4.0-zh/reference/taker-buysell-volume-exchange-list
币种主动买卖比
https://open-api-v4.coinglass.com
/api/futures/taker-buy-sell-volume/exchange-list
该接口提供聚合多个交易所中币种的主动买卖成交量比数据。
缓存 / 更新频率:
1秒钟一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": {
    "symbol": "BTC", // 币种
    "buy_ratio": 51.01, // 买入比例（%）
    "sell_ratio": 48.99, // 卖出比例（%）
    "buy_vol_usd": 1112108532.1688, // 总买入金额（美元）
    "sell_vol_usd": 1068220541.0417, // 总卖出金额（美元）
    "exchange_list": [ // 各个交易所的买卖数据
      {
        "exchange": "Binance", // 交易所名称
        "buy_ratio": 49.22, // 买入比例（%）
        "sell_ratio": 50.78, // 卖出比例（%）
        "buy_vol_usd": 240077939.5811, // 买入金额（美元）
        "sell_vol_usd": *********.1653 // 卖出金额（美元）
      },
      {
        "exchange": "OKX", // 交易所名称
        "buy_ratio": 50.84, // 买入比例（%）
        "sell_ratio": 49.16, // 卖出比例（%）
        "buy_vol_usd": *********.6214, // 买入金额（美元）
        "sell_vol_usd": *********.5904 // 卖出金额（美元）
      }
    ]
  }
}


---

## 大户账户数多空比历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/top-longshort-account-ratio
大户账户数多空比历史
https://open-api-v4.coinglass.com
/api/futures/top-long-short-account-ratio/history
该接口提供大户账户的多空比历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": *************, // 时间戳（毫秒）
      "top_account_long_percent": 73.3, // 大户账户多单比例（%）
      "top_account_short_percent": 26.7, // 大户账户空单比例（%）
      "top_account_long_short_ratio": 2.75 // 大户账户多空比（多/空）
    },
    {
      "time": *************, // 时间戳（毫秒）
      "top_account_long_percent": 74.18, // 大户账户多单比例（%）
      "top_account_short_percent": 25.82, // 大户账户空单比例（%）
      "top_account_long_short_ratio": 2.87 // 大户账户多空比（多/空）
    }
  ]
}


---

## 大户持仓多空比历史

**URL:** https://docs.coinglass.com/v4.0-zh/reference/top-longshort-position-ratio
大户持仓多空比历史
https://open-api-v4.coinglass.com
/api/futures/top-long-short-position-ratio/history
该接口提供大户持仓多空比历史数据。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": *************, // 时间戳（毫秒）
      "top_position_long_percent": 64.99, // 大户持仓多单比例（%）
      "top_position_short_percent": 35.01, // 大户持仓空单比例（%）
      "top_position_long_short_ratio": 1.86 // 大户持仓多空比（多/空）
    },
    {
      "time": *************, // 时间戳（毫秒）
      "top_position_long_percent": 64.99, // 大户持仓多单比例（%）
      "top_position_short_percent": 35.01, // 大户持仓空单比例（%）
      "top_position_long_short_ratio": 1.86 // 大户持仓多空比（多/空）
    }
  ]
}


---

## 200周移动平均热力图

**URL:** https://docs.coinglass.com/v4.0-zh/reference/tow-hundred-week-moving-avg-heatmap
200周移动平均热力图
https://open-api-v4.coinglass.com
/api/index/200-week-moving-average-heatmap
该接口提供200周移动平均热力图数据
缓存 / 更新频率:
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                              
  "msg": "success",                         
  "data": [
    {
      "timestamp": 1325203200000,          // 时间戳 (毫秒)
      "price": 4.31063509000584,           // 当前价格
      "moving_average_1440": 4.143619070636635, // 200 周移动平均线 (1440 表示周期)
      "moving_average_1440_ip": 0,         // 移动平均线位置 (IP 指标)
      "buy_quantity": 0,                   // 买单数量
      "sell_quantity": 0                   // 卖单数量
    }
  ]
}


---

## 两年MA乘数指标

**URL:** https://docs.coinglass.com/v4.0-zh/reference/tow-year-ma-multiplier
两年MA乘数指标
https://open-api-v4.coinglass.com
/api/index/2-year-ma-multiplier
该接口提供两年MA乘数指标数据
缓存 / 更新频率:
1天一次.
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
响应数据
JSON
{
  "code": "0",                              
  "msg": "success",                         
  "data": [
    {
      "timestamp": 1282003200000,               // 时间戳 (毫秒)
      "price": 0.07,                            // 当前价格
      "moving_average_730": 0.07,               // 2 年移动平均线 (730 表示周期)
      "moving_average_730_multiplier_5": 0.35000000000000003, // 2 年移动平均线的 5 倍 (Multiplier)
    }
  ]
}


---

## 成交量加权资金费率历史(K线)

**URL:** https://docs.coinglass.com/v4.0-zh/reference/vol-weight-ohlc-history
成交量加权资金费率历史(K线)
https://open-api-v4.coinglass.com
/api/futures/funding-rate/vol-weight-history
该接口提供按成交量加权的合约币种资金费率 K 线数据，包含开盘价、最高价、最低价和收盘价（OHLC）。
该接口以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
✅
✅
✅
✅
✅
颗粒度
>=4h
​
>=30m
无限制
无限制
无限制
响应数据
JSON
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "time": 1658880000000, // 时间戳（毫秒）
      "open": "0.004603",     // 开盘资金费率
      "high": "0.009388",     // 最高资金费率
      "low": "-0.005063",     // 最低资金费率
      "close": "0.009229"     // 收盘资金费率
    },
    {
      "time": 1658966400000, // 时间戳（毫秒）
      "open": "0.009229",     // 开盘资金费率
      "high": "0.01",         // 最高资金费率
      "low": "0.007794",      // 最低资金费率
      "close": "0.01"         // 收盘资金费率
    }
  ]
}


---

## 基础介绍

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ws-getting-started
基础介绍
基础地址
wss://open-ws.coinglass.com/ws-api
连接方式
要建立连接，请使用以下地址并附带您的 API Key：
wss://open-ws.coinglass.com/ws-api?cg-api-key={your_api_key}
连接管理
如果发生网络问题，系统将自动断开连接。
为了保持连接的稳定性，建议每 20 秒发送一次 'ping' 消息，并预期收到 'pong' 响应。
订阅
如需订阅某个频道，请发送以下消息：
JSON
{
    "method": "subscribe",
    "channels": ["liquidationOrders"]
}
取消订阅
如需取消订阅某个频道，请发送以下消息：
JSON
{
    "method": "unsubscribe",
    "channels": ["liquidationOrders"]
}
响应示例
当收到数据时，响应格式如下所示：
JSON
{
    "channel": "liquidationOrders",
    "data": [
        {
            "baseAsset": "BTC",
            "exName": "Binance",
            "price": 56738.00,
            "side": 2,
            "symbol": "BTCUSDT",
            "time": 1725416318379,
            "volUsd": 3858.18400
        }
    ]
}
该格式可确保在使用 WebSocket API 交互时的清晰性与易用性。
Table of Contents
基础地址
连接方式
连接管理
订阅
取消订阅
响应示例

---

## 爆仓订单

**URL:** https://docs.coinglass.com/v4.0-zh/reference/ws-liquidation-order
爆仓订单
强平订单快照流提供市场交易对的强平订单信息。
以下API等级可用：
API 等级
：
API 等级
爱好版
创业版
标准版
专业版
企业版
可用性
❌
❌
✅
✅
✅
实时强平订单推送
频道:
liquidationOrders
如需订阅 liquidationOrders 频道，请发送以下消息：
JSON
{
    "method": "subscribe",
    "channels": ["liquidationOrders"]
}
返回示例
接收到数据后，返回内容如下所示：
JSON
{
    "channel": "liquidationOrders",
    "data": [
        {
            "baseAsset": "BTC",
            "exName": "Binance",
            "price": 56738.00,
            "side": 2, //side=1   Long liquidation     side=2   Short liquidation
            "symbol": "BTCUSDT",
            "time": 1725416318379,
            "volUsd": 3858.18400
        }
    ]
}
Table of Contents
实时强平订单推送
频道: liquidationOrders

---

