# hack-v1-tm21-TXZY

## TG_HOUSEKEEPER TG组群管家

这个产品针对的是一个很实际的需求，许多web3社区都有管理TG上的困难，其中包括：

- 层出不穷的垃圾广告
- 用户行为的引导管理
- 社群规则和激励机制管理
...

其实WeGroup Bot很大意义上帮助了管理区解决这个问题，但是它存在两个显著的问题。第一是它无法很准确的进行广告封禁，大部分情况下都很容易漏掉一些特殊情形。第二是它不能自动的去做积分计算和代币分配，最后管理需要手动发送。

## 我们能做什么

我们的核心功能就是解决这两个问题：

- 广告
- 积分派发

当然，AI不是万能的，但是提供这样的工具，您可以定制化的修改一些功能

## 使用须知

运行award.py即可，但是需要新建一个.env文件，并且将以下内容填写完毕

`TELEGRAM_BOT_TOKEN` = 组群机器人的Token，需要通过Botfather创建
`OPENAI_API_KEY` = CHATGPT的APIKEY
`OPENAI_BASE_URL` = 自己采用第三方的URL，如果没有可以不填
`PK` = 发放奖励的钱包私钥

运行之后，注意award当中的几个参数

`REWARD_CONF` = AI奖励的置信度，越高则越严格
`MUTE_CONF` = AI封禁的置信度，越高则越严格
`GROUP_RULES_TEXT` = 文字化的群规，你可以任意描述

## 命令

目前只支持4中命令

`/points` 显示当前积分，不限权限
`/rules`  显示群规，不限权限
`/reg`    注册钱包，不限权限
`/award`  发放奖励，需要管理员权限

## 其他

如果你想自己偷偷改分，就用`bot_config.js`
