# Batch AI 聊天机器人 v2.8

一个基于 Google Gemini 2.5 Flash 的高级 Telegram 聊天机器人，支持革命性的**批量对话模式**和智能**任务编排引擎**。

## 🌟 核心特性

### 🔑 智能 API 密钥管理
- **多密钥存储**：支持添加多个 API 密钥，按时间戳自动排序
- **自动故障切换**：当前密钥失效时，自动尝试下一个可用密钥
- **密钥状态监控**：实时显示每个密钥的可用状态（✅可用/❌失效）
- **安全显示**：密钥采用掩码显示（AIza****），保护隐私安全
- **一键管理**：支持添加、删除、重新测试、查看密钥详情
- **智能验证**：添加时自动测试密钥有效性，支持批量测试所有密钥

### 🤖 双模式对话系统
- **单提示词模式**：传统的一对一连续对话，界面简洁专注
- **批量对话模式**：一次输入，获取多角度、多样化的批量回复
- **无缝切换**：主菜单一键切换两种对话模式
- **状态持久化**：所有配置自动保存，重启后完美恢复
- **智能状态管理**：自动检测对话状态，防止误操作

### 📝 灵活提示词系统
- **多提示词管理**：创建、编辑、删除自定义提示词
- **实时切换**：对话中随时切换提示词
- **批量选择**：批量模式下可同时选择多个提示词（支持多选）
- **默认提示词**：内置默认选项，开箱即用
- **提示词预览**：管理界面显示提示词预览，便于识别

### ⚙️ 任务编排引擎 (v2.8 核心功能)
强大的批量处理配置系统，提供精确的任务控制：

#### 执行策略
- **任务优先（深度优先）**：完成一个提示词的全部重复次数后，再开始下一个
  - 执行顺序：A1, A2, A3 → B1, B2, B3
  - 适合：需要完整对比不同提示词效果的场景
- **提示词优先（广度优先）**：先为所有选中的提示词执行第1轮，然后是第2轮
  - 执行顺序：A1, B1 → A2, B2 → A3, B3
  - 适合：需要快速获得多角度初步结果的场景

#### 交付策略
- **一次性输出（批量交付）**：等待所有任务结束，将结果一次性打包发送
  - 优势：结果完整，便于对比分析
  - 适合：不急于查看中间结果的场景
- **逐一输出（流式交付）**：每完成一个API调用就立即发送结果
  - 优势：实时反馈，可提前查看部分结果
  - 适合：需要实时监控处理进度的场景

#### 输出格式
- **聊天框**：纯文本消息形式，直接在对话中显示
- **TXT文件**：.txt 格式的附件，便于保存和分享
- **两者都要**：同时发送文本消息和 .txt 附件，兼顾查看和保存

### 📊 智能报告系统
- **按执行轮次分组**：提示词优先策略下的透明报告结构
  - 清晰显示每轮执行的所有提示词结果
  - 便于横向对比同轮次不同提示词的效果
- **按提示词分组**：任务优先策略下的经典报告格式
  - 完整展示每个提示词的所有重复结果
  - 便于纵向分析单个提示词的稳定性
- **统一分隔符**：使用"---"分隔符的简洁设计
- **成功率统计**：详细的任务执行状态统计，包含成功/失败数量和百分比
- **时间戳记录**：每个任务结果都包含精确的完成时间

### 💾 数据管理
- **智能缓存**：自动管理聊天缓存大小，默认限制2GB，防止文件过大
- **自动清理**：缓存超限时自动清理90%最旧数据，保持系统流畅
- **历史导出**：支持将对话历史导出为 .txt 文件，文件名包含时间戳
- **数据持久化**：用户配置和聊天记录分离存储，互不影响
- **安全设计**：API密钥仅在内存中存储，不在日志中暴露，保护隐私

## 📱 用户界面

### 主菜单示例

**单提示词模式**（简洁专注）：
```
🤖 batch ai 聊天机器人

🔑 API 密钥：3 个 (可用: 3)
   当前选中：✅ key1
💬 对话模式：单提示词 (默认提示词)
🔊 对话状态：❌ 未开启

📝 使用说明：
1️⃣ 点击 '▶️ 开始对话' 开启聊天
2️⃣ 直接发送消息开始对话
3️⃣ 可管理提示词自定义AI行为

🔑 免费API密钥获取：https://aistudio.google.com/apikey
```

**批量对话模式**（功能扩展）：
```
🤖 batch ai 聊天机器人

🔑 API 密钥：3 个 (可用: 3)
   当前选中：✅ key1
💬 对话模式：批量 (已选: 2, 重复: 3次, 共6次调用)
⚙️ 执行策略：🐢 深度优先 (任务优先) | 交付：📥 批量交付 | 格式：💬 聊天消息
🔊 对话状态：❌ 未开启

📝 使用说明：
1️⃣ 选择一个或多个提示词
2️⃣ 设置重复次数（可选）
3️⃣ 点击 '▶️ 开始对话' 开启批量模式
```

### 界面布局设计

#### 内联键盘（主菜单）
```
[ 📝 管理提示词 ] [ 🔑 密钥管理 ] [ ⚙️ 设置 ]
[ ✅ key1 ] [ key2 ] [ key3 ]
[ ✅ 默认提示词 ] [ ✅ 提示词1 ] [ 提示词2 ]
[ 不重复 ] [ ✅ 重复: 3 ] [ 自定义... ]
[ ✅ 单提示词 ] [ 批量对话 ] [ ▶️ 开始对话 ] [ ⏸️ 取消对话 ]
```

#### 底部常驻键盘
```
[ 🏠 主菜单 ] [ 🔑 密钥管理 ] [ 📝 提示词管理 ] [ ⚙️ 设置 ]
[ 📋 导出记录 ] [ ▶️ 开始对话 ] [ ⏸️ 取消对话 ]
```

#### 设置中心界面
```
⚙️ 任务编排引擎设置

🔄 执行策略：
[ ✅ 任务优先 ] [ 提示词优先 ]

📦 交付策略：
[ ✅ 批量交付 ] [ 逐一交付 ]

📄 输出格式：
[ ✅ 聊天消息 ] [ TXT文件 ] [ 两者都要 ]

[ 🔙 返回主菜单 ]
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 稳定的网络连接
- Telegram 账户

### 安装依赖
```bash
pip install -r requirements.txt
```

**依赖包说明：**
- `python-telegram-bot==21.3` - Telegram Bot API 库
- `requests==2.31.0` - HTTP 请求库

### 配置 Bot Token
编辑 `config.py` 文件：
```python
# 从 @BotFather 获取的 Bot Token
TELEGRAM_BOT_TOKEN = "your_bot_token_here"

# Bot 设置
BOT_SETTINGS = {
    "api_test_timeout": 10,        # API 密钥测试超时时间（秒）
    "api_call_timeout": 30,        # API 调用超时时间（秒）
    "cache_max_size_mb": 2048,     # 缓存文件最大大小（MB）
    "cache_cleanup_ratio": 0.9,    # 清理时保留的比例
}
```

### 运行机器人
```bash
python telegram_gemini_bot.py
```

**启动成功标志：**
```
Bot 启动成功！
已设置命令菜单和底部常驻键盘
✅ 已设置机器人命令菜单
```

## 📖 使用指南

### 初次设置
1. 启动机器人后，发送 `/start` 命令或点击 "🏠 主菜单"
2. 点击 "🔑 密钥管理" 按钮
3. 点击 "➕ 添加密钥" 添加您的第一个 Gemini API Key
4. 系统会自动测试密钥有效性并显示状态（✅可用/❌失效）
5. 返回主菜单即可开始使用

### 单提示词模式（传统对话）
1. 保持默认的"✅ 单提示词"模式
2. 可选择"默认提示词"或自定义提示词
3. 点击"▶️ 开始对话"激活对话状态
4. 直接发送消息开始对话
5. 享受连续的一对一对话体验

### 批量对话模式（核心功能）
1. 点击"批量对话"切换模式
2. 选择一个或多个提示词（点击切换选中状态，✅表示已选中）
3. 设置重复次数（不重复、重复3/5/7次或自定义1-20次）
4. 进入"⚙️ 设置"配置任务编排引擎策略
5. 点击"▶️ 开始对话"激活批量模式
6. 发送一条消息，获取多角度批量回复

### 任务编排引擎详细配置

#### 执行策略选择
- **任务优先（深度优先）**：适合需要完整对比不同提示词效果
  - 执行顺序：提示词A的所有重复 → 提示词B的所有重复
  - 报告格式：按提示词分组显示
- **提示词优先（广度优先）**：适合快速获得多角度初步结果
  - 执行顺序：第1轮所有提示词 → 第2轮所有提示词
  - 报告格式：按执行轮次分组显示

#### 交付策略选择
- **批量交付（一次性输出）**：等待所有任务完成后统一发送
  - 优势：结果完整，便于对比分析
  - 适合：不急于查看中间结果的场景
- **逐一交付（流式输出）**：每完成一个任务立即发送结果
  - 优势：实时反馈，可提前查看部分结果
  - 适合：需要实时监控处理进度的场景

#### 输出格式选择
- **聊天消息**：直接在对话中显示，便于快速查看
- **TXT文件**：生成附件文件，便于保存和分享
- **两者都要**：同时发送消息和文件，兼顾查看和保存

### 实际使用示例

**场景**：将一篇文章进行多角度分析

1. **准备提示词**：
   - 提示词1：请总结这篇文章的核心要点
   - 提示词2：请将这篇文章翻译成英文
   - 提示词3：请提取这篇文章的关键词

2. **配置批量任务**：
   - 切换到"批量对话"模式
   - 选中3个提示词（✅ 提示词1、✅ 提示词2、✅ 提示词3）
   - 设置重复次数为2次（获得多个版本供选择）

3. **配置执行策略**：
   - 执行策略：选择"提示词优先"（广度优先）
   - 交付策略：选择"批量交付"（一次性输出）
   - 输出格式：选择"两者都要"（文本+文件）

4. **执行任务**：
   - 点击"▶️ 开始对话"
   - 发送文章内容
   - 系统执行6次API调用（3个提示词 × 2次重复）

5. **获取结果**：
   - 按轮次分组的结构化报告
   - 同时收到聊天消息和TXT文件
   - 包含成功率统计和时间戳

### 故障切换机制
- **智能密钥管理**：系统按添加时间排序，优先使用最新密钥
- **自动故障切换**：当前密钥失效时，自动尝试下一个可用密钥
- **全链路重试**：依次尝试所有密钥直到找到可用的
- **友好提示**：所有密钥都失效时，提示用户添加新密钥
- **状态监控**：实时显示每个密钥的可用状态

## 🔧 技术架构

### 核心组件架构
- **TelegramGeminiBot 类**：核心机器人逻辑，处理所有用户交互
- **任务编排引擎**：批量处理和结果管理的核心模块
- **状态管理器**：用户配置和对话状态的持久化管理
- **缓存系统**：智能的聊天记录管理和自动清理
- **密钥管理器**：多密钥存储、测试和故障切换
- **界面控制器**：内联键盘和底部键盘的统一管理

### 数据存储结构
```
项目根目录/
├── user_data.json          # 用户配置数据
│   ├── API密钥列表（加密存储）
│   ├── 自定义提示词
│   ├── 任务编排引擎设置
│   └── 用户偏好配置
├── chat_cache.json         # 聊天记录缓存
│   ├── 按用户ID分组的对话历史
│   ├── 自动清理机制
│   └── 大小限制控制
└── 临时文件/
    ├── 导出的聊天记录 (.txt)
    ├── 批量处理结果 (.txt)
    └── 单任务结果文件
```

### API 集成架构
- **Google Gemini 2.5 Flash**：主要AI模型，支持高质量对话生成
- **多密钥支持**：自动故障切换和负载均衡机制
- **请求优化**：智能超时设置（测试10秒，调用30秒）和错误处理
- **并发控制**：批量任务的顺序执行，避免API限流
- **安全机制**：密钥掩码显示，不在日志中暴露敏感信息

## 📋 命令列表

| 命令 | 功能描述 | 使用场景 |
|------|----------|----------|
| `/start` | 启动机器人并显示主菜单 | 首次使用或重新开始 |
| `/menu` | 显示主菜单 | 快速返回主界面 |
| `/keys` | 密钥管理 | 添加、删除、测试API密钥 |
| `/prompts` | 提示词管理 | 创建、编辑、删除提示词 |
| `/settings` | 设置中心（任务编排引擎） | 配置批量处理策略 |
| `/chat` | 开始对话 | 快速激活对话状态 |
| `/stop` | 停止对话 | 结束当前对话会话 |
| `/export` | 导出聊天记录 | 保存对话历史为文件 |
| `/help` | 显示帮助信息 | 查看详细使用说明 |

### 快捷操作
除了命令外，还可以使用：
- **底部常驻键盘**：快速访问常用功能
- **内联键盘**：直观的图形化操作界面
- **智能状态检测**：根据当前状态自动显示相应选项

## 🔑 获取 API Key

访问 [Google AI Studio](https://aistudio.google.com/apikey) 获取免费的 Gemini API Key。

## 🤖 创建 Telegram Bot

### 详细步骤
1. 在 Telegram 中搜索并与 [@BotFather](https://t.me/botfather) 对话
2. 发送 `/newbot` 命令创建新机器人
3. 按提示设置机器人名称（如：My Batch AI Bot）
4. 设置机器人用户名（必须以bot结尾，如：my_batch_ai_bot）
5. 获取 Bot Token 并配置到 `config.py` 文件
6. （可选）设置机器人头像和描述

### 推荐配置
```
机器人名称：Batch AI Assistant
用户名：your_batch_ai_bot
描述：基于 Gemini 2.5 Flash 的智能批量对话机器人
```

## 🔒 安全特性

### 隐私保护
- **API密钥掩码**：密钥采用 `AIza****` 格式显示，保护完整密钥信息
- **日志安全**：API密钥不在日志中暴露，仅显示掩码版本
- **本地存储**：所有数据仅在本地存储，不上传到云端服务器
- **内存安全**：敏感信息仅在内存中临时存储，不写入磁盘缓存

### 数据安全
- **智能清理**：自动清理缓存文件，防止数据积累过多
- **分离存储**：用户配置和聊天记录分离，降低数据泄露风险
- **故障恢复**：支持密钥状态监控和自动重试机制
- **输入验证**：严格验证用户输入，防止恶意数据注入

## 📊 性能特性

### 缓存优化
- **智能缓存**：自动管理聊天缓存大小（默认2GB限制）
- **自动清理**：超限时保留90%最新数据，删除10%最旧数据
- **内存效率**：优化的用户状态管理，避免内存泄漏

### 并发处理
- **批量优化**：支持高效的批量API调用处理
- **顺序执行**：避免API限流，确保请求成功率
- **快速响应**：优化的超时设置（测试10秒，调用30秒）
- **错误处理**：完善的异常处理和用户友好的错误提示

### 性能监控
- **实时进度**：批量处理时显示实时进度信息
- **成功率统计**：详细的任务执行状态统计
- **时间戳记录**：每个操作都有精确的时间记录

## 🆕 v2.8 更新亮点

### 🎯 任务编排引擎（核心功能）
- **精确控制**：执行策略、交付策略、输出格式的全面配置
  - 执行策略：任务优先（深度优先）vs 提示词优先（广度优先）
  - 交付策略：批量交付（一次性输出）vs 逐一交付（流式输出）
  - 输出格式：聊天消息 vs TXT文件 vs 两者都要
- **透明报告**：按执行轮次或提示词分组的结构化结果
- **智能截断**：超长结果自动生成文件，避免Telegram消息长度限制
- **统一设计**：使用"---"分隔符的简洁美观格式

### 🎨 用户体验优化
- **设置中心**：专门的配置界面，详细的策略说明和实时预览
- **双层键盘**：内联键盘+底部常驻键盘，提供完整的操作体验
- **状态显示**：主菜单实时显示当前策略配置和系统状态
- **进度提示**：批量处理时的实时进度显示（已完成/总任务数）
- **智能提示**：根据当前状态提供相应的操作指导

### 🔧 技术改进
- **模块化设计**：清晰分离的功能模块，便于维护和扩展
- **错误处理**：完善的异常处理和用户友好的错误反馈
- **代码优化**：更高效的任务队列生成和结果处理算法
- **向后兼容**：完全兼容旧版本的用户数据，平滑升级
- **内存管理**：优化的状态管理，避免内存泄漏

### 🚀 性能提升
- **批量处理优化**：支持最多20次重复，单次最多处理数百个任务
- **文件生成优化**：大结果自动生成文件，避免界面卡顿
- **缓存管理**：智能的缓存清理机制，保持系统流畅运行

## 🎯 适用场景

### 📝 内容创作
- **多风格写作**：一次输入，获取正式、轻松、专业等多种风格的文案
- **创意激发**：通过不同角度的提示词激发创意灵感
- **版本对比**：生成多个版本供选择，提高创作效率

### 📄 文档处理
- **多任务并行**：同时进行总结、翻译、关键词提取等操作
- **格式转换**：将同一内容转换为不同格式（报告、PPT大纲、邮件等）
- **质量保证**：通过重复生成确保内容质量和准确性

### 🎓 学习研究
- **多角度分析**：从不同角度分析问题，获得全面见解
- **知识整理**：将复杂内容进行分类整理和结构化处理
- **学习辅助**：生成练习题、总结要点、制作学习卡片

### 💼 工作效率
- **批量处理**：处理重复性的AI任务，节省时间
- **标准化输出**：使用固定提示词确保输出格式一致
- **团队协作**：生成多个方案供团队讨论选择

## 💡 使用技巧

### 🎨 提示词设计
1. **明确目标**：每个提示词都应有明确的目的和预期输出
2. **分工明确**：不同提示词负责不同任务，避免功能重叠
3. **格式统一**：使用一致的格式要求，便于后续处理

### ⚙️ 策略选择
1. **执行策略选择**：
   - 需要对比效果时选择"任务优先"
   - 需要快速预览时选择"提示词优先"
2. **交付策略选择**：
   - 不急于查看时选择"批量交付"
   - 需要实时监控时选择"逐一交付"
3. **输出格式选择**：
   - 快速查看选择"聊天消息"
   - 需要保存选择"TXT文件"
   - 兼顾两者选择"两者都要"

### 🔧 实用技巧
1. **合理设置重复次数**：一般2-3次即可获得足够的选择空间
2. **善用文件输出**：复杂结果使用文件格式便于保存和分享
3. **定期导出记录**：及时导出重要的对话记录，避免数据丢失
4. **密钥管理**：添加多个API密钥确保服务稳定性
5. **批量处理监控**：大任务时选择"逐一交付"便于监控进度

## 📊 批量处理示例

### 场景一：文章多角度分析

**需求**：对一篇技术新闻稿进行全面分析
**配置**：
- 选中提示词：总结要点、翻译英文、提取关键词
- 重复次数：2次
- 执行策略：提示词优先（广度优先）
- 交付策略：批量交付（一次性输出）
- 输出格式：两者都要

**执行过程**：
1. 系统生成6个任务（3个提示词 × 2次重复）
2. 按广度优先顺序执行：第1轮所有提示词 → 第2轮所有提示词
3. 等待所有任务完成后统一发送结果

**输出结果**：
```
� 批量处理完整报告

📝 原始消息：人工智能技术获得重大突破，新算法...
🕒 报告生成时间：2025-01-20 15:30:45
📈 总计任务：6 个
✅ 成功任务：6 个
❌ 失败任务：0 个
📊 成功率：100.0%

---

�🔄 第 1 轮

📝 总结要点:
• 人工智能技术获得重大突破
• 新算法提升处理效率50%
• 预计2025年商业化应用

📝 翻译英文:
Artificial Intelligence technology achieves major breakthrough...

📝 提取关键词:
关键词：人工智能、技术突破、算法优化、商业化

---

🔄 第 2 轮

📝 总结要点:
核心要点包括：
1. AI算法创新突破传统限制
2. 性能提升显著超出预期...

📝 翻译英文:
The newly announced AI technology represents...

📝 提取关键词:
核心术语：AI技术、创新算法、性能优化、产业应用
```

### 场景二：创意文案生成

**需求**：为产品生成多种风格的营销文案
**配置**：
- 选中提示词：正式商务风格、轻松活泼风格、专业技术风格
- 重复次数：3次
- 执行策略：任务优先（深度优先）
- 交付策略：逐一交付（流式输出）
- 输出格式：聊天消息

**执行过程**：
1. 系统生成9个任务（3个提示词 × 3次重复）
2. 按深度优先顺序执行：正式风格3次 → 活泼风格3次 → 技术风格3次
3. 每完成一个任务立即发送结果

**优势**：
- 可以实时查看每种风格的多个版本
- 便于快速筛选满意的文案
- 不需要等待所有任务完成

## 🔧 开发与调试

### 项目结构
```
telegram-ai-bot/
├── telegram_gemini_bot.py    # 主程序文件（2300+行）
├── config.py                 # 配置文件
├── requirements.txt          # 依赖列表
├── user_data.json           # 用户配置数据（自动生成）
├── chat_cache.json          # 聊天记录缓存（自动生成）
└── README.md                # 项目文档
```

### 核心功能实现

#### 1. 批量处理核心逻辑
```python
def generate_task_queue(self, selected_prompts, repeat_count, execution_strategy, user_state):
    """根据执行策略生成任务队列"""
    # 任务优先：A1,A2,A3 → B1,B2,B3
    # 提示词优先：A1,B1 → A2,B2 → A3,B3
```
- **任务队列生成**：根据执行策略动态生成任务序列
- **API调用管理**：智能的密钥轮询和故障切换机制
- **结果收集处理**：支持多种输出格式的结果整理和文件生成

#### 2. 状态管理系统
```python
def get_user_state(self, user_id):
    """获取用户状态，自动初始化缺失字段"""
    # 支持向后兼容的状态管理
```
- **用户配置持久化**：API密钥、提示词、偏好设置的JSON存储
- **聊天缓存管理**：独立的对话历史存储和智能清理
- **实时状态同步**：界面状态与后端数据的一致性保证

#### 3. 错误处理机制
```python
async def call_gemini_api(self, user_state, contents):
    """API调用，支持多密钥故障切换"""
    # 自动重试和密钥切换逻辑
```
- **API调用错误**：自动重试和密钥切换，最多尝试所有可用密钥
- **文件操作错误**：完善的资源清理和用户友好提示
- **用户输入验证**：参数范围检查（重复次数1-20）和格式验证

### 代码质量保证

#### 已实现的关键特性
1. **类型安全**：用户ID统一使用字符串类型，避免JSON序列化问题
2. **资源管理**：临时文件的正确创建和清理机制
3. **异常处理**：完善的try-catch块和用户友好的错误消息
4. **状态一致性**：界面状态与数据状态的实时同步

#### 测试覆盖场景
- **功能测试**：单提示词模式、批量模式、所有策略组合
- **异常测试**：网络错误、API限流、文件操作失败
- **边界测试**：空提示词、最大重复次数、超长消息
- **并发测试**：多用户同时操作的稳定性验证

### 性能监控

#### 文件命名规范
```python
# 时间戳格式：YYYYMMDD_HHMMSS
chat_log_20250120_153045.txt      # 聊天记录导出
batch_report_20250120_153045.txt  # 批量结果文件
task_result_1.txt                 # 单任务结果（流式输出）
```

#### 缓存管理策略
```python
BOT_SETTINGS = {
    "api_test_timeout": 10,        # API密钥测试超时（秒）
    "api_call_timeout": 30,        # API调用超时（秒）
    "cache_max_size_mb": 2048,     # 缓存大小限制（MB）
    "cache_cleanup_ratio": 0.9,    # 清理时保留的数据比例
}
```

#### 性能优化点
- **智能缓存**：超过2GB时自动清理10%最旧数据
- **内存管理**：及时释放大文件内容，避免内存泄漏
- **并发控制**：批量任务顺序执行，避免API限流
- **进度反馈**：实时更新处理进度，提升用户体验

## 🚀 部署指南

### 开发环境部署
```bash
# 1. 下载项目文件
# 将所有项目文件下载到本地目录

# 2. 安装Python依赖
pip install -r requirements.txt

# 3. 配置Bot Token
# 编辑 config.py 文件，设置你的 TELEGRAM_BOT_TOKEN

# 4. 运行机器人
python telegram_gemini_bot.py
```

### 生产环境部署

#### 使用 systemd 服务（Linux推荐）
```bash
# 1. 创建服务文件
sudo nano /etc/systemd/system/batch-ai-bot.service

# 2. 服务配置内容
[Unit]
Description=Batch AI Telegram Bot
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/bot
ExecStart=/usr/bin/python3 telegram_gemini_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# 3. 启动服务
sudo systemctl daemon-reload
sudo systemctl start batch-ai-bot
sudo systemctl enable batch-ai-bot

# 4. 查看状态
sudo systemctl status batch-ai-bot
```

#### 使用 screen/tmux（简单部署）
```bash
# 使用 screen
screen -S batch-ai-bot
python telegram_gemini_bot.py
# Ctrl+A+D 分离会话

# 使用 tmux
tmux new-session -d -s batch-ai-bot 'python telegram_gemini_bot.py'
```

### Docker 部署
```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 暴露端口（如果需要）
# EXPOSE 8080

# 运行命令
CMD ["python", "telegram_gemini_bot.py"]
```

```bash
# 构建镜像
docker build -t batch-ai-bot .

# 运行容器
docker run -d --name batch-ai-bot \
  -v $(pwd)/user_data.json:/app/user_data.json \
  -v $(pwd)/chat_cache.json:/app/chat_cache.json \
  batch-ai-bot

# 查看日志
docker logs -f batch-ai-bot
```

### 部署注意事项
1. **数据持久化**：确保 `user_data.json` 和 `chat_cache.json` 文件的持久化存储
2. **权限设置**：确保运行用户有读写项目目录的权限
3. **网络连接**：确保服务器能够访问 Telegram API 和 Google Gemini API
4. **监控日志**：定期检查运行日志，及时发现和解决问题
5. **备份数据**：定期备份用户配置和重要聊天记录

## 🤝 贡献指南

### 代码贡献
1. Fork 项目到你的GitHub账户
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

### 问题反馈
- 使用GitHub Issues报告bug或请求新功能
- 提供详细的错误信息和复现步骤
- 包含系统环境和版本信息

### 代码规范
- 遵循PEP 8 Python编码规范
- 添加必要的注释和文档字符串
- 确保所有测试通过
- 保持向后兼容性

## 📝 更新日志

### v2.8 (2025-01-20) - 任务编排引擎版本
#### 🎯 核心功能
- ✨ **任务编排引擎**：全新的批量处理配置系统
  - 执行策略：任务优先（深度优先）vs 提示词优先（广度优先）
  - 交付策略：批量交付（一次性输出）vs 逐一交付（流式输出）
  - 输出格式：聊天消息 vs TXT文件 vs 两者都要
- � **智能报告系统**：按执行轮次或提示词分组的结构化报告
- ⚙️ **设置中心**：专门的配置界面，详细的策略说明

#### 🎨 用户体验
- � **双层键盘设计**：内联键盘+底部常驻键盘
- 📱 **状态实时显示**：主菜单实时显示当前策略配置
- 🔄 **进度实时反馈**：批量处理时的实时进度显示
- � **智能提示系统**：根据当前状态提供操作指导

#### 🚀 技术改进
- 🏗️ **模块化重构**：清晰分离的功能模块，便于维护
- 🛡️ **错误处理优化**：完善的异常处理和用户友好反馈
- ⚡ **性能优化**：更高效的任务队列和结果处理算法
- 🔄 **向后兼容**：完全兼容旧版本用户数据

### v2.0 (2025-01-12) - 批量对话版本
#### 主要功能
- 🚀 **批量对话模式**：一次输入，多角度批量回复
- 📝 **多提示词并行处理**：支持同时选择多个提示词
- 🔄 **重复次数控制**：1-20次可配置重复次数
- 💾 **状态持久化**：所有配置自动保存和恢复

#### 技术特性
- 🔑 **多密钥管理**：智能故障切换机制
- 📊 **结果统计**：详细的成功率和时间统计
- 🎯 **用户体验**：直观的多选界面和状态显示

### v1.0 (2025-01-10) - 初始版本
#### 基础功能
- 🎉 **项目启动**：基于 Google Gemini 2.5 Flash 的 Telegram 机器人
- 🔑 **API密钥管理**：多密钥存储和状态监控
- 💬 **基础对话功能**：单提示词模式的连续对话
- 📤 **聊天记录导出**：对话历史的文件导出功能

#### 核心架构
- 🏗️ **TelegramGeminiBot类**：核心机器人逻辑框架
- 💾 **数据持久化**：JSON格式的用户数据存储
- 🔒 **安全设计**：API密钥掩码显示和隐私保护

## 📞 支持与联系

### 🛠️ 技术支持
- � **问题反馈**：通过 GitHub Issues 报告 bug 或请求新功能
- � **使用文档**：详细阅读本 README 文档获取完整使用指南
- � **故障排除**：检查日志输出，确认网络连接和API密钥状态
- 💡 **功能建议**：欢迎提出改进建议和新功能需求

### 🤝 社区交流
- � **经验分享**：分享使用技巧和最佳实践
- 🎯 **应用场景**：讨论不同行业的应用案例
- � **版本更新**：及时了解新版本功能和改进
- �📚 **学习资源**：共享相关学习资料和教程

### 🚀 项目发展
- 🌟 **开源贡献**：欢迎提交代码改进和功能扩展
- 🔍 **代码审查**：遵循代码规范，确保质量标准
- 📈 **功能规划**：参与讨论未来版本的功能规划
- � **界面优化**：改进用户界面和交互体验

### 📋 常见问题解答

#### Q: 如何获取 Gemini API Key？
A: 访问 [Google AI Studio](https://aistudio.google.com/apikey)，使用 Google 账户登录即可免费获取。

#### Q: 批量处理失败怎么办？
A: 检查 API 密钥状态，确认网络连接，查看错误日志信息。

#### Q: 如何备份我的配置？
A: 定期备份 `user_data.json` 文件，包含所有用户配置和提示词。

#### Q: 支持哪些操作系统？
A: 支持 Windows、Linux、macOS 等所有支持 Python 3.7+ 的系统。

#### Q: 如何升级到新版本？
A: 下载新版本文件，替换 `telegram_gemini_bot.py`，用户数据会自动兼容。

---

## 🎉 结语

**Batch AI v2.8** - 让AI对话更高效，让批量处理更智能！

### 🌟 项目特色
- � **创新功能**：革命性的批量对话模式和任务编排引擎
- 🎯 **实用性强**：解决实际工作中的AI应用需求
- 🔧 **易于使用**：直观的界面设计和详细的使用指南
- 🛡️ **稳定可靠**：完善的错误处理和故障恢复机制

### 📈 持续改进
我们致力于不断改进和优化这个项目，让它成为最好用的 Telegram AI 机器人。您的反馈和建议是我们前进的动力！

### 🙏 致谢
感谢所有使用者的支持和反馈，感谢开源社区的贡献和帮助！

---

📄 **许可证**：MIT License
⭐ **如果这个项目对你有帮助，请给个Star支持！**
🔄 **欢迎 Fork 和贡献代码！**
