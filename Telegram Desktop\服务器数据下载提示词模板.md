# 服务器数据下载标准提示词模板

## 基础下载模板

```
帮我从服务器下载业务数据，服务器信息：
- IP: [服务器IP地址]
- 用户: [用户名，如ubuntu]
- 密钥: [密钥文件名，如web666.pem]
- 项目路径: [项目根目录，如/home/<USER>

需要下载的目录/文件：
- [目录名，如data]

排除的目录（不下载）：
- [排除目录1，如backups]
- [排除目录2，如coinglass]
- [排除目录3，如logs]
```

## 快速下载模板（适合着急的情况）

```
快速下载服务器数据：
服务器：[IP] 用户：[username] 密钥：[keyfile]
下载：[目录名]
排除：[排除目录1, 排除目录2]
```

## 完整下载模板（包含具体需求）

```
帮我下载服务器的业务数据，具体要求：

**服务器信息：**
- IP地址: [服务器IP]
- 登录用户: [用户名]
- SSH密钥: [密钥文件路径]
- 项目运行目录: [项目路径]

**下载需求：**
- 目标目录: [要下载的目录路径]
- 文件类型限制: [如只下载*.json文件]
- 排除目录: [不需要的目录列表]
- 本地保存路径: [本地目录名]

**特殊要求：**
- [ ] 需要保持目录结构
- [ ] 需要压缩打包
- [ ] 只下载特定时间后的文件
- [ ] 需要备份重命名
```

## 实际使用示例

### 示例1：下载完整业务数据
```
帮我下载服务器的业务数据：
- 服务器：**************
- 用户：ubuntu  
- 密钥：web666.pem
- 下载目录：data
- 排除：backups, coinglass, logs
```

### 示例2：只下载特定文件
```
从服务器下载用户数据：
服务器：************** (ubuntu/web666.pem)
只下载：data目录下的*.json文件
排除：data/backups, data/coinglass
```

### 示例3：紧急下载
```
快点下载服务器data目录！
IP: **************
不要：backups, coinglass
```

## 提示词优化建议

1. **明确服务器信息**：IP、用户名、密钥文件
2. **指定具体路径**：避免模糊的"项目数据"等说法
3. **明确排除项**：列出不需要的目录/文件
4. **表达紧急程度**：如需要快速完成可以直接说"快点"
5. **指定保存位置**：本地保存的目录名称

## 常用命令参考

```bash
# 查找项目运行路径
ps aux | grep python

# 递归下载并排除特定目录
scp -r -i keyfile user@ip:/path/to/data ./local_dir

# 打包排除特定目录后下载
rsync -av --exclude='dir1' --exclude='dir2' /source/ /dest/
``` 