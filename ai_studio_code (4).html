<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PYTHIA/USD (1H) - 极简K线图 (带下载功能)</title>
    <style>
        /* 关键CSS: 确保图表占满整个屏幕，无边距 */
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 防止出现滚动条 */
            background-color: #131722; /* 背景色与图表主题一致 */
        }
        #chart_container {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>

    <!-- 唯一的HTML元素：用于承载图表的容器 -->
    <div id="chart_container"></div>

    <!-- 引入 TradingView 官方的 tv.js 库 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    
    <!-- 初始化并配置图表小部件 -->
    <script type="text/javascript">
        new TradingView.widget({
            // --- 核心配置 ---
            "container_id": "chart_container",
            "autosize": true,
            "symbol": "CRYPTO:PYTHIAUSD",
            "interval": "60", // 1小时周期
            "locale": "zh_CN",
            "theme": "dark",

            // --- 【关键修改】我们不再隐藏顶部工具栏 ---
            "hide_top_toolbar": false,

            // --- 【关键修改】我们只禁用“除了截图之外”的所有按钮 ---
            "disabled_features": [
                // 禁用顶部工具栏的所有按钮...
                "header_symbol_search",      // 币种搜索
                "header_resolutions",        // 时间周期选择
                "header_chart_type",         // 图表类型选择
                "header_indicators",         // 技术指标按钮
                "header_compare",            // 品种对比按钮
                "header_saveload",           // 保存/加载布局按钮
                "header_fullscreen_button",  // 全屏按钮
                "header_settings",           // 图表设置按钮
                // ...除了 "header_screenshot" (截图按钮)

                // 禁用左侧绘图工具栏
                "left_toolbar"
            ],
            
            // 预加载的技术指标
            "studies": [
                "Volume@tv-basicstudies"
            ],
            
            // 视觉样式配置
            "overrides": {
                "paneProperties.rightMargin": 15,
                "paneProperties.background": "#131722",
                "paneProperties.vertGridProperties.color": "#363c4e",
                "paneProperties.horzGridProperties.color": "#363c4e",
                "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350"
            }
        });
    </script>

</body>
</html>