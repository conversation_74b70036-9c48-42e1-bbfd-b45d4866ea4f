!function(){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)({}).hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},t.apply(null,arguments)}var r="undefined"!=typeof window?window:void 0,n="undefined"!=typeof globalThis?globalThis:r,e=Array.prototype.forEach,i=null==n?void 0:n.navigator,a=null==n?void 0:n.document;null==n||n.location,null==n||n.fetch,null!=n&&n.XMLHttpRequest&&"withCredentials"in new n.XMLHttpRequest&&n.XMLHttpRequest,null==n||n.AbortController,null==i||i.userAgent;var u=null!=r?r:{};function o(t,r){return-1!==t.indexOf(r)}var l=function(t){return t.trim()},s=Array.isArray,c=Object.prototype,h=c.hasOwnProperty,f=c.toString,v=s||function(t){return"[object Array]"===f.call(t)},d=t=>"function"==typeof t,_=t=>d(t)&&-1!==t.toString().indexOf("[native code]"),m=()=>!!r.Zone,b=t=>void 0===t,p=t=>"[object String]"==f.call(t),g=t=>null===t,w=t=>b(t)||g(t),k=t=>"[object Number]"==f.call(t),y=t=>t instanceof FormData,A=t=>{var n={t:function(n){if(r&&u.POSTHOG_DEBUG&&!b(r.console)&&r.console){for(var e=("__rrweb_original__"in r.console[n]?r.console[n].__rrweb_original__:r.console[n]),i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];e(t,...a)}},info:function(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];n.t("log",...r)},warn:function(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];n.t("warn",...r)},error:function(){for(var t=arguments.length,r=new Array(t),e=0;e<t;e++)r[e]=arguments[e];n.t("error",...r)},critical:function(){for(var r=arguments.length,n=new Array(r),e=0;e<r;e++)n[e]=arguments[e];console.error(t,...n)},uninitializedWarning:t=>{n.error("You must initialize PostHog before calling "+t)},createLogger:r=>A(t+" "+r)};return n},$=A("[PostHog.js]"),x={};function C(t,r,n){if(v(t))if(e&&t.forEach===e)t.forEach(r,n);else if("length"in t&&t.length===+t.length)for(var i=0,a=t.length;i<a;i++)if(i in t&&r.call(n,t[i],i)===x)return}function O(t,r,n){if(!w(t)){if(v(t))return C(t,r,n);if(y(t)){for(var e of t.entries())if(r.call(n,e[1],e[0])===x)return}else for(var i in t)if(h.call(t,i)&&r.call(n,t[i],i)===x)return}}var j=function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),e=1;e<r;e++)n[e-1]=arguments[e];return C(n,(function(r){for(var n in r)void 0!==r[n]&&(t[n]=r[n])})),t};function D(t){for(var r=Object.keys(t),n=r.length,e=new Array(n);n--;)e[n]=[r[n],t[r[n]]];return e}function E(t,r,n,e){var{capture:i=!1,passive:a=!0}=null!=e?e:{};null==t||t.addEventListener(r,n,{capture:i,passive:a})}function T(t){return!!t&&1===t.nodeType}function R(t,r){return!!t&&!!t.tagName&&t.tagName.toLowerCase()===r.toLowerCase()}function S(t){return t?l(t).split(/\s+/):[]}function L(t){var r="";switch(typeof t.className){case"string":r=t.className;break;case"object":r=(t.className&&"baseVal"in t.className?t.className.baseVal:null)||t.getAttribute("class")||"";break;default:r=""}return S(r)}function M(t){var r="";return H(t)&&!P(t)&&t.childNodes&&t.childNodes.length&&O(t.childNodes,(function(t){var n,e;(function(t){return!!t&&3===t.nodeType})(t)&&t.textContent&&(r+=null!==(e=t.textContent,n=w(e)?null:l(e).split(/(\s+)/).filter((t=>G(t))).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255))&&void 0!==n?n:"")})),l(r)}var z=["a","button","form","input","select","textarea","label"];function H(t){for(var r=t;r.parentNode&&!R(r,"body");r=r.parentNode){var n=L(r);if(o(n,"ph-sensitive")||o(n,"ph-no-capture"))return!1}if(o(L(t),"ph-include"))return!0;var e=t.type||"";if(p(e))switch(e.toLowerCase()){case"hidden":case"password":return!1}var i=t.name||t.id||"";if(p(i)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(i.replace(/[^a-zA-Z0-9]/g,"")))return!1}return!0}function P(t){return!!(R(t,"input")&&!["button","checkbox","submit","reset"].includes(t.type)||R(t,"select")||R(t,"textarea")||"true"===t.getAttribute("contenteditable"))}var I="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",N=new RegExp("^(?:"+I+")$"),W=new RegExp(I),q="\\d{3}-?\\d{2}-?\\d{4}",B=new RegExp("^("+q+")$"),F=new RegExp("("+q+")");function G(t,r){if(void 0===r&&(r=!0),w(t))return!1;if(p(t)){if(t=l(t),(r?N:W).test((t||"").replace(/[- ]/g,"")))return!1;if((r?B:F).test(t))return!1}return!0}function V(t){var r=M(t);return G(r=(r+" "+Y(t)).trim())?r:""}function Y(t){var r="";return t&&t.childNodes&&t.childNodes.length&&O(t.childNodes,(function(t){var n;if(t&&"span"===(null==(n=t.tagName)?void 0:n.toLowerCase()))try{var e=M(t);r=(r+" "+e).trim(),t.childNodes&&t.childNodes.length&&(r=(r+" "+Y(t)).trim())}catch(t){$.error("[AutoCapture]",t)}})),r}function Z(r){return function(r){var n=r.map((r=>{var n,e,i="";if(r.tag_name&&(i+=r.tag_name),r.attr_class)for(var a of(r.attr_class.sort(),r.attr_class))i+="."+a.replace(/"/g,"");var u=t({},r.text?{text:r.text}:{},{"nth-child":null!==(n=r.nth_child)&&void 0!==n?n:0,"nth-of-type":null!==(e=r.nth_of_type)&&void 0!==e?e:0},r.href?{href:r.href}:{},r.attr_id?{attr_id:r.attr_id}:{},r.attributes),o={};return D(u).sort(((t,r)=>{var[n]=t,[e]=r;return n.localeCompare(e)})).forEach((t=>{var[r,n]=t;return o[J(r.toString())]=J(n.toString())})),i+=":",i+=D(o).map((t=>{var[r,n]=t;return r+'="'+n+'"'})).join("")}));return n.join(";")}(function(t){return t.map((t=>{var r,n,e={text:null==(r=t.$el_text)?void 0:r.slice(0,400),tag_name:t.tag_name,href:null==(n=t.attr__href)?void 0:n.slice(0,2048),attr_class:K(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return D(t).filter((t=>{var[r]=t;return 0===r.indexOf("attr__")})).forEach((t=>{var[r,n]=t;return e.attributes[r]=n})),e}))}(r))}function J(t){return t.replace(/"|\\"/g,'\\"')}function K(t){var r=t.attr__class;return r?v(r)?r:S(r):void 0}function Q(t,r){return r.length>t?r.slice(0,t)+"...":r}function U(t){if(t.previousElementSibling)return t.previousElementSibling;var r=t;do{r=r.previousSibling}while(r&&!T(r));return r}function X(t,r,n,e){var i=t.tagName.toLowerCase(),a={tag_name:i};z.indexOf(i)>-1&&!n&&("a"===i.toLowerCase()||"button"===i.toLowerCase()?a.$el_text=Q(1024,V(t)):a.$el_text=Q(1024,M(t)));var u=L(t);u.length>0&&(a.classes=u.filter((function(t){return""!==t}))),O(t.attributes,(function(n){var i;if((!P(t)||-1!==["name","id","class","aria-label"].indexOf(n.name))&&((null==e||!e.includes(n.name))&&!r&&G(n.value)&&(i=n.name,!p(i)||"_ngcontent"!==i.substring(0,10)&&"_nghost"!==i.substring(0,7)))){var u=n.value;"class"===n.name&&(u=S(u).join(" ")),a["attr__"+n.name]=Q(1024,u)}}));for(var o=1,l=1,s=t;s=U(s);)o++,s.tagName===t.tagName&&l++;return a.nth_child=o,a.nth_of_type=l,a}function tt(t,n){for(var e,i,u,{e:l,maskAllElementAttributes:s,maskAllText:c,elementAttributeIgnoreList:h,elementsChainAsString:f}=n,v=[t],d=t;d.parentNode&&!R(d,"body");)(u=d.parentNode)&&11===u.nodeType?(v.push(d.parentNode.host),d=d.parentNode.host):(v.push(d.parentNode),d=d.parentNode);var _,m,p,g=[],w={},k=!1,y=!1;if(O(v,(t=>{var r=H(t);"a"===t.tagName.toLowerCase()&&(k=t.getAttribute("href"),k=r&&k&&G(k)&&k),o(L(t),"ph-no-capture")&&(y=!0),g.push(X(t,s,c,h));var n=function(t){if(!H(t))return{};var r={};return O(t.attributes,(function(t){if(t.name&&0===t.name.indexOf("data-ph-capture-attribute")){var n=t.name.replace("data-ph-capture-attribute-",""),e=t.value;n&&e&&G(e)&&(r[n]=e)}})),r}(t);j(w,n)})),y)return{props:{},explicitNoCapture:y};if(c||("a"===t.tagName.toLowerCase()||"button"===t.tagName.toLowerCase()?g[0].$el_text=V(t):g[0].$el_text=M(t)),k){var A,$;g[0].attr__href=k;var x=null==(m=k,p=null==a?void 0:a.createElement("a"),A=b(p)?null:(p.href=m,p))?void 0:A.host,C=null==r||null==($=r.location)?void 0:$.host;x&&C&&x!==C&&(_=k)}return{props:j({$event_type:l.type,$ce_version:1},f?{}:{$elements:g},{$elements_chain:Z(g)},null!=(e=g[0])&&e.$el_text?{$el_text:null==(i=g[0])?void 0:i.$el_text}:{},_&&"click"===l.type?{$external_click_url:_}:{},w)}}var rt={};function nt(t){return function(t,r){var n=rt[t];if(n)return n;var e=r[t];if(_(e)&&!m())return rt[t]=e.bind(r);var i=r.document;if(i&&d(i.createElement))try{var a=i.createElement("iframe");a.hidden=!0,i.head.appendChild(a);var u=a.contentWindow;u&&u[t]&&(e=u[t]),i.head.removeChild(a)}catch(r){$.warn("Could not create sandbox iframe for "+t+" check, bailing to assignableWindow."+t+": ",r)}return e&&d(e)?rt[t]=e.bind(r):e}("MutationObserver",t)}function et(t){var r,n,e=b((r=t).target)?r.srcElement||null:null!=(n=r.target)&&n.shadowRoot?r.composedPath()[0]||null:r.target||null;return e?{node:e,originalEvent:t,timestamp:Date.now()}:null}function it(t,r){return k(t)&&t>=r}class at{i(t){var r,n,e,i,a=this.u((null==t?void 0:t.__onCapture)||this.o.bind(this));return{element_attribute_ignorelist:null!==(r=null==t?void 0:t.element_attribute_ignorelist)&&void 0!==r?r:a.element_attribute_ignorelist,scroll_threshold_ms:null!==(n=null==t?void 0:t.scroll_threshold_ms)&&void 0!==n?n:a.scroll_threshold_ms,selection_change_threshold_ms:null!==(e=null==t?void 0:t.selection_change_threshold_ms)&&void 0!==e?e:a.selection_change_threshold_ms,mutation_threshold_ms:null!==(i=null==t?void 0:t.mutation_threshold_ms)&&void 0!==i?i:a.mutation_threshold_ms,__onCapture:a.__onCapture}}constructor(t,r){this.l=[],this.u=t=>({element_attribute_ignorelist:[],scroll_threshold_ms:100,selection_change_threshold_ms:100,mutation_threshold_ms:2500,__onCapture:t}),this.h=t=>{var r=et(t);g(r)||this.v(r)||this.l.push(r),this.l.length&&b(this.m)&&(this.m=u.setTimeout((()=>{this.p()}),1e3))},this.k=()=>{var t=Date.now();t%50==0&&this.l.forEach((r=>{b(r.scrollDelayMs)&&(r.scrollDelayMs=t-r.timestamp)}))},this.A=()=>{this.$=Date.now()},this.instance=t,this.C=this.i(r),this._onCapture=this.C.__onCapture}start(t){this.O(),this.j(),this.D(),this.T(t)}T(t){if(!this.R){var r=nt(u);this.R=new r((t=>{this.S(t)})),this.R.observe(t,{attributes:!0,characterData:!0,childList:!0,subtree:!0})}}stop(){var t;null==(t=this.R)||t.disconnect(),this.R=void 0,u.removeEventListener("click",this.h),u.removeEventListener("scroll",this.k,{capture:!0}),u.removeEventListener("selectionchange",this.A)}S(t){this.L=Date.now()}O(){E(u,"click",this.h)}j(){E(u,"scroll",this.k,{capture:!0})}D(){E(u,"selectionchange",this.A)}v(t){return!t||(!!((r=t.node)instanceof Element&&("__POSTHOG_TOOLBAR__"===r.id||null!=r.closest&&r.closest(".toolbar-global-fade-container")))||(!!this.l.some((r=>r.node===t.node&&Math.abs(r.timestamp-t.timestamp)<1e3))||!(!R(t.node,"html")&&T(t.node)&&!z.includes(t.node.tagName.toLowerCase()))));var r}p(){if(this.l.length){clearTimeout(this.m),this.m=void 0;var t=this.l;for(var r of(this.l=[],t)){var n;r.mutationDelayMs=null!==(n=r.mutationDelayMs)&&void 0!==n?n:this.L&&r.timestamp<=this.L?this.L-r.timestamp:void 0,r.absoluteDelayMs=Date.now()-r.timestamp,r.selectionChangedDelayMs=this.$&&r.timestamp<=this.$?this.$-r.timestamp:void 0;var e=it(r.scrollDelayMs,this.C.scroll_threshold_ms),i=it(r.selectionChangedDelayMs,this.C.selection_change_threshold_ms),a=it(r.mutationDelayMs,this.C.mutation_threshold_ms),o=it(r.absoluteDelayMs,1.1*this.C.mutation_threshold_ms),l=k(r.scrollDelayMs)&&r.scrollDelayMs<this.C.scroll_threshold_ms,s=k(r.mutationDelayMs)&&r.mutationDelayMs<this.C.mutation_threshold_ms,c=k(r.selectionChangedDelayMs)&&r.selectionChangedDelayMs<this.C.selection_change_threshold_ms;l||s||c||(e||a||o||i?this._onCapture(r,{$dead_click_last_mutation_timestamp:this.L,$dead_click_event_timestamp:r.timestamp,$dead_click_scroll_timeout:e,$dead_click_mutation_timeout:a,$dead_click_absolute_timeout:o,$dead_click_selection_changed_timeout:i}):r.absoluteDelayMs<this.C.mutation_threshold_ms&&this.l.push(r))}this.l.length&&b(this.m)&&(this.m=u.setTimeout((()=>{this.p()}),1e3))}}o(r,n){this.instance.capture("$dead_click",t({},n,tt(r.node,{e:r.originalEvent,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.C.element_attribute_ignorelist,elementsChainAsString:!1}).props,{$dead_click_scroll_delay_ms:r.scrollDelayMs,$dead_click_mutation_delay_ms:r.mutationDelayMs,$dead_click_absolute_delay_ms:r.absoluteDelayMs,$dead_click_selection_changed_delay_ms:r.selectionChangedDelayMs}),{timestamp:new Date(r.timestamp)})}}u.__PosthogExtensions__=u.__PosthogExtensions__||{},u.__PosthogExtensions__.initDeadClicksAutocapture=(t,r)=>new at(t,r)}();
//# sourceMappingURL=dead-clicks-autocapture.js.map
