# Google 提示词最佳实践宝典

## 目录

*   [基础概念](#基础概念)
    *   [引言](#引言)
    *   [提示工程](#提示工程)
    *   [LLM 输出配置](#llm-输出配置)
*   [提示技巧](#提示技巧)
    *   [通用提示 / 零样本](#通用提示--零样本)
    *   [单样本 & 少样本](#单样本--少样本)
    *   [系统、上下文和角色提示](#系统上下文和角色提示)
    *   [思维链 (Chain of Thought - CoT)](#思维链-chain-of-thought---cot)
    *   [回退提示 (Step-Back Prompting)](#回退提示-step-back-prompting)
    *   [自我一致性 (Self-consistency)](#自我一致性-self-consistency)
*   [代码提示](#代码提示)
    *   [编写代码的提示](#编写代码的提示)
    *   [解释代码的提示](#解释代码的提示)
    *   [翻译代码的提示](#翻译代码的提示)
*   [最佳实践](#最佳实践)
    *   [提供示例](#提供示例)
    *   [简洁设计](#简洁设计)
    *   [具体说明输出](#具体说明输出)

## 基础概念

### 引言

**任何人都可以编写提示**
在探讨大型语言模型（LLM）的输入与输出时，文本提示（有时伴随图像等其他模态）是模型用于预测特定输出的输入形式。编写提示并非数据科学家或机器学习工程师的专利——任何人都可以进行。

> "You don't need to be a data scientist or a machine learning engineer – everyone can write a prompt."

**提示有效性的影响因素**
*   所使用的模型
*   模型的训练数据
*   模型配置
*   措辞选择
*   风格语调
*   结构
*   上下文

**提示工程的特点**
*   迭代的过程
*   不恰当的提示可能导致模糊、不准确的响应
*   需要结构化的方法和清晰的呈现

**Gemini 聊天机器人 vs Vertex AI/API**
*   **Gemini 聊天机器人**:
    *   无法访问温度等配置参数
    *   休闲使用场景
*   **Vertex AI/API**:
    *   可直接配置温度等参数
    *   高级提示工程基础
    *   特定创造性或确定性任务

### 提示工程

**LLM 工作原理**
理解 LLM 的工作原理至关重要：它是一个预测引擎。模型接收顺序文本作为输入，然后基于其训练数据预测下一个应该出现的令牌（token）。LLM 被设计为反复执行此过程，将先前预测的令牌添加到序列文本的末尾，以预测下一个令牌。

*   **预测引擎**: 基于训练数据预测下一个令牌
*   **迭代过程**: 反复执行预测过程
*   **上下文依赖**: 基于先前令牌内容预测

"工程"一词在此处的使用是恰当的，因为它描述了一个涉及"设计"、"优化"、"评估"和"调试"的系统过程。这不仅仅是写作，更是一个针对需求进行系统性改进的过程，类似于传统的工程学科。

**提示工程的应用场景**
*   **自然语言处理**:
    *   文本摘要
    *   信息提取
    *   问答系统
    *   文本分类
*   **代码相关**:
    *   代码生成
    *   代码翻译
    *   代码文档编写
    *   代码推理

### LLM 输出配置

选定模型后，需要确定模型配置。大多数 LLM 都带有各种配置选项，用于控制其输出。有效的提示工程需要为特定任务优化设置这些配置。

**输出长度 (Output Length)**
*   **令牌数量**: 影响计算量 (短 / 中 / 长)
*   生成更多令牌需要 LLM 进行更多计算，导致更高的能耗、可能更慢的响应时间以及更高的成本。
*   减少输出长度并不会使 LLM 输出更简洁，只是强制截断。

**采样控制 (Sampling Control)**
LLM 预测下一个令牌可能是什么的概率，然后对这些令牌概率进行采样，以确定将生成的下一个令牌。包括：
*   温度 (Temperature)
*   Top-K
*   Top-P

**温度 (Temperature)**
*   **随机程度控制**: 0-1范围
*   **确定性 vs 创造性**:
    *   低温 (0-0.3): 适用于期望更确定性响应的提示（如事实问答）。
    *   高温 (0.7-1): 可能导致更多样化或意想不到的结果（如故事生成）。
*   **技术细节**:
    *   温度为 0（贪婪解码）是确定性的：始终选择概率最高的令牌。
    *   接近最大值的温度倾向于产生更随机的输出。
    *   随着温度越来越高，所有令牌成为下一个预测令牌的可能性变得均等。

**Top-K 和 Top-P (Top-K and top-P)**
*   **Top-K 采样**:
    *   从模型预测的分布中选择概率最高的 K 个令牌。
    *   K 值影响 (1 - 词汇表大小): 较低 K 值更具限制性，较高 K 值更具创造性。
    *   Top-K 为 1 等同于贪婪解码。
*   **Top-P 采样**:
    *   选择累积概率不超过某个值 (P) 的最高概率令牌。
    *   P 值范围 (0 - 1): 较低 P 值更具确定性，较高 P 值更具多样性。
    *   也称为核采样 (nucleus sampling)。
*   **比较分析**:
    *   机制差异: Top-K 是令牌数量的硬限制，Top-P 是基于概率总和的限制。

## 提示技巧

### 通用提示 / 零样本

零样本提示是指不向模型提供任何示例，直接要求其完成任务。

**零样本提示示例**
模型温度应设置为较低的数字，因为不需要创造性，并且我们使用 `gemini-pro` 默认的 Top-K 和 Top-P 值，这实际上禁用了这两个设置。

**表 1：零样本提示示例 (An example of zero-shot prompting)**

| 字段         | 值                                                                                                                                                             |
| :----------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 名称 (Name)  | 1_1_movie_classification                                                                                                                                       |
| 目标 (Goal)  | 将电影评论分类为正面、中性或负面。(Classify movie reviews as positive, neutral or negative.)                                                                              |
| 模型 (Model) | `gemini-pro`                                                                                                                                                   |
| 温度 (Temp)  | 0.1                                                                                                                                                            |
| 令牌限制 (Limit) | 5                                                                                                                                                              |
| Top-K        | N/A                                                                                                                                                            |
| Top-P        | 1                                                                                                                                                              |
| 提示 (Prompt) | 将电影评论分类为正面 (POSITIVE)、中性 (NEUTRAL) 或负面 (NEGATIVE)。<br><br>评论："她"是一项令人不安的研究，揭示了如果允许人工智能不受约束地持续进化，人类将走向何方。我希望有更多像这部杰作一样的电影。<br><br>情绪： |
| 输出 (Output) | POSITIVE                                                                                                                                                       |

当零样本不起作用时，可以在提示中提供演示或示例，这就引出了"单样本"和"少样本"提示。

### 单样本 & 少样本

**示例的重要性**
在为 AI 模型创建提示时，提供示例很有帮助。这些示例可以帮助模型理解您的要求，尤其是在希望引导模型遵循特定的输出结构或模式时。

*   **单样本提示 (One-shot Prompting)**: 提供单个示例。
*   **少样本提示 (Few-shot Prompting)**: 向模型提供多个示例，展示需要遵循的模式。

**示例数量指南**
*   少样本提示通常建议至少使用三到五个示例。
*   具体数量取决于任务复杂性、示例质量和模型能力。
*   更复杂的任务可能需要更多示例，或因输入长度限制而减少示例。

**少样本提示示例**
使用与之前相同的 `gemini-pro` 模型配置设置，只是增加了令牌限制以适应更长响应的需求。

**表 2：少样本提示示例 (An example of few-shot prompting)**

| 字段         | 值                                                                                                                                                                                                                                                        |
| :----------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 将披萨订单解析为 JSON (Parse pizza orders to JSON)                                                                                                                                                                                                              |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                              |
| 温度 (Temp)  | 0.1                                                                                                                                                                                                                                                       |
| 令牌限制 (Limit) | 250                                                                                                                                                                                                                                                     |
| Top-K        | N/A                                                                                                                                                                                                                                                       |
| Top-P        | 1                                                                                                                                                                                                                                                         |
| 提示 (Prompt) | 将顾客的披萨订单解析为有效的 JSON：<br><br>示例：<br>我想要一个小号披萨，配料有奶酪、番茄酱和意大利辣香肠。<br>JSON 响应： `json { "size": "small", "type": "normal", "ingredients": [["cheese", "tomato sauce", "peperoni"]] }`<br><br>示例：<br>我可以要一个大号披萨，配料有番茄酱、罗勒和马苏里拉奶酪吗？<br>JSON 响应： `Json { "size": "large", "type": "normal", "ingredients": [["tomato sauce", "bazel", "mozzarella"]] }`<br><br>现在：<br>我想要一个大号披萨，一半是奶酪和马苏里拉奶酪。另一半是番茄酱、火腿和菠萝。<br><br>JSON 响应： |
| 输出 (Output) | `Json { "size": "large", "type": "half-half", "ingredients": [["cheese", "mozzarella"], ["tomato sauce", "ham", "pineapple"]] }`                                                                                                                            |

**上下文学习 (In-Context Learning)**
少样本提示利用了模型的上下文学习能力。通过观察示例，模型可以推断出潜在的任务和期望的输出格式，而无需显式的指令调整。因此，示例的质量和多样性至关重要。

**示例选择指南**
*   **高质量示例**:
    *   与任务高度相关
    *   展示不同的输入情况
    *   格式一致且准确
    *   避免小错误，以免混淆模型
*   **边缘情况**:
    *   包含不寻常或意外的输入（模型仍应能处理）
    *   不常见但可能发生的输入
    *   边界条件测试
    *   模糊或歧义输入

### 系统、上下文和角色提示

这三种提示类型都是用于指导 LLM 如何生成文本的技术，但它们侧重于不同的方面：

*   **系统提示 (System Prompt)**: 设置语言模型的总体背景和目的。定义模型应该做什么的"大局"（如翻译、分类）。
*   **上下文提示 (Context Prompt)**: 提供与当前对话或任务相关的特定细节或背景信息。帮助模型理解细微差别并调整响应。
*   **角色提示 (Role Prompt)**: 为语言模型分配一个特定的角色或身份以供其采用。帮助模型生成与所分配角色及其相关知识和行为一致的响应。

**提示类型的比较**
这三种提示类型代表了指导 LLM 的不同层面或维度。系统提示设定舞台，上下文提示提供即时的场景细节，而角色提示定义了"演员"的形象。它们可以单独使用，也可以组合使用以实现精细控制。

**系统提示示例**
提高了温度以获得更高的创造力水平，并指定了更高的令牌限制。然而，由于关于如何返回输出的明确指示，模型没有返回额外的文本。

**表 3：系统提示示例**

| 字段         | 值                                                                                                                                                                                             |
| :----------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 将电影评论分类为正面、中性或负面。仅返回大写标签。(Classify movie reviews as positive, neutral or negative. Only return the label in uppercase.)                                                               |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                   |
| 温度 (Temp)  | 1                                                                                                                                                                                              |
| 令牌限制 (Limit) | 5                                                                                                                                                                                              |
| Top-K        | 40                                                                                                                                                                                             |
| Top-P        | 0.8                                                                                                                                                                                            |
| 提示 (Prompt) | 将电影评论分类为正面 (POSITIVE)、中性 (NEUTRAL) 或负面 (NEGATIVE)。仅以大写形式返回标签。<br><br>评论："她"是一项令人不安的研究，揭示了如果允许人工智能不受约束地持续进化，人类将走向何方。它是如此令人不安，以至于我无法观看。<br><br>情绪： |
| 输出 (Output) | NEGATIVE                                                                                                                                                                                       |

系统提示对于生成满足特定要求的输出非常有用，例如生成特定编程语言的代码或返回某种结构。

**表 4：带 JSON 格式的系统提示示例**

| 字段         | 值                                                                                                                                                                                                                                                         |
| :----------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 将电影评论分类为正面、中性或负面，返回 JSON。(Classify movie reviews as positive, neutral or negative, return JSON.)                                                                                                                                       |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                               |
| 温度 (Temp)  | 1                                                                                                                                                                                                                                                          |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                                                                                   |
| Top-K        | 40                                                                                                                                                                                                                                                         |
| Top-P        | 0.8                                                                                                                                                                                                                                                        |
| 提示 (Prompt) | 将电影评论分类为正面 (POSITIVE)、中性 (NEUTRAL) 或负面 (NEGATIVE)。返回有效的 JSON：<br><br>评论："她"是一项令人不安的研究，揭示了如果允许人工智能不受约束地持续进化，人类将走向何方。它是如此令人不安，以至于我无法观看。<br><br>模式 (Schema)：<br>```json\n{\n  "MOVIE": {\n    "sentiment": "String (POSITIVE|NEUTRAL|NEGATIVE)"\n  }\n}``` |
| 输出 (Output) | ```json\n{\n  "Movie_reviews": {\n    "sentiment": "NEGATIVE"\n  }\n}```                                                                                                                                                                                      |

**结构化数据输出**
通过提供模式 (Schema) 可以强制执行结构化数据输出 (JSON)，这对于将 LLM 输出集成到下游应用程序中非常有价值。强制结构也有助于限制幻觉。

**JSON 格式的优势**
*   易于应用程序集成。
*   可以按排序顺序返回数据（方便处理日期时间）。
*   迫使模型创建结构，限制幻觉。

系统提示对于安全性和毒性控制也非常有用。只需在提示中添加一行，例如："你的回答应该保持尊重。"

**角色提示技术**
角色提示是提示工程中的一种技术，涉及为生成式 AI 模型分配特定角色。这可以帮助模型生成更相关、信息更丰富的输出。

**表 5：角色提示示例**

| 字段         | 值                                                                                                                                                                                               |
| :----------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 扮演旅行指南并提供 3 个旅行建议 (Act as travel guide and provide 3 travel suggestions)                                                                                                                  |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                     |
| 温度 (Temp)  | 1                                                                                                                                                                                                |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                           |
| Top-K        | 40                                                                                                                                                                                               |
| Top-P        | 0.8                                                                                                                                                                                              |
| 提示 (Prompt) | 我希望你扮演一个旅行指南。我会告诉你我的位置，你会建议我附近 3 个值得参观的地方。在某些情况下，我还会告诉你我将参观的地方类型。<br><br>我的建议："我在阿姆斯特丹，只想参观博物馆。" <br><br>旅行建议：                               |
| 输出 (Output) | 国立博物馆 (Rijksmuseum): 世界著名的博物馆，以伦勃朗的《夜巡》等荷兰杰作而闻名。不要错过展示荷兰艺术和历史的令人难以置信的绘画和文物收藏。<br>梵高博物馆 (Van Gogh Museum): 沉浸在文森特·梵高迷人的作品中。该博物馆收藏了他最大量的绘画和素描，包括《星夜》和《向日葵》。<br>阿姆斯特丹市立博物馆 (Stedelijk Museum Amsterdam): 探索来自世界各地的现代和当代艺术。该博物馆的收藏包括毕加索、康定斯基和里特维尔德的作品，坐落在一座引人注目的现代建筑中。 |

**角色提示的优势**
为 AI 模型定义角色视角，为其提供了所需的语调、风格和专注专业知识的蓝图，以提高输出的质量、相关性和有效性。角色提示允许 LLM 模拟一个角色，采用其预期的知识、词汇、语调和风格。

**有效的风格选择**
*   对抗性
*   描述性
*   直接
*   正式
*   幽默
*   有影响力
*   非正式
*   鼓舞人心
*   有说服力

**表 6：带幽默语气的角色提示示例**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :----------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 扮演旅行指南并提供 3 个旅行建议 (Act as travel guide and provide 3 travel suggestions)                                                                                                                                                                                                                                                                                                                                                                                                   |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| 温度 (Temp)  | 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Top-K        | 40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Top-P        | 0.8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| 提示 (Prompt) | 我希望你扮演一个旅行指南。我会告诉你我的位置，你会用幽默的风格建议我附近 3 个值得参观的地方。<br><br>我的建议："我在曼哈顿。" <br><br>旅行建议：                                                                                                                                                                                                                                                                                                                                               |
| 输出 (Output) | **见证帝国之心 (Behold the Empire State of Mind):** 登上帝国大厦令人眩晕的高度，沐浴在曼哈顿天际线的荣耀中。准备好感觉自己像金刚站在大苹果之巅，只是没有巨猿大小的香蕉。<br>**在 MoMA 变得艺术范儿 (Get Artsy-Fartsy at MoMA):** 在现代艺术博物馆 (MoMA) 释放你内心的艺术爱好者。凝视那些会让你大开眼界、让你质疑自己的简笔画是否有任何艺术价值的杰作。<br>**在第五大道购物到手软 (Shop 'Til You Drop on Fifth Avenue):** 在标志性的第五大道尽情享受购物疗法。当你浏览（或实际购买）那些会让你的钱包哭泣的设计师精品店时，请准备好迎接价格冲击。但是，嘿，你在曼哈顿，所以不妨拥抱这种精彩！ |

**上下文提示的价值**
通过提供上下文提示，可以帮助确保 AI 交互尽可能无缝和高效。模型将能够更快地理解您的请求，并能够生成更准确、更相关的响应。上下文提示将 LLM 的响应置于特定的环境或背景信息中，防止产生通用或不相关的输出。

**表 7：上下文提示示例**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 为关于复古游戏的博客建议文章 (Suggest articles for a blog about retro games)                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 温度 (Temp)  | 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Top-K        | 40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Top-P        | 0.8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 提示 (Prompt) | 背景： 你正在为一个关于 80 年代复古街机视频游戏的博客写作。<br><br>建议 3 个可以撰写文章的主题，并附上几行关于该文章应包含内容的描述。                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 输出 (Output) | **街机柜设计的演变 (The Evolution of Arcade Cabinet Design):** 这篇文章将探讨街机柜设计的演变，从 1970 年代早期的木质和金属柜到 1980 年代及以后的时尚、霓虹灯设计。<br>**昔日重现：80 年代标志性街机游戏 (Blast From The Past: Iconic Arcade Games of The 80's):** 这篇文章将简要介绍 1980 年代一些最具标志性和影响力的街机游戏的历史、影响和遗产，重点介绍它们的创新、流行机制和持久魅力。<br>**像素艺术的兴起与复古复兴 (The Rise and Retro Revival of Pixel Art):** 这篇文章将深入探讨像素艺术作为早期街机游戏定义性视觉风格的演变，追溯其根源，探索其技术，并讨论像素艺术在现代游戏和数字艺术中的复苏。 |

### 回退提示 (Step-Back Prompting)

这种技术通过先生成更抽象或基础性的概念，然后将这些概念用作上下文来指导模型完成具体任务，从而提高输出的准确性和相关性。

**1. 基线：直接请求 (Baseline: Direct Request)**
直接请求创意内容，尤其是在高温度下，可能产生通用输出。

**表 8：基线提示示例：直接请求创意内容**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 提示 (Prompt) | 为一个新的第一人称射击视频游戏关卡编写一段具有挑战性且引人入胜的故事情节（一段）。<br><br>关卡开始时，玩家的小队在一个密集的城市区域遭到无情敌方派系的伏击。玩家必须在迷宫般的后巷和破旧建筑中奋力杀出一条血路，利用潜行和精确射击消灭敌军并避免被发现。玩家必须利用环境优势，设置陷阱，利用掩体，并采取经过计算的动作才能生存。在此过程中，他们发现情报表明敌人正计划进行毁灭性攻击，玩家必须与时间赛跑，收集证据并将其传回总部，以免敌人实施计划。 |
| 输出 (Output) | 玩家小队在城市废墟中执行侦察任务时突然遭到敌方精锐部队的伏击。子弹呼啸而过，爆炸震撼着摇摇欲坠的建筑。作为小队中唯一的幸存者，你必须利用环境优势——翻倒的车辆作为临时掩体，狭窄的小巷限制敌人数量优势，设置爆炸陷阱引诱敌人进入致命伏击。随着你深入敌后，你截获的无线电通讯揭示了一个可怕的阴谋：敌人正在准备对平民区发动生化攻击。时间紧迫，你必须突破层层防线，在敌人实施计划前将证据传回总部。                                                             |

**2. 回退步骤：生成一般概念 (Step Back: Generate General Concepts)**
退一步思考，首先生成一些有助于构建故事情节的一般概念。

**表 9：生成一般概念**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 生成 5 个虚构的关键设定，有助于构建具有挑战性和吸引力的第一人称射击游戏关卡故事情节。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| 温度 (Temp)  | 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Top-K        | 40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Top-P        | 0.8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 提示 (Prompt) | 基于流行的第一人称射击动作游戏，有哪些 5 个虚构的关键设定有助于在第一人称射击视频游戏中构建具有挑战性且引人入胜的关卡故事情节？                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| 输出 (Output) | **废弃的军事基地 (Abandoned Military Base):** 一个庞大的、后末日时代的军事综合体，爬满了变异士兵和流氓机器人，非常适合具有挑战性的枪战。<br>**赛博朋克城市 (Cyberpunk City):** 一个霓虹闪烁、未来主义的城市环境，有高耸的摩天大楼和密集的巷道，以经过控制论增强的敌人和黑客机制为特色。<br>**外星飞船 (Alien Spaceship):** 一艘搁浅在地球上的巨大外星飞船，有怪异的走廊、零重力区域和需要遭遇的外星生物。<br>**僵尸横行的小镇 (Zombie-Infested Town):** 一个荒凉的小镇，被成群的侵略性僵尸占领，以激烈的近距离战斗和解谜寻找安全通道为特色。<br>**水下研究设施 (Underwater Research Facility):** 一个被水淹没的深海实验室，充满了变异的水生生物，需要潜行和水下探索技能才能生存。 |

**3. 最终提示：应用回退上下文 (Final Prompt: Apply Step-Back Context)**
回到最初的提示，但这次将回退步骤的答案作为上下文包含进去。

**表 10：使用回退上下文的最终提示**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| :----------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 目标 (Goal)  | 基于提供的 5 个主题之一，为第一人称射击游戏编写一个具有挑战性和吸引力的关卡故事情节。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 模型 (Model) | `gemini-pro`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 温度 (Temp)  | 1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 令牌限制 (Limit) | 1024                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Top-K        | 40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| Top-P        | 0.8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| 提示 (Prompt) | 背景： 5 个引人入胜的第一人称射击视频游戏主题：<br>1. 废弃的军事基地：...<br>2. 赛博朋克城市：...<br>3. 外星飞船：...<br>4. 僵尸横行的小镇：...<br>5. 水下研究设施：...<br><br>选择其中一个主题，为新的第一人称射击视频游戏关卡编写一段具有挑战性且引人入胜的故事情节（一段）。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| 输出 (Output) | 在黑暗深渊的中心，坐落着一个破败的水下研究设施，它是人类野心及其灾难性后果的见证。笼罩在黑暗中，脉动着故障机器的嗡嗡声，并萦绕着溺亡灵魂的回声，该设施现在是深海噩梦的滋生地。玩家，一名装备先进潜水装备和实验性武器的精英海军陆战队员，被派往这个恐怖的水生领域冒险。凭借韧性和足智多谋，他们必须穿越危险的走廊，解开神秘的谜题，并对抗潜伏在深处的恐怖海洋怪物。从应对深海的巨大压力到智胜狡猾的水生捕食者，在这个未知的地下世界中的每一刻都在考验人类耐力和勇气的极限。 |

通过使用回退提示技术，可以提高提示的准确性，生成更具体、更具主题性且更引人入胜的输出。

### 思维链 (Chain of Thought - CoT)

思维链（Chain of Thought, CoT）提示是一种通过生成中间推理步骤来提高 LLM 推理能力的技术。这有助于 LLM 生成更准确的答案。

**CoT 的优点**
*   省力且非常有效，适用于现成的 LLM（无需微调）。
*   可获得可解释性，可以看到所遵循的推理步骤。
*   在不同 LLM 版本之间转换时提高了鲁棒性。

**CoT 的缺点**
*   LLM 响应包含思维链推理，意味着更多的输出令牌，导致更高的预测成本和更长的响应时间。

**示例：数学问题的缺陷 (没有 CoT)**
LLM 经常在数学任务上遇到困难，可能提供不正确的答案。

**表 11：尝试解决数学问题的提示示例 (无 CoT)**

| 字段         | 值                                                              |
| :----------- | :-------------------------------------------------------------- |
| 提示 (Prompt) | 当我 3 岁时，我的伴侣是我年龄的 3 倍。现在，我 20 岁了。我的伴侣多大了？ |
| 输出 (Output) | 63 岁                                                           |

这个答案显然是错误的。

**零样本思维链 (Zero-shot CoT)**
简单地添加触发短语（如 "让我们一步一步地思考"）可以引出推理过程。

**表 12：思维链提示示例 (零样本 CoT)**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 提示 (Prompt) | 当我 3 岁时，我的伴侣是我年龄的 3 倍。现在，我 20 岁了。我的伴侣多大了？让我们一步一步地思考。                                                                                                                                                                                                                                                                                                                                                                                                                          |
| 输出 (Output) | 1. 当我 3 岁时，我的年龄是 3 岁。<br>2. 那时我伴侣的年龄是我年龄的 3 倍，这意味着我伴侣的年龄是 3 * 3 = 9 岁。<br>3. 现在，我 20 岁了，这意味着自我 3 岁以来，我的年龄增加了 20 - 3 = 17 岁。<br>4. 由于我 3 岁时伴侣的年龄是 9 岁，并且我的年龄增加了 17 岁，那么我伴侣的年龄也一定增加了 17 岁。<br>5. 因此，我伴侣当前的年龄是 9 + 17 = 26 岁。<br><br>所以，我的伴侣是 26 岁。 |

现在最终答案是正确的。这是因为我们明确指示 LLM 解释每个步骤。

**少样本思维链 (Few-shot CoT)**
当与单样本或少样本结合时，思维链提示可能非常强大。提供一个期望的推理过程的示例，可以指导模型为新问题遵循类似的结构和逻辑。

**表 13：带单样本的思维链提示示例 (少样本 CoT)**

| 字段         | 值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| :----------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 提示 (Prompt) | **问：** 当我哥哥 2 岁时，我的年龄是他年龄的两倍。现在我 40 岁了。我哥哥多大了？让我们一步一步地思考。<br>**答：** 当我哥哥 2 岁时，我是 2 * 2 = 4 岁。年龄差是 2 岁，我更大。现在我 40 岁了，所以我哥哥是 40 - 2 = 38 岁。答案是 38。<br><br>**问：** 当我 3 岁时，我的伴侣是我年龄的 3 倍。现在，我 20 岁了。我的伴侣多大了？让我们一步一步地思考。<br><br>**答：**                                                                                                                                                                                                                                                                                            |
| 输出 (Output) | 当我 3 岁时，我的伴侣是 3 * 3 = 9 岁。年龄差是 6 岁，我的伴侣更大。现在我 20 岁了，所以我的伴侣是 20 + 6 = 26 岁。答案是 26。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**CoT 的应用场景**
*   代码生成：将请求分解为步骤，并将步骤映射到代码行。
*   合成数据创建：基于种子信息（如产品标题）引导模型生成描述。
*   任何可以通过"逐步阐述"来解决的任务。

### 自我一致性 (Self-consistency)

**自我一致性原理**
自我一致性（Self-consistency）结合了采样和多数投票来生成多样化的推理路径，并选择最一致的答案。它提高了 LLM 生成响应的准确性和连贯性。

**工作原理**
1.  **生成多样化的推理路径**: 多次向 LLM 提供相同的提示（通常使用较高的温度）。
2.  **从每个生成的响应中提取答案**。
3.  **选择最常见的答案** (多数投票)。

**权衡**
自我一致性以计算成本（多次运行提示）换取了鲁棒性和更可靠的答案。

## 代码提示

LLM 在处理与代码相关的任务时非常有用。以下是一些常见的代码提示类型：

### 编写代码的提示
直接要求模型根据描述或需求生成代码片段或完整程序。

### 解释代码的提示
提供一段代码，要求模型解释其功能、逻辑或特定部分。

### 翻译代码的提示
提供一种编程语言的代码，要求模型将其翻译成另一种编程语言。

## 最佳实践

以下是一些通用的提示工程最佳实践：

### 提供示例
*   使用单样本或少样本提示来指导模型理解任务和期望的输出格式。
*   确保示例质量高、相关且多样化。

### 简洁设计
*   提示应清晰、简洁，避免不必要的词语或模糊性。
*   直接说明要求。

### 具体说明输出
*   明确指定所需的输出格式（如 JSON、Markdown 列表、特定风格）。
*   使用系统提示强制执行格式或约束。
*   如果需要特定长度或结构，请在提示中说明。