<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Poe</title>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Press Start 2P', cursive;
        }

        body {
            display: flex;
            height: 100vh;
            background-color: #c0c0c0;
            color: #000;
            font-size: 12px;
            image-rendering: pixelated;
        }

        .sidebar {
            width: 260px;
            background-color: #c0c0c0;
            display: flex;
            flex-direction: column;
            border-right: 2px solid #808080;
        }

        .sidebar-header {
            padding: 15px;
            background: #000080;
            color: #fff;
        }

        .new-chat-btn {
            background-color: #c0c0c0;
            color: #000;
            padding: 10px;
            margin: 10px;
            cursor: pointer;
            border: 2px outset #ffffff;
            font-size: 10px;
            text-align: center;
        }

        .new-chat-btn:active {
            border-style: inset;
        }

        .model-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .model-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 5px;
            cursor: pointer;
            border: 2px solid transparent;
            font-size: 10px;
        }

        .model-item:hover {
            background-color: #000080;
            color: #fff;
        }

        .model-item.active {
            background-color: #000080;
            color: #fff;
            border: 2px inset #ffffff;
        }

        .model-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #808080;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 8px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
        }

        .chat-header {
            padding: 10px;
            background: #000080;
            color: #fff;
            font-size: 10px;
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #ffffff;
        }

        .message {
            max-width: 80%;
            margin: 10px 0;
            padding: 10px;
            font-size: 10px;
            line-height: 1.5;
        }

        .user-message {
            margin-left: auto;
            background: #c0c0c0;
            border: 2px solid #808080;
        }

        .bot-message {
            background: #ffffff;
            border: 2px solid #808080;
        }

        .input-area {
            padding: 15px;
            background: #c0c0c0;
            border-top: 2px solid #808080;
        }

        .message-input {
            width: 100%;
            padding: 10px;
            border: 2px inset #ffffff;
            background: #ffffff;
            font-family: 'Press Start 2P', cursive;
            font-size: 10px;
            resize: none;
            height: 80px;
        }

        .status-bar {
            padding: 5px 10px;
            background: #c0c0c0;
            border-top: 2px solid #808080;
            font-size: 8px;
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">PIXEL POE</div>
        <button class="new-chat-btn">NEW CHAT</button>
        <div class="model-list">
            <!-- AI Models -->
            <div class="model-item" onclick="switchModel('GPT-4')">
                <div class="model-icon">G4</div>
                GPT-4
            </div>
            <div class="model-item" onclick="switchModel('PaLM')">
                <div class="model-icon">P</div>
                PaLM
            </div>
            <div class="model-item" onclick="switchModel('Grok')">
                <div class="model-icon">GK</div>
                Grok
            </div>
            <div class="model-item" onclick="switchModel('LLaMA')">
                <div class="model-icon">LL</div>
                LLaMA
            </div>
            <div class="model-item" onclick="switchModel('GPT-3')">
                <div class="model-icon">G3</div>
                GPT-3
            </div>
            <div class="model-item" onclick="switchModel('LaMDA')">
                <div class="model-icon">LA</div>
                LaMDA
            </div>
            <div class="model-item" onclick="switchModel('BLOOM')">
                <div class="model-icon">BL</div>
                BLOOM
            </div>
            <div class="model-item" onclick="switchModel('Stable-Diffusion')">
                <div class="model-icon">SD</div>
                Stable Diffusion
            </div>
            <div class="model-item" onclick="switchModel('DALL-E')">
                <div class="model-icon">DE</div>
                DALL-E
            </div>
            <!-- Add more models as needed -->
        </div>
    </div>

    <div class="main-content">
        <div class="chat-header">
            CHAT WITH <span id="current-model">GPT-4</span>
        </div>
        
        <div class="chat-container" id="chat-container">
            <div class="message bot-message">
                INITIALIZING SYSTEM...<br>
                MODEL: GPT-4<br>
                USER: FUD114514<br>
                TIME: 2025-03-23 08:56:18<br>
                STATUS: READY<br>
                <br>
                HOW MAY I ASSIST YOU?
            </div>
        </div>

        <div class="input-area">
            <textarea 
                class="message-input"
                placeholder="TYPE YOUR MESSAGE HERE..."
                onkeydown="if(event.keyCode === 13 && !event.shiftKey) { event.preventDefault(); sendMessage(); }">
            </textarea>
        </div>

        <div class="status-bar">
            <div>USER: FUD114514</div>
            <div>TIME: 2025-03-23 08:56:18</div>
            <div>MODEL: GPT-4</div>
        </div>
    </div>

    <script>
        const currentTime = "2025-03-23 08:56:18";
        const currentUser = "fud114514";

        function switchModel(modelName) {
            // Update active model styling
            document.querySelectorAll('.model-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // Update headers
            document.getElementById('current-model').textContent = modelName;

            // Add system message
            document.getElementById('chat-container').innerHTML = `
                <div class="message bot-message">
                    INITIALIZING SYSTEM...<br>
                    MODEL: ${modelName}<br>
                    USER: ${currentUser}<br>
                    TIME: ${currentTime}<br>
                    STATUS: READY<br>
                    <br>
                    HOW MAY I ASSIST YOU?
                </div>
            `;
        }

        function sendMessage() {
            const input = document.querySelector('.message-input');
            const message = input.value.trim();
            
            if (message) {
                const chatContainer = document.getElementById('chat-container');
                const currentModel = document.getElementById('current-model').textContent;

                // Add user message
                const userMessageDiv = document.createElement('div');
                userMessageDiv.className = 'message user-message';
                userMessageDiv.textContent = message.toUpperCase();
                chatContainer.appendChild(userMessageDiv);

                // Add bot response
                const botMessageDiv = document.createElement('div');
                botMessageDiv.className = 'message bot-message';
                botMessageDiv.textContent = `[${currentModel}] PROCESSING YOUR REQUEST...`;
                chatContainer.appendChild(botMessageDiv);

                // Clear input
                input.value = '';

                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }

        // Initialize first model as active
        document.querySelector('.model-item').classList.add('active');
    </script>
</body>
</html>