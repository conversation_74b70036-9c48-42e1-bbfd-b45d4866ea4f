# 文案逆向工程 SOP (Standard Operating Procedure) - 详细版

**版本:** 1.1
**日期:** 2023-10-27
**制定人:** [你的名字或部门]

## 1. 目的 (Purpose)

本SOP旨在规范通过大型语言模型（LLM）对优质文案进行逆向工程，提取其核心创作逻辑（提示词）的流程。目标是实现对成功文案模式的复刻、优化与创新，提升内容生产的效率和质量。

## 2. 范围 (Scope)

本SOP适用于所有需要从现有优秀文案中学习、借鉴并生成类似风格或结构新文案的场景。详细涵盖从文案收集、数据处理、LLM交互（两次）、提示词生成、优化到最终提示词构建的每一个环节。

## 3. 职责 (Responsibilities)

*   **操作员/文案分析师:** 严格按照本SOP规定的步骤执行，确保每个环节的操作准确无误，并对结果进行记录。

## 4. 先决条件与工具 (Prerequisites & Tools)

*   **工具:**
    *   可靠的文案来源渠道 (如竞品网站、广告库、社交媒体平台等)。
    *   文本编辑器 (支持 Markdown 语法高亮，如 VS Code, Typora, Obsidian 等)。
    *   访问已配置好的两个大型语言模型（LLM）环境：
        *   **LLM 1 (逆向模型):** 配置了用于“从文案推导提示词”的特定指令/能力的模型。
        *   **LLM 2 (优化模型):** 配置了用于“优化和改进提示词”的特定指令/能力的模型。
    *   预设的“文案逆向”基础提示词模板 (供 LLM 1 使用)。
    *   预设的“提示词优化”基础提示词模板 (供 LLM 2 使用)。
*   **知识:**
    *   熟练掌握 Markdown 基本语法。
    *   理解 LLM 基本原理及提示词工程（Prompt Engineering）概念。
    *   具备一定的文案判断和分析能力。

## 5. 操作流程 (Procedure)

---

### **步骤 1: 收集目标文案 (Collect Target Copy)**

1.  **动作:** 识别并收集你认为高质量、希望逆向分析其创作逻辑的目标文案。
2.  **标准:**
    *   文案应具有代表性、效果良好或风格独特。
    *   确保收集到的文案内容完整，无缺失。
    *   记录文案来源以备后续参考。
3.  **输出:** 原始文案素材 (文本、截图或其他格式)。

---

### **步骤 2: 清洗原始文案数据 (Clean Raw Copy Data)**

1.  **动作:** 对收集到的原始文案进行内容清洗。
2.  **细节:**
    *   去除所有非核心文案内容，如：网页导航元素、按钮文字（除非其本身是关键文案）、广告标签、图片描述（除非与文案强相关）、页脚信息、免责声明等。
    *   保留文案的主体内容、标题、副标题、段落、列表等结构。
    *   修正明显的输入错误或格式混乱（可选，取决于是否希望逆向包含这些错误）。
3.  **输出:** 纯净的文案文本内容。

---

### **步骤 3: 转换文案为 Markdown 格式 (Convert Copy to Markdown)**

1.  **动作:** 将**步骤 2** 输出的纯净文案文本，使用 Markdown 语法进行格式化。
2.  **标准:**
    *   使用 `#` `##` `###` 等表示标题层级。
    *   使用 `-` 或 `*` 创建无序列表，`1.` `2.` 创建有序列表。
    *   使用 `**文字**` 表示加粗，`*文字*` 表示斜体。
    *   使用 `>` 表示引用块。
    *   保持段落间的空行。
    *   目标是尽可能还原原文的视觉结构和强调重点。
3.  **输出:** `.md` 格式的、结构化、清晰的文案文件。**【关键中间产物 1】**

---

### **步骤 4: 输入文案到 LLM 1 进行初步逆向 (Input Copy to LLM 1 for Initial Reversal)**

1.  **动作:** 准备与 LLM 1 (逆向模型) 的交互。
2.  **细节:** 确认 LLM 1 已加载或配置了执行“文案到提示词”逆向分析的基础提示词或模式。
3.  **动作:** 构造向 LLM 1 的输入。通常包含：
    *   你的“文案逆向”基础提示词模板。
    *   **步骤 3** 输出的 Markdown 格式文案。
4.  **指令示例 (嵌入模板中):**
    ```
    # 任务：文案逆向工程

    请仔细分析以下使用 Markdown 格式编写的文案，并逆向推导出一个能够生成类似风格、结构、语气和核心信息的详细创作提示词 (Prompt)。请专注于提炼创作指令，而非简单复述原文。

    ## 待分析文案：

    [此处粘贴步骤 3 输出的 Markdown 文案内容]

    ## 输出要求：
    请生成一个结构化的 Prompt。
    ```
5.  **动作:** 将构造好的完整输入提交给 LLM 1 并执行。
6.  **输出:** LLM 1 生成的初步逆向提示词 (文本格式)。

---

### **步骤 5: 获取初步逆向提示词 (Obtain Initial Reversed Prompt)**

1.  **动作:** 接收并记录 LLM 1 的输出结果。
2.  **标准:** 检查输出是否是一个看起来像提示词的文本，描述了如何创作类似文案。
3.  **输出:** 初步逆向得到的提示词文本。**【关键中间产物 2】**

---

### **步骤 6: 输入初步提示词到 LLM 2 进行优化 (Input Initial Prompt to LLM 2 for Optimization)**

1.  **动作:** 准备与 LLM 2 (优化模型) 的交互。
2.  **细节:** 确认 LLM 2 已加载或配置了执行“提示词优化”的基础提示词或模式。
3.  **动作:** 构造向 LLM 2 的输入。通常包含：
    *   你的“提示词优化”基础提示词模板。
    *   **步骤 5** 输出的初步逆向提示词。
4.  **指令示例 (嵌入模板中):**
    ```
    # 任务：提示词优化

    请优化以下这个由 AI 初步生成的文案创作提示词。目标是使其更加清晰、结构化、具体、可操作，并减少模糊性，确保 AI 能更好地理解和执行指令，稳定生成高质量、符合原始意图的文案。

    ## 待优化提示词：

    [此处粘贴步骤 5 获取的初步逆向提示词]

    ## 优化要求：
    - 增强指令的明确性。
    - 结构化输出，可能使用标题、列表等。
    - 明确关键要素，如目标受众、核心卖点、语气风格、格式要求等。
    - 移除冗余或不清晰的表述。
    ```
5.  **动作:** 将构造好的完整输入提交给 LLM 2 并执行。
6.  **输出:** LLM 2 生成的优化后的提示词 (文本格式)。

---

### **步骤 7: 获取优化后的提示词 (Obtain Optimized Prompt)**

1.  **动作:** 接收并记录 LLM 2 的输出结果。
2.  **标准:** 检查输出的提示词是否比**步骤 5** 的版本更清晰、结构化、易于理解和执行。
3.  **输出:** 经过优化处理后的提示词文本。**【关键中间产物 3】**

---

### **步骤 8: 在优化后的提示词中添加输出示例 (Add Output Example to Optimized Prompt)**

1.  **动作:** 编辑**步骤 7** 输出的优化后提示词文本。
2.  **细节:**
    *   在提示词文本的末尾或适当位置，添加一个明确的标题，如 `### 输出示例 (Example Output)` 或类似标记。
    *   在此标题下方，完整粘贴 **步骤 3** 产出的那个 **清理并格式化为 Markdown 的原始文案**。
3.  **目的:** 这个示例作为 Few-shot learning 的一部分，为 LLM 提供一个具体的模仿目标，极大地提高生成结果的相关性和格式准确性。
4.  **输出:** 包含指令和完整输出示例的最终提示词草稿 (文本/Markdown 格式)。

---

### **步骤 9: （可选但推荐）测试与最终确定 (Test & Finalize)**

1.  **动作:** 使用**步骤 8**构建的包含示例的最终提示词，输入到一个有能力的LLM（可以是LLM 1、LLM 2或另一个通用模型）中进行测试。
2.  **细节:** 可以要求LLM基于此提示词生成一个关于*新主题*或*略微调整要求*的文案。
3.  **标准:** 评估生成结果是否在风格、结构、语气等方面与示例相似，且符合提示词的指令。根据测试结果，可能需要微调提示词内容（返回步骤 7 或 8）。
4.  **动作:** 一旦满意，将此经过验证、包含示例的提示词进行保存。
5.  **输出:** 一个完整的、经过验证的、可复用的最终文案创作提示词 ( `.md` 或文本格式)。**【最终产出】**

---

## 6. 预期成果 (Expected Outcome)

*   一个高质量、结构化、包含清晰指令和具体输出示例的最终文案创作提示词。
*   该提示词能够有效指导 LLM 生成与原始目标文案风格、结构、核心信息相似的新内容。

## 7. 备注与最佳实践 (Notes & Best Practices)

*   **模型依赖性:** 结果质量高度依赖于 LLM 1 和 LLM 2 的能力以及为其配置的基础提示词模板。
*   **迭代是关键:** 整个过程可能需要多次迭代，尤其是在优化和测试环节。
*   **示例质量:** 添加的示例（即原始清理后的文案）的质量和代表性至关重要。
*   **Prompt工程知识:** 对 Prompt Engineering 的理解越深，越能在优化环节做出有效调整。
*   **版本控制:** 对最终生成的提示词进行版本管理，记录其来源文案和优化过程。