!function(){"use strict";var n,t,e=function(){var n=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(n&&n.responseStart>0&&n.responseStart<performance.now())return n},r=function(n){if("loading"===document.readyState)return"loading";var t=e();if(t){if(n<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||n<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||n<t.domComplete)return"dom-content-loaded"}return"complete"},i=function(n){var t=n.nodeName;return 1===n.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},o=function(n,t){var e="";try{for(;n&&9!==n.nodeType;){var r=n,o=r.id?"#"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(e.length+o.length>(t||100)-1)return e||o;if(e=e?o+">"+e:o,r.id)break;n=r.parentNode}}catch(n){}return e},u=-1,a=function(){return u},f=function(n){addEventListener("pageshow",(function(t){t.persisted&&(u=t.timeStamp,n(t))}),!0)},c=function(){var n=e();return n&&n.activationStart||0},d=function(n,t){var r=e(),i="navigate";return a()>=0?i="back-forward-cache":r&&(document.prerendering||c()>0?i="prerender":document.wasDiscarded?i="restore":r.type&&(i=r.type.replace(/_/g,"-"))),{name:n,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},v=function(n,t,e){try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var r=new PerformanceObserver((function(n){Promise.resolve().then((function(){t(n.getEntries())}))}));return r.observe(Object.assign({type:n,buffered:!0},e||{})),r}}catch(n){}},s=function(n,t,e,r){var i,o;return function(u){t.value>=0&&(u||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=function(n,t){return n>t[1]?"poor":n>t[0]?"needs-improvement":"good"}(t.value,e),n(t))}},l=function(n){requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))},m=function(n){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&n()}))},h=function(n){var t=!1;return function(){t||(n(),t=!0)}},g=-1,p=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},y=function(n){"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===n.type?n.timeStamp:0,T())},b=function(){addEventListener("visibilitychange",y,!0),addEventListener("prerenderingchange",y,!0)},T=function(){removeEventListener("visibilitychange",y,!0),removeEventListener("prerenderingchange",y,!0)},M=function(){return g<0&&(g=p(),b(),f((function(){setTimeout((function(){g=p(),b()}),0)}))),{get firstHiddenTime(){return g}}},E=function(n){document.prerendering?addEventListener("prerenderingchange",(function(){return n()}),!0):n()},L=[1800,3e3],P=function(n,t){t=t||{},E((function(){var e,r=M(),i=d("FCP"),o=v("paint",(function(n){n.forEach((function(n){"first-contentful-paint"===n.name&&(o.disconnect(),n.startTime<r.firstHiddenTime&&(i.value=Math.max(n.startTime-c(),0),i.entries.push(n),e(!0)))}))}));o&&(e=s(n,i,L,t.reportAllChanges),f((function(r){i=d("FCP"),e=s(n,i,L,t.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,e(!0)}))})))}))},S=[.1,.25],w=0,C=1/0,F=0,D=function(n){n.forEach((function(n){n.interactionId&&(C=Math.min(C,n.interactionId),F=Math.max(F,n.interactionId),w=F?(F-C)/7+1:0)}))},k=function(){return n?w:performance.interactionCount||0},O=function(){"interactionCount"in performance||n||(n=v("event",D,{type:"event",buffered:!0,durationThreshold:0}))},B=[],j=new Map,I=0,A=[],N=function(n){if(A.forEach((function(t){return t(n)})),n.interactionId||"first-input"===n.entryType){var t=B[B.length-1],e=j.get(n.interactionId);if(e||B.length<10||n.duration>t.latency){if(e)n.duration>e.latency?(e.entries=[n],e.latency=n.duration):n.duration===e.latency&&n.startTime===e.entries[0].startTime&&e.entries.push(n);else{var r={id:n.interactionId,latency:n.duration,entries:[n]};j.set(r.id,r),B.push(r)}B.sort((function(n,t){return t.latency-n.latency})),B.length>10&&B.splice(10).forEach((function(n){return j.delete(n.id)}))}}},q=function(n){var t=self.requestIdleCallback||self.setTimeout,e=-1;return n=h(n),"hidden"===document.visibilityState?n():(e=t(n),m(n)),e},R=[200,500],x=function(n,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},E((function(){var e;O();var r,i=d("INP"),o=function(n){q((function(){n.forEach(N);var t=function(){var n=Math.min(B.length-1,Math.floor((k()-I)/50));return B[n]}();t&&t.latency!==i.value&&(i.value=t.latency,i.entries=t.entries,r())}))},u=v("event",o,{durationThreshold:null!==(e=t.durationThreshold)&&void 0!==e?e:40});r=s(n,i,R,t.reportAllChanges),u&&(u.observe({type:"first-input",buffered:!0}),m((function(){o(u.takeRecords()),r(!0)})),f((function(){I=k(),B.length=0,j.clear(),i=d("INP"),r=s(n,i,R,t.reportAllChanges)})))})))},H=[],V=[],W=0,_=new WeakMap,z=new Map,G=-1,J=function(n){H=H.concat(n),K()},K=function(){G<0&&(G=q(Q))},Q=function(){z.size>10&&z.forEach((function(n,t){j.has(t)||z.delete(t)}));var n=B.map((function(n){return _.get(n.entries[0])})),t=V.length-50;V=V.filter((function(e,r){return r>=t||n.includes(e)}));for(var e=new Set,r=0;r<V.length;r++){var i=V[r];U(i.startTime,i.processingEnd).forEach((function(n){e.add(n)}))}var o=H.length-1-50;H=H.filter((function(n,t){return n.startTime>W&&t>o||e.has(n)})),G=-1};A.push((function(n){n.interactionId&&n.target&&!z.has(n.interactionId)&&z.set(n.interactionId,n.target)}),(function(n){var t,e=n.startTime+n.duration;W=Math.max(W,n.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(e-i.renderTime)<=8){(t=i).startTime=Math.min(n.startTime,t.startTime),t.processingStart=Math.min(n.processingStart,t.processingStart),t.processingEnd=Math.max(n.processingEnd,t.processingEnd),t.entries.push(n);break}}t||(t={startTime:n.startTime,processingStart:n.processingStart,processingEnd:n.processingEnd,renderTime:e,entries:[n]},V.push(t)),(n.interactionId||"first-input"===n.entryType)&&_.set(n,t),K()}));var U=function(n,t){for(var e,r=[],i=0;e=H[i];i++)if(!(e.startTime+e.duration<n)){if(e.startTime>t)break;r.push(e)}return r},X=[2500,4e3],Y={},Z="undefined"!=typeof window?window:void 0,$="undefined"!=typeof globalThis?globalThis:Z,nn=null==$?void 0:$.navigator;null==$||$.document,null==$||$.location,null==$||$.fetch,null!=$&&$.XMLHttpRequest&&"withCredentials"in new $.XMLHttpRequest&&$.XMLHttpRequest,null==$||$.AbortController,null==nn||nn.userAgent;var tn=null!=Z?Z:{},en={onLCP:function(n,t){!function(n,t){t=t||{},E((function(){var e,r=M(),i=d("LCP"),o=function(n){t.reportAllChanges||(n=n.slice(-1)),n.forEach((function(n){n.startTime<r.firstHiddenTime&&(i.value=Math.max(n.startTime-c(),0),i.entries=[n],e())}))},u=v("largest-contentful-paint",o);if(u){e=s(n,i,X,t.reportAllChanges);var a=h((function(){Y[i.id]||(o(u.takeRecords()),u.disconnect(),Y[i.id]=!0,e(!0))}));["keydown","click"].forEach((function(n){addEventListener(n,(function(){return q(a)}),{once:!0,capture:!0})})),m(a),f((function(r){i=d("LCP"),e=s(n,i,X,t.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,Y[i.id]=!0,e(!0)}))}))}}))}((function(t){var r=function(n){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:n.value};if(n.entries.length){var r=e();if(r){var i=r.activationStart||0,u=n.entries[n.entries.length-1],a=u.url&&performance.getEntriesByType("resource").filter((function(n){return n.name===u.url}))[0],f=Math.max(0,r.responseStart-i),c=Math.max(f,a?(a.requestStart||a.startTime)-i:0),d=Math.max(c,a?a.responseEnd-i:0),v=Math.max(d,u.startTime-i);t={element:o(u.element),timeToFirstByte:f,resourceLoadDelay:c-f,resourceLoadDuration:d-c,elementRenderDelay:v-d,navigationEntry:r,lcpEntry:u},u.url&&(t.url=u.url),a&&(t.lcpResourceEntry=a)}}return Object.assign(n,{attribution:t})}(t);n(r)}),t)},onCLS:function(n,t){!function(n,t){t=t||{},P(h((function(){var e,r=d("CLS",0),i=0,o=[],u=function(n){n.forEach((function(n){if(!n.hadRecentInput){var t=o[0],e=o[o.length-1];i&&n.startTime-e.startTime<1e3&&n.startTime-t.startTime<5e3?(i+=n.value,o.push(n)):(i=n.value,o=[n])}})),i>r.value&&(r.value=i,r.entries=o,e())},a=v("layout-shift",u);a&&(e=s(n,r,S,t.reportAllChanges),m((function(){u(a.takeRecords()),e(!0)})),f((function(){i=0,r=d("CLS",0),e=s(n,r,S,t.reportAllChanges),l((function(){return e()}))})),setTimeout(e,0))})))}((function(t){var e=function(n){var t,e={};if(n.entries.length){var i=n.entries.reduce((function(n,t){return n&&n.value>t.value?n:t}));if(i&&i.sources&&i.sources.length){var u=(t=i.sources).find((function(n){return n.node&&1===n.node.nodeType}))||t[0];u&&(e={largestShiftTarget:o(u.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:u,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(n,{attribution:e})}(t);n(e)}),t)},onFCP:function(n,t){P((function(t){var i=function(n){var t={timeToFirstByte:0,firstByteToFCP:n.value,loadState:r(a())};if(n.entries.length){var i=e(),o=n.entries[n.entries.length-1];if(i){var u=i.activationStart||0,f=Math.max(0,i.responseStart-u);t={timeToFirstByte:f,firstByteToFCP:n.value-f,loadState:r(n.entries[0].startTime),navigationEntry:i,fcpEntry:o}}}return Object.assign(n,{attribution:t})}(t);n(i)}),t)},onINP:function(n,e){t||(t=v("long-animation-frame",J)),x((function(t){var e=function(n){var t=n.entries[0],e=_.get(t),i=t.processingStart,u=e.processingEnd,a=e.entries.sort((function(n,t){return n.processingStart-t.processingStart})),f=U(t.startTime,u),c=n.entries.find((function(n){return n.target})),d=c&&c.target||z.get(t.interactionId),v=[t.startTime+t.duration,u].concat(f.map((function(n){return n.startTime+n.duration}))),s=Math.max.apply(Math,v),l={interactionTarget:o(d),interactionTargetElement:d,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:s,processedEventEntries:a,longAnimationFrameEntries:f,inputDelay:i-t.startTime,processingDuration:u-i,presentationDelay:Math.max(s-u,0),loadState:r(t.startTime)};return Object.assign(n,{attribution:l})}(t);n(e)}),e)}};tn.__PosthogExtensions__=tn.__PosthogExtensions__||{},tn.__PosthogExtensions__.postHogWebVitalsCallbacks=en,tn.postHogWebVitalsCallbacks=en}();
//# sourceMappingURL=web-vitals.js.map
