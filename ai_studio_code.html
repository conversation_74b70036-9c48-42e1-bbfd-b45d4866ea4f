<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PYTHIA/USD (1H) 专业K线图</title>
    <style>
        /* 关键CSS: 确保图表占满整个屏幕，无边距 */
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 防止出现滚动条 */
            background-color: #131722; /* 背景色与图表主题一致 */
        }
        #pythia_chart_container {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>

    <!-- 唯一的HTML元素：用于承载图表的容器 -->
    <div id="pythia_chart_container"></div>

    <!-- 引入 TradingView 官方的 tv.js 库 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    
    <!-- 初始化并配置图表小部件 -->
    <script type="text/javascript">
        new TradingView.widget({
            // --- 核心配置 ---
            "container_id": "pythia_chart_container", // 绑定到上面的div
            "autosize": true,                         // 关键：让图表自动填充容器
            "symbol": "CRYPTO:PYTHIAUSD",             // 【关键】锁定的交易对
            "interval": "60",                         // 【关键修改】默认时间周期: "60" 代表1小时
            "locale": "zh_CN",                        // 语言: 中文
            "theme": "dark",                          // 主题: 深色
            "style": "1",                             // K线样式

            // --- 功能开关 ---
            "allow_symbol_change": false,             // 【关键】禁止切换交易对，实现锁定功能
            "hide_side_toolbar": false,               // 显示左侧的绘图工具栏
            
            // --- 预加载的技术指标 ---
            "studies": [
                "Volume@tv-basicstudies"              // 默认加载成交量指标
            ],
            
            // --- 深度自定义图表外观 ---
            "overrides": {
                // K线居中 (在图表右侧留出15%的空白)
                "paneProperties.rightMargin": 15,

                // 主要窗格和K线颜色，打造专业外观
                "paneProperties.background": "#131722",
                "paneProperties.vertGridProperties.color": "#363c4e",
                "paneProperties.horzGridProperties.color": "#363c4e",
                "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350"
            }
        });
    </script>

</body>
</html>