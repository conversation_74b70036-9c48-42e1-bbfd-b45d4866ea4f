# 如何看待杨振宁认为有造物主的存在？

原视频存在刻意剪辑，故替换，这里提供完整的视频 2021.06.17.18：00 有朋友修改指正非常欢迎，但还是保留下原来视频以作对比更合适，我再重新…

杨振宁所说的，是无数科学家都面临的本质问题：宇宙中为什么有那么多精妙绝伦的设定。

我看很多大佬已经就杨振宁先生本人的故事和研究做了介绍， 我就不妨剑走偏锋，从“智能产生”的角度，给大家从另一个角度展示“宇宙中精妙绝伦的设定”。

先说结论：在我们的宇宙中，智慧生命的产生并不是偶然，而是一个大概率事件。也就是说，如果倒带回去几十亿年，把宇宙的发展重演无数次，那么几乎每一个宇宙的版本里都会有智慧生命的存在。

为什么这么有信心呢？

因为我们的宇宙有充足的逻辑资源，允许我们对环境做非常精细的压缩拟合。

计算机科学家沃尔夫勒姆对这个问题有非常本质的实验和论证，我曾经写文章详细介绍过他的研究，放在这里，希望帮你对“造物主”有更深的理解。

***

## 最 Deep 的 Seek：AI 的“终极设计图”是什么样子？

文｜史中

我写这篇文章的时候，正值 DeepSeek 撕开防线，和人类最强的智能 ChatGPT 隔空对峙。

DeepSeek R1 的凶猛，并不在于它超越了对手 o1 模型的逻辑推理能力（实际上只是几乎追平），而在于它实现这些能力，只需要十分之一的成本。

换句话说，它的智能密度相当之高。

具体实现方式，DeepSeek 已经公布了论文，很多大神也做了详细解读，不多说了。

今天咱们试着潜入智能的最深处，讨论三个层层递进的话题：

1.  用“显微镜”看，智能的微观结构究竟是什么？
2.  什么东西在决定“智能的密度”？
3.  我们有办法做出智能密度达到极限的 AI 吗？

这么深刻的话题，中哥确实没能力自己构建理论体系。给你介绍一个高手，他就是计算机科学家，被称为**目前活着的最聪明的人**——**史蒂芬·沃尔夫勒姆 (Stephen Wolfram)**。

这篇文章的核心观点，就是来自这位大神。

*(图片：Stephen Wolfram)*

友情提示，接下来 20 分钟，我们要和最聪明的大脑打交道，也许有些观念过于抽象，需要一些额外的“思维垫脚石”。

有些垫脚石乍看和主题无关。但相信我，它们都是通向最终结论所必须的。如果卡住，延伸开去琢磨一下，再回到主题，最后一定会有收益。

让我们开始吧！

## （一）智能的莲花开在淤泥上

沃尔夫勒姆从一个简单的人工智能神经网络开始：

*(图片：二极管风格的函数图)*

上图是一个很有“二极管”风格的函数，它其实和人脑很像。

你可以理解为它就是你的大脑在恋爱中所做的判断：当 x 满足某些条件时，你会上头，和 ta 结婚，此时 f[x]=1；当 x 不满足这个条件时，你会下头，和 ta 分手，此时 f[x]=0。

现在，我们想“克隆”一下自己的大脑——用人工智能来模拟和这个“目标函数”一模一样的操作。

该怎么做呢？

我们可以搞一个神经网络，然后从这个目标函数上做数据采样，用这些采样去训练神经网络。

经过 1000 轮训练，最后出来的结果是酱的：
一堆神经元分层传导，每条连线都定了不同的参数权重，它们形成了一个类似于方程的计算系统。你给出一个 x 的值，它就能给你算出 f[x]的值。

*(图片：训练后的神经网络结构图)*

你可以代入数值验证一下，它虽然不和原始方程精准重合，但确实是非常接近的——这是个好使的人工智能。

不过这里有两个问题：

1.  **在训练开始时，你无法预知最终每个参数会是什么。**

    *(图片：函数曲线在训练中不断接近目标函数的过程)*
    上图显示从第1轮到 1000 轮的训练过程中，函数曲线不断接近目标函数的过程。由于参数是在一步步迭代中确定的，所以无法预知后续如何变化。

2.  **训练结束后，你很难讲出每个具体神经元参数对最终结果的影响是什么。**

    *(图片：X变化时中间参数取值变化图)*
    随着 X 的变化，中间参数取值也在变化。在曲线的转折点，你能看出发生了变化，但很难说清楚每根线具体弯折的意义。

所以整个训练过程有点儿“解释不清楚”：你只知道最后这个神经网络能用，但是，一不知道它是怎么能用的；二不知道它是怎么变成能用的。。。

这不是很奇怪吗？

其实也没那么怪。

我猜你上学时，一定有过这样的经历：试卷上一道题，你能直接说出正确答案。同桌问你怎么做的，你就是没办法拆成他能理解的步骤给他讲明白。

还有的人，可以控制自己的耳朵动。可是你问他具体怎么控制，他肯定没办法和你说清楚，因为这是神经系统整体的运作，无法用语言逻辑拆分。

沃尔夫勒姆的意思是：

> “解释”这件事情，根本就是个幻觉。

比如我要给你解释：“汽车为什么会走？”
我可以从宏观层面解释：
能源的化学能转化成了动能，汽车就能走。
但你还不明白，让我详细说说。
于是我从更细节的层面解释：
发动机的四个冲程让燃料燃烧，推动了传动杆，传动杆又连通了底盘和车轮，车轮转动，与地面摩擦，汽车向前。
但你还不明白，让我再详细说说的。
于是，我从微观层面解释：
分子层面的化学反应，刚体物理的诸多性质。
但你还不明白，让我继续深入解释。
于是我发现，细微到一定程度，解释就失去意义了：
汽车的微观层面就是一堆原子。。。它们在遵循特定的规则震动。无数震动效果的总和就是汽车向前移动。

这算啥解释？

沃尔夫勒姆在上个世纪就提出一个简洁又凶残的洞见：**世界的本质是“计算不可约”的**。（这是迄今为止对我震撼最大的认知之一，我在《活成了狗》中也详细介绍过。）

简单说就是：
1.  微观粒子遵循基本规律；
2.  宏观世界是微观粒子的直接累积，无法被简化。

但我们的大脑一厢情愿希望找到“简单解释”。
哪怕这些解释不是100%事实，而是舍弃一部分事实之后，形成粗简的“故事”，以便大脑（可怜的）计算力能够与其他的故事类比起来，以此才能对改造世界的工作进行一些（不一定正确的）指导。

越往微观层面走，我们保留的事实就越多，故事就越不好理解，但离真相更近。
越往宏观层面走，我们舍弃的事实越多，故事就越好理解，但离真相越远。

这种感觉很奇妙。它暗示：我们的世界就像一朵莲花，花瓣分明，艳丽异常，但追根溯源，却根植在一坨烂泥上。

*(图片：莲花)*

回到我们的主题。
神经网络每个神经元是干啥的，之所以不好解释，就是因为我们试图用微观事实在宏观上拼出一个的“简化的”故事，这本质上是无法做到的。

说了半天，意思就是。。。此题无解吗？？！！

诶，沃尔夫勒姆的凶悍之处正在于此。他的观点是：
> 通过深刻理解智能为什么不能解释，可以指导人类造出更厉害的 AI！

下面扶稳坐好，我们从最微观的一砖一瓦开始，一点点描绘这幅图景。

## （二）大脑是“离散”的！

计算机是会死机的。
如果程序里存在嵌套的逻辑，计算机就只能一直算一直算，死而后已。
之所以这样，是因为它试图用有限的资源模拟出一个“无限的数学空间”。
这个数学空间里，任何东西都是连续的。

例如，一个小数字都可以分成更小的数字：
0.001 够小了吧，你给 1 前面再塞个 0，它就是 0.0001，妥妥更小。
同理，任何一个大数字都可以组成更大的数字。
在这个数学空间里，你可以砍一刀，再砍一刀，无限逼近但永远也砍不完，跟拼多多一个德性。

*(图片：无限分割示意图)*

实际上，现代数学的危机与荣光，微积分、群论这种高深的理论，都必须建立在各种极限概念之上。它们共同构成了“形式计算”的恢宏大厦。

但是，这个完美的数学空间只存在于纯粹逻辑之中。

最近一百年的科学证据已经疯狂暗示：**真实宇宙的基本结构不是连续的，也不是无限可分的，而是“离散的”**。

你可以不严谨地把宇宙想象成一个屏幕。
在最小的尺度上看，全是像素点。一个粒子要么在 1 号点位，要么在 2 号点位，不可能在中间的 1.5 号点位，因为宇宙的基本结构决定了就没有这么个“像素”。
一个粒子从 1 号位置移动到 2 号位置，不可能是“滑”过去，必须从 1 号位置消失，然后瞬间在 2 号位置出现。

*(图片：粒子离散移动示意图)*

沃尔夫勒姆想强调的是，在这种不连续的底层结构上进化出来的大脑，也必然“遗传”了这个离散化的底色。

现实情况也在印证，大脑不是计算机：
首先，我们的大脑真的不擅长算数，两位数都容易算错，而计算机最擅长的就是算数。
其次，大脑如果真的是计算机，一定会经常死机，但大脑从不死机。

残酷的自然选择，要求我们的大脑必须具备“反智”的能力：**把任何问题都快速坍缩成一个确定的答案，同时可以不要求准确！**

原始人在野外看到一个长条的物体，第一要务不是搞清楚它到底是蛇还是藤，第一要务是——跑。

于是，下次你听到“不买华为是汉奸”，“日本人都该死”之类的二极管论断时，可以更加心平气和。
因为大脑本来就是这样工作的，它进化出来是为了在有限的资源下帮人做出决定的，而不是用来探寻真相的。

接下来的问题是：大脑究竟是怎么通过“离散化的结构”给出“又快又不准”的答案呢？

是时候请出“元胞自动机”了。

## （三）宇宙里的“逻辑碎片”

元胞自动机最早是冯·诺依曼提出来的设想。
简单来说就是把世界简化成一个充满格子的平面，然后给出一定的规则，再给出一个初始条件。然后就像上帝一样放手不管，只是隔空俯瞰这个世界的演化。

示例如下：

*(图片：元胞自动机示例 - 初始条件、规则、计算过程)*
第一排：初始条件 第二排：规则 第三排：通过规则对下一行进行计算的过程

沃尔夫勒姆把元胞自动机玩出了花，他强烈地相信元胞自动机里暗示了宇宙和生命的密码。

我们一直在强调的**“计算不可约化”**原理，也是从元胞自动机里观察出来的。

*(图片：规则30元胞自动机演化图)*
这个规则叫做“30 号规则”，从初始的一个黑点，可以衍生出复杂的完全没有规律的图案。

现在，他设想了一个“三色”元胞自动机。意思就是每个格子可以填入两种颜色：红、蓝，加上空白时的白色，一共是三色。
上面一排的三个格子的状态，决定了正下面一个格子的状态。
也就是说，要让这个元胞自动机启动，你只需要设定一个由 27 条规则（也就是 3³ 条）组成的规则集，还有第一行的初始状态。

*(图片：三色元胞自动机规则集示例)*
这就是一种规则集（包含 27 条规则）。

任务来了：假设初始状态只有正中一个红格子，那么有没有一套规则，可以让这个系统正好演化 40 步，然后就停止了呢？

就像下图：
*(图片：目标为40步的演化示意图)*

由于计算不可约化，没有算法可以预知答案，只能进行实验。
而且，这里有超过 7 万亿种组合情况（3²⁷），枚举法太慢。

有一种比较聪明的方法：
在 27 个规则中，每次随机突变一个，如果生命长度接近 40，就保留这个突变；如果生命长度没变或者原理目标，就不保留。
这个方法叫做“连续随机突变”。

就这样，经过 300 多次的尝试，突然碰到了一套规则，让生命的长度恰好是 40。

*(图片：规则迭代过程，显示规则变化)*
上图的每一行都只显示了 27 条规则的输出结果（输入没显示，和之前的那张图里顺序相同），从 27 个白格子开始逐步迭代某些规则的结果。右侧的数字显示了两排之间发生变化的规则数量。

下面这张图就是随着规则不断进行突变，最终结果不断接近目标的过程。
*(图片：演化长度接近目标40的过程图)*

但是，如果你问我为什么 40 的生命长度对应这套规则，我无法解释，因为是我“碰”出来的。

> “即便不能解释，但它真的好使。”

这句话是否似曾相识？
没错，这个特点和神经网络一！毛！一！样！

看到这，你有没有一种不踏实的感觉？上学时老师可不样这么解题啊。万一我没“碰”出来正确的方法，怎么办？

为了打消你的疑虑，沃尔夫勒姆又多做了几次。由于每次的随机性不同，他找到很多套规则，结果都可以是 40。
以下就是五种情况：

*(图片：五种不同的规则集都能得到40步的结果)*

这说明啥？说明正确答案不止一个，想要碰出来，也没那么难。

这里有一个隐藏的关键前提，沃尔夫勒姆选择了“三色元胞自动机”，它在逻辑上就内涵了 7 万亿种情况。
如果选择“二色元胞自动机”，则一共就有 256 种规则组合，这里面的可能性就大大降低了。
我甚至可以都列出来给你：

*(图片：二色元胞自动机规则可能性)*

元胞自动机里设置的颜色种类，在某种意义上对应了宇宙空间中的“维度”概念。三维宇宙，就对应着元胞自动机的三色。
通过元胞自动机你可以感受到一个类比：三维宇宙比二维宇宙的逻辑丰富性可是大了不止一点半点。

为啥咱们的宇宙是三维的？
很可能是因为二维宇宙可能无法产生复杂生命，也就无法追问宇宙为什么是二维的。

*(图片：沃尔夫勒姆的宇宙模型-网状结构)*
根据沃尔夫勒姆的宇宙模型，空间可以理解为一种由点线组成的网状结构。维度越高，点之间的连线就越多，从 A 到 B 可能的路径也更多，也就是逻辑更丰富。

由此，我们能得到如下三条启示：
1.  我们的宇宙充满了逻辑碎片。
2.  简单的逻辑碎片通过排列组合，可以成为拥有特定功能的工具。
3.  用逻辑碎片组合出特定工具的方法并不需要多高的智慧，仅仅通过“突变”+“筛选”就可以。

而智能系统没啥神秘的，本质上就是一个可以实现特定功能的（复杂一点儿的）工具嘛。

既然这么说，用类似的方法，也可以做出一个大脑咯？！

可以试试。

## （四）用“小方块”做出一个大脑

为了方便你理解，先做一个小小的热身。
我们使用一个“二维元胞自动机”。但这次我们不对规则进行突变，而是给定如下两套规则。（沃尔夫勒姆给他们的编号是规则 4 和规则 146）

*(图片：规则4和规则146定义)*

在我们的元胞自动机中，具体每一个格子使用规则 4 还是规则 146，由突变决定。
为了突出展示，我们把采用规则 4 的格子用绿色填充，把采用规则 146 的格子用粉色填充。
以下是几个示例：

*(图片：混合规则的元胞自动机示例)*

下面我们开始实操：
假设我们的目的是让这个元胞自动机活 50 步。然后我们从纯绿色开始，在随机的地方把绿色变成粉色，筛选距离 50 步更近的突变。
不出所料，我们能碰出来符合条件的突变：

*(图片：达到50步演化的突变结果)*

而且还不止一种。
下图就是另一些例子：

*(图片：其他达到50步演化的结果)*

下图显示的是很多次实验中，找到结果分别所需花费的步骤。

*(图片：找到结果所需步骤的分布图)*

好，你应该已经明白了基本玩法，热身结束。
接下来我们把这个元胞自动机做一点儿小改动。
首先，我们把结构改成蜂窝状，每个细胞的状态只由最近的两个决定。
然后，我们使用如下两套规则：

*(图片：与&异或逻辑函数规则)*

略懂逻辑学的童鞋肯定看出来了，这不就是两个基础的逻辑函数么：与&异或。
这里我们用绿色代表“与”，用橘色代表“异或”。

*(图片：蜂窝状结构与/异或规则示意)*

厉害的来了！使用“与”、“异或”排列组合，还可生成无数其他规则，有种“一生二二生三三生万物”的意思。
系统在随机突变中，理论上会制造出各种函数！

别急，我们一步步来。
先采用我们熟悉的方法进行突变，让这个系统存活 30 步。显然可以做到：

*(图片：蜂窝AI存活30步的演化)*

但我们已经不满足于这一点了，接下来上点儿强度：
我们想让系统从某个特定细胞出发，30 步之后，恰好到达另一个特定细胞。
通过突变，可能创造出这样的系统吗？
事实证明，可以！

*(图片：特定输入到特定输出的30步演化)*

我们继续上强度，让这个系统变得更“智能”：
能不能用一个系统同时满足多个要求？
例如，点亮第 0 排的 x 细胞，就一定能点亮第 30 排的 y 细胞？
就像下面这样：

*(图片：满足多个输入输出要求的蜂窝AI系统)*

注意，要用一套固定的系统（绿橘色块位置不能动）来完成这些不同的任务哦！
事实证明，依然可以。上面展示的这个系统就可以嘛！

说到这，你有没有发现什么？
你再仔细看看上面那张图。没错，我们训练出了最早给你展示的那个神经网络 f[x]。

这个用元胞自动机做出来的像“蜂巢”的 AI 和我们之前的那个神经网络是几乎等价的。

*(图片：蜂巢AI训练过程的中间形态与对应方程)*
下图是这个系统训练过程中各个“中间形态”的突变点位和与之对应的方程 f[x]：

但是，敲黑板！这个元胞自动机可不是经典的神经网络结构，它们不仅是长得不一样，在基础结构上也是不一样的。

经典的神经网络人工智能也是“离散”的，不过每个神经元的权重最初都是通过形式计算得到的，可能是循环的、无限的小数。
为了不让 AI 在运行时死机，必须强制把他们小数点后面的位数切断才能用。属于是“强制离散化”。

但“蜂巢 AI”的结构天然就是离散的，从头到尾都不会遇到小数点的困扰。

这个离散的结构有两个巨大的优势：
1.  **它是自然的产物**：没有经过人类设计，是通过演化得来的，基本结构和我们的大脑更接近。
2.  **它的性价比极高**：哪怕它得到的结果不太准确，但运行时耗费的计算力非常非常小。

不过，这样做出来的智能系统，它的稳定性如何呢？会不会稍微有点儿扰动就“神经错乱”呢？

没关系，是骡子是马拉出来遛遛！

*(图片：不同初始值下系统点亮细胞的热力图)*
这个系统从不同的初始值出发，所有过程中被点亮的细胞的热力图。可见：无论取什么初始值，最后结果都落在固定的“0”和“1”上。

## （五）模糊的正确

就拿人类来说，我们的大脑时刻要面对的信号都不是“纯净”的。
比如现在，你的大脑正在接受很多信号：手机屏幕上的文字，视野里的背景信息，耳朵里的声音，肢体感觉，等等。。。
这些信号永远会纠缠在一起，你的大脑必须能应对这种状况：耳朵里听见别的声音，你还得保持继续阅读才行。
当然信号的噪音不能太大，干扰太大谁都受不了。但你的大脑抗干扰能力越大，就说明你大脑的“鲁棒性”越强。

现在我们回到“蜂巢 AI”，试着给它输入噪音。
怎么模拟噪音呢？
可以在初始的时候，同时给它输入两个黑点，甚至多个黑点。

我们先选一种没有噪音的情况下“蜂巢 AI”的表现：

从：
*(图片：无噪音初始状态)*
演化到：
*(图片：无噪音最终状态)*

也就是下图最左边的情况。
下图右边几张是在初始值中添加不同噪音的效果：
*(图片：有无噪音对比图及噪音影响示意)*
第 0 排带红圈的点位就是噪音；
下面所有带红圈的点位就是噪音产生的扰动，也就是相对于没有噪音的区别。

下面这张图，显示了蜂巢 AI 对噪音的适应情况。（没有列举所有可能的噪音，只是一些典型的情况。）
*(图片：蜂巢AI对噪音的适应情况总结)*
第一排是选定的一种没有噪音的原始输入。下面就是在原始输入的基础上添加噪音的影响。
白色横条，意味着输出和原本的一致。没有受到噪音影响。
粉色的横条，意味着输出和原本不一致了。受到了噪音影响。

乍一看，这系统的稳定性也不咋地啊，粉色的情况那么多。
别急，我们来仔细分析一下，受到了干扰后，具体结果是啥？

沃尔夫勒姆总结了各种情况，如下图：
*(图片：各种噪音干扰下的结果分类统计图)*

其中 32.1% 其实是没有输出结果，系统走到半路就停了。
这就好比你在嘈杂的地方读书，既没有听清别人说什么，也没有看懂书里写什么。这种结果其实挺好，因为系统没有胡乱给出答案。
还有 23.8% 是给出了纯纯正确的答案。
还有 15.6%、2.54%、1.7% 是给出了包含正确答案的错误答案。
实话说，这个表现已经很牛了！
有趣的是，还有 24.2%，是给出了纯纯错误的答案，但这个错误答案却对应着其他输入的正确答案！

这说明啥？
说明这个系统也许会出错，但它不会错到非常离谱。进化迭代，让这个系统冥冥中形成了两个“吸引盆”，好像结果会自动滑落到盆底一般。

当然，要达到这种境界，也需要一些训练技巧。
所谓技巧也不是人工干预细节，而是在演化的时候，加入一些“负样本”，当蜂巢 AI 得出错误的结果时，会触发“惩罚”机制。
这个方法，和我们熟悉的经典人工智能训练是一样的，也和人脑的训练方法是一样的。你不好好学习，老师就会邀请你妈妈来揍你一顿，这就是惩罚机制。

至此，我们已经训练出一个丐版的智能系统。
它具有模糊的正确性，它在噪音中保持强韧，不轻易被毁灭，它拿到了“进化游戏”的入场券。

而且我们似乎还额外获得了一个认知。
那就是：对智能祛魅。

智能的秩序和自然界的无序总是形成鲜明反差，以至于在漫长的历史中，人们总愿意相信有个“造物主”来屈尊造人。
而“蜂巢 AI”的训练过程恰恰告诉我们：智能的出现，不是什么了不得的偶然事件，反而是个大概率事件。

*(图片：模拟f[x]方程的其他组合形式)*
同样模拟 f[x] 那个方程，还能探索出很多种组合形式。

## （六）智能是“逻辑的搬运工”

沃尔夫勒姆尝试了很多“蜂巢 AI”，每次都能训练出来拟合最初那个 f[x] 方程的人工智能。
这里的关键是，即便它们完成的任务相同，但由于随机性的影响，每次训练出来的系统在微观结构上都不一样。（红绿色块的位置不同）

怎么训怎么有，这说明什么？
说明“逻辑资源”在自然界极其丰富。
就像铜矿一样，在地球上到处都有。古代各个地区的人类文明虽然没有交流，但都顺利发现了冶炼铜的技术，独立进入青铜时代。

如此，我们可以试着回答最初的问题：
“智能”的微观结构，到底是什么？

> 智能的运转，就是把自然界已有的逻辑碎片给组合了起来，让它能够判断输入与输出极其复杂的对应关系。

不过，即便只是对既有逻辑的搬运和整合，也不是所有整合方法都能产生高密度的智能。

一个反直觉的结论是：
> 当你越佛系松弛的时候，越能造出高密度的智能；当你越想严格把控，恰恰越难以造出高密度的智能！

这不是一碗鸡汤，而是一把锋利的认知武器。

不信我们接着看一个实验：
刚才我们说过，用“与”和“异或”可以组合出各种函数，而且针对某一个函数，有无数种方法可以等效出来。
但查看细节就会发现，等效的“蜂巢块”的大小可不一样。
比如我们找到的等效于两色元胞自动机规则 30 的“蜂巢块”最小只需要 4 行，并且有两种情况：

*(图片：进化得到的规则30蜂巢块 - 4行)*

如果允许加到 5 行，那情况就一下子多了。

*(图片：进化得到的规则30蜂巢块 - 5行)*

可这些蜂巢块都是进化得到的，看上去杂乱无章，无法解释。
如果我们非要做出可以解释的蜂巢块也可以，就得按照人类理解的逻辑计算方式来一步步生成。
比如：

*(图片：人工搭建的规则30逻辑示意)*
其中的 x、y、z 就代表规则 30 的三个输入。

可以证明，这个“蜂巢块”确实是对的↓↓↓
*(图片：人工搭建的规则30蜂巢块)*

但你发现没，人工搭建的块，比自然进化出来的块更多，需要 6 行。
多数情况，按照人类逻辑来搭建逻辑乐高，得出的结果要大得更多。比如规则 110。

这些是通过进化得到的：
*(图片：进化得到的规则110蜂巢块)*

这个是通过人工搭建得到的：
*(图片：人工搭建的规则110蜂巢块)*

一个结论呼之欲出：
> 即便逻辑资源在自然界丰富存在，但是“随机进化”冶炼出来的纯度更高，而“人工搭建”的方法纯度更低。

要知道，无论是规则 30 还是规则 110，都还仅仅是一个简单的思想实验，模拟了神经协作模式的皮毛而已。真正大脑的运作会比这个复杂千倍万倍。
可想而知，如果使用“人类可解释的编程方法”，在脑细胞层面每一次逻辑迭代都会比自然进化的方案更耗能，那么整体思考的代价将变得非常沉重。

这暗示了一个真相：**一个系统的“逻辑密度”和“可解释度”是一个跷跷板！**

*(图片：逻辑密度 vs 可解释度 跷跷板示意图)*

这也解释了一个大问题：为什么现在我们的人工智能如此耗能？
因为我们在训练中使用了大量“人工搭建”的逻辑，它们就像“脚手架”，增加了智能的可解释性，但也降低了智能的逻辑密度。

这里，我们可以回到 DeepSeek。
之所以说 DeepSeek 对 AI 技术产生了极大的理论贡献，是因为它终于找到了一种方法，在训练的流程中拆掉了大量脚手架。
具体来说，DeepSeek 在很多重要的点位上用强化学习（RL）的方法替代掉了人类监督微调（SFT）。
简单理解就是：强化学习就是在底层去掉人类监督，只保留一些高层的人类筛选，让 AI 有更大的自由度自己探索适合的思考方式。
这相当于在训练的关键步骤照搬了宇宙的智能设计图——“突变”+“筛选”。

换句话说：它找到了一种更好的“搭宇宙便车的方法”！

如果回头望，你会惊奇地发现：**整个 AI 的发展历史，就是科学家们不断放手，不断把智能的产生交给随机进化，不断更好地“搭宇宙便车”的过程！**

## （七）搭好宇宙的便车

在人工智能学科诞生初期，重磅科学家们几乎都在支持“符号主义”，也就是手动匹配万事万物的联系，让 AI 的全部推理都有理有据，在最细节的层面也要能被解释。
但逐渐，科学家承认“学会多少道理都过不好这一生”，不如放手让 AI 自己去学习事物之间的联系，这才倒向了罗森布拉特的“联结主义”，乃至后续辛顿教授在这一流派基础上开创的反向传播路线，以至于 ChatGPT 诞生。
由此，AI 才汹涌成蓬勃的大河。
不是因为人们喜欢放手，而是因为不放手就无法前行。

*(图片：Mark I 感知机)*
世界上第一个基于“联结主义”的人工智能感知机，Mark I。

和这条河流所对应的，是人类计算负载从 CPU 向 GPU 的史诗级迁移。
CPU 是为形式计算而设计的，源自于人造的理想空间：它可以处理复杂的控制指令。
GPU 是为图像处理而设计的，根植于人类的视觉进化：它可以高效处理简单重复计算。

你还记得那个跷跷板吗？
本质上，CPU 就代表了“可解释度”，而 GPU 则代表了“逻辑密度”。

CPU 时代的领军企业英特尔，股价腰斩，险被收购；而 GPU 时代的领军企业英伟达，股价已经翻了无数翻。
从微观上看，两家公司的每一次经营决策的累积导致了如今的分野。但拉开视野来看，顺应历史的潮流，才是胜利的关键。

数学是人类智慧的王冠，精准形式计算的需求永远庞大。
但宇宙的“计算不可约性”从根本上决定，更多的日常决策只适合于离散化的拟合。

联结主义 AI 的兴起、离散化结构的成功、英伟达 GPU 的崛起，不都是因为他们搭对了宇宙的便车吗？

有个笑话讲：最牛的 AI 老师傅每天上班都要默念十遍“智能的本质是压缩”。

> 智能的本质是压缩

这恰好揭示了生命进化的真理，也揭示了离散化拟合的本质。那就是：只求神似，不求精准。

世界上的现象复杂，但凡要用有限的计算力去拟合，就需要有损压缩。
比如在用蜂巢 AI 拟合 f[x] 时，由于系统的“离散”本质，即便不断增加系统的计算力，我们得到的也是一个近似曲线，而不可能完全贴合形式计算的那个理想曲线。

*(图片：f[x]理想曲线与压缩后结果对比)*
下图就是对 f[x] 的理想曲线进行压缩后的结果。

但它的好处显而易见，那就是计算时间是绝对刚性的。
无论如何，系统都可以在有限时间内快速给出拟合结果——不死机。这对于生命的生存至关重要。
刚性时间就是进化的筛选条件之一。
在此基础上智能进化的过程，就是不断找到更好压缩方案的过程。

为了更直接地展示 AI 在压缩上的能力，沃尔夫勒姆做了另一个实验——“自编码器”。
把第一排当做输入，把最后一排当做输出。系统的目标就是：经过中间步骤的演化，让输出无限接近于输入。
它模拟了人“压缩世界”的过程：在内心构建一个世界的“像”。
这个系统没有看上去那么简单，因为在计算的过程中，最初的信息会被“碾碎”，而在后来又要“重构”起来。
但宇宙内禀的逻辑资源太丰富了，不费什么力气就进化出很多“自编码器”。

*(图片：自编码器基本结构与演化)*

接下来更骚的操作来了：他把系统的“腰部”收窄，看看还能不能训练出自编码器。
结果是：仍然可以。

*(图片：腰部收窄的自编码器)*

而且，就算腰部极细，细到只有两个格子（下图），系统仍然能完成自编码。（只不过在这种极端情况下，压缩的质量不忍直视。）

*(图片：腰部极细的自编码器)*

在细腰部分传递的信息，其实就是数据的压缩版本；
腰部以上，是压缩过程；
腰部以下，是解压过程。

不同的压缩系统，对应着不同的耗能和效果。两个参数做简单的除法，可知它们智能的密度也不相同。

这个简单的模型却给了我们巨大的信心：
只要能找到更好的方式对世界进行压缩，我们就能制造出一个和人脑平齐，甚至超越人脑的智能系统。

如今，我们已经有了 ChatGPT，有了 DeepSeek。
再往前看，我们有可能把这种“搭便车”玩到极限，造出理解万物、无所不能的超级智能吗？

## （八）我们的无知与欢喜

其实，如果我们接受了“计算不可约性”，很容易推出如下结论：
1.  我们永远有机会做出更好的人工智能；
2.  我们永远无法抵达人工智能的“极限”。

所谓人工智能的极限，就是对一切问题都能给出正确答案的那种神级智能。
作为“离散宇宙”的生物，我们只能基于离散化的结构创造智能。运转的细节依靠亿万模糊的拟合，它的原理本身就内含了出现大量错误的可能性。
也就是说，无论我们如何挣扎，基础的物理定律“保证”了我们永远无法准确理解万物，也永远无法准确预测未来。

我们，就像被困在一个黑盒子里，再撕心裂肺的呐喊都无法透传出去——如同进入 *《2001 太空漫游》* 里那个黑色的石碑，人类能做的只有沉默与敬畏。

沃尔夫勒姆把宇宙的底层结构想象为一种“超图重写”结构，这是元胞自动机的另一种抽象，也是和元胞自动机计算等价的。
简单来说，超图重写就是把宇宙看成一幅“图”。在这幅图上运行迭代策略：随着时间流逝，按照既定规则，以一个结构替代另一个结构。
这个规则可能很简单，比如下面就是一个规则示例：

*(图片：超图重写规则示例)*

即便规则这么简单，每次迭代都会产生新的结构，下一次的迭代也会在新的结构中继续演化，并不重复。由于计算的不可约性，宇宙将会变得越来越复杂。
下图，就是基于上面那个规则演化几步之后的结果：

*(图片：超图演化示例)*

同样因为计算不可约性，在这样的宇宙里生活，没人能够先于演化精准预测超图的全景，最多只能通过（基于离散结构的）“智能”来对某个局部的图纸做粗略预测。
而且，由于我们用来预测的智能系统本身，也是“超图”的一部分，这意味着智能当然也无法预测自身的未来状态。

这个特点解决了一个终极追问：人到底有没有自由意志？
我们可以逆向思考：
什么是没有自由意志？就是我们可能找到预测自己在未来某一刻的思想的方法。对吧？
但计算不可约性已经预言了，我们没有办法准确预测大脑未来的状态。也就是说，无论我们的思维是不是机械运动的，都不影响“我们无法预测它”这一事实。

所以，我们永远可以认为自己“有”自由意志！

只不过，这种自由意志的代价是昂贵的：
人类，作为一个智能体，永远无法理解宇宙的所有真相。
AI，作为一个人造的智能体，同样永远无法理解宇宙的所有真相。

但这种对“无法理解”本身的探寻，何尝不是一种理解呢？
这种揭示自己渺小的真相的路程，何尝不是一种伟大呢？

我们盛开，但我们脚踩淤泥；
我们脚踩淤泥，但我们盛开。

正如胡适所说：
> 怕什么真理无穷，进一寸有一寸的欢喜。

真正的慰藉，也许并非“朝闻道夕死可矣”。而是在终极真理的巨大引力下跌撞前行，收获的一路欢喜。

这，也许才是终极意义上的 Deep Seek。

---

**参考资料：**

*   [https://writings.stephenwolfram.com/2024/08/whats-really-going-on-in-machine-learning-some-minimal-models/](https://writings.stephenwolfram.com/2024/08/whats-really-going-on-in-machine-learning-some-minimal-models/)
*   《机器学习中到底发生了什么？一些极简模型》

---

浅友们好~我是史中，我的日常生活是开撩五湖四海的科技大牛，我会尝试用各种姿势，把他们的无边脑洞和温情故事讲给你听，欢迎和我做朋友。