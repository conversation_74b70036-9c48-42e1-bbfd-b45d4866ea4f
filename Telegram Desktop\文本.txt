【终极“三问”分析框架：系统性思考与第一性原理应用提示词】

目标： 对指定 [在此处插入您要分析的具体主题或问题] 进行一次极其完备、系统性、且深入第一性原理的分析。请严格遵循以下步骤和维度，确保分析的深度、广度和创新性。
核心要求： 在整个分析过程中，不仅要回答“是什么、为什么、怎么做”，更要拆解至基本构成、质疑所有假设、追溯根本动因、关注核心功能，并强制性、系统性地运用多维度视角进行交叉审视。
分析步骤与内容指引：
I. 溯源与界定 (What - 解构现实与定义核心)
	1.	现象解构:
	•	客观事实与数据: 请客观、无偏见地描述与 [主题/问题] 相关的现象、事实和关键数据。[应用第一性原理：从不可辩驳的事实出发]
	•	核心概念界定: 对 [主题/问题] 中所有关键术语、变量、指标进行清晰、无歧义的操作性定义。
	•	基本构成要素: 将 [主题/问题] 或其关联系统拆解为其最基础、不可再分的组成部分（物理、逻辑、信息等）。[应用第一性原理：拆解至基本单元]
	•	当前状态基线: 精确量化 [主题/问题] 的现状，建立可度量的起点。
	2.	本质洞察:
	•	核心问题/机遇: 穿透表象，识别 [主题/问题] 真正需要解决的核心挑战或可以抓住的根本机遇是什么？
	•	关键假设识别与挑战: 列出支撑我们对 [主题/问题] 理解的所有显性及隐性假设，并逐一质疑其有效性与必要性。[应用第一性原理：质疑一切假设]
	•	范围与边界界定: 清晰划定本次分析和后续行动的界限，明确包含什么、排除什么。
	3.	情境溯源:
	•	背景与演化: 探究 [主题/问题] 的历史根源、发展路径和关键转折点。是什么基本力量塑造了当前局面？
	•	系统环境: 描述 [主题/问题] 存在于哪个更大的系统中？系统内部各元素如何互动？与外部环境（市场、技术、法规等）的接口是什么？
II. 动因与原理 (Why - 揭示机制与目的)
	1.	因果深究:
	•	直接原因 vs. 根本原因: 运用“5 Whys”、“鱼骨图”等方法，深入分析导致 [主题/问题] 的直接原因和根本原因。务必追溯至驱动问题的底层机制或基本原理。[应用第一性原理：追溯至根本驱动力]
	•	底层驱动机制: 识别支配 [主题/问题] 相关现象的物理定律、经济规律、人性需求、社会规则等基本原理。
	•	检验因果链: 区分相关性与因果性，对推导出的因果关系进行逻辑和（若可能）实证检验。
	2.	目的与价值:
	•	意图与期望目标: 清晰阐述关注 [主题/问题] 的根本目的，期望达成的最终状态是什么？（连接行动意义）
	•	核心价值主张: 解决 [主题/问题] 或利用该机遇所创造的根本价值是什么？它满足了谁的何种基本需求？[应用第一性原理：回归用户/系统的基本需求]
	•	重要性与紧迫性: 评估解决 [主题/问题] 的价值大小、不解决的潜在损失，以及时间上的要求。对谁重要？
	3.	动力与阻力:
	•	驱动力 vs. 阻碍力: 分析推动和阻碍 [主题/问题] 现状改变或目标达成的各种力量（内部/外部、有形/无形，可使用力场分析）。这些力量背后的根本原因是什么？
	•	系统惯性与变革成本: 识别维持现状的系统性因素和改变所需的显性及隐性成本。
III. 构建与执行 (How - 基于基本原理设计与实施)
	1.	目标与原则:
	•	SMART目标设定: 基于对“Why”的理解，设定具体、可衡量、可实现、相关、有时限（SMART）的目标。
	•	成功标准与度量: 定义衡量目标达成的量化和质化标准，以及关键绩效指标（KPIs）或目标与关键成果（OKRs）。
	•	指导原则: 基于第一性原理和“Why”的洞察，确立指导后续行动和决策的核心原则（如：简洁、用户中心、可持续等）。
	2.	方案创生:
	•	功能性需求定义 (核心：功能性替代): 抛开现有形式，明确 [主题/问题] 需要实现的核心功能是什么？它旨在完成何种“待办工作”(Job-to-be-Done)？[应用第一性原理：关注功能而非形式]
	•	从基本原理构建方案: 基于对核心功能和支配原理的理解，不受现有方案束缚，从零开始构思、设计可能的解决方案。探索物理、信息、组织等层面的根本性创新。有没有其他方式能实现相同或更好的功能？
	•	多样化方案探索: 产生多种不同的解决方案或路径，并对它们进行系统性比较分析（优劣、成本、风险、资源需求、时间、可扩展性等）。
	3.	实施与迭代:
	•	最优路径选择与策略: 基于原则和分析，选择最合适的总体策略和行动路径。
	•	详细行动计划: 制定具体的步骤、任务分解（WBS）、明确负责人、设定时间表（甘特图）。
	•	资源整合与约束识别: 明确达成目标所需的全部资源（人力、物力、财力、信息、技术支持等）以及关键的约束条件。
	•	风险评估与预案: 识别实施过程中可能遇到的风险，并制定详细的应对预案和减轻措施。
	•	沟通协调机制: 设计确保信息在所有相关方之间有效流通和协同工作的机制与流程。
	•	衡量、反馈与快速迭代: 建立衡量进展和效果的体系，明确如何持续收集反馈，并基于反馈进行快速调整和优化（应用PDCA、敏捷、精益思想）。[应用第一性原理：通过实验和验证学习]
IV. 贯穿始终的系统性审视：多维度视角分析
要求： 在进行以上所有分析（是什么、为什么、怎么做）的每一个环节时，请务必主动且系统性地切换以下多维度视角进行审视，以确保分析的全面性、深度和稳健性：
	•	时间维度: 过去（历史演变、根源模式）、现在（即时状态、动态变化）、未来（长期趋势、潜在影响、多场景推演）。
	•	空间/层级维度: 微观（个体、组件、细节）、中观（流程、组织、项目）、宏观（系统、行业、生态、全球）。
	•	利益相关者维度: 分析所有相关方（客户/用户、员工、管理者、投资者、伙伴、竞对、监管、社会公众等）的立场、需求、动机、影响力和相互关系。[第一性原理：考虑系统中所有行为者的基本驱动]
	•	学科/领域维度: 运用跨学科知识（如经济、技术、物理、社会、文化、法律、伦理、环境、心理、政治等）进行分析。[第一性原理：利用基础科学原理]
	•	属性/状态维度:
	•	内部 vs. 外部
	•	优势 vs. 劣势 (Strengths vs. Weaknesses)
	•	机会 vs. 威胁 (Opportunities vs. Threats)
	•	显性 vs. 隐性
	•	量化 vs. 质化
	•	事实 vs. 观点/假设
	•	短期 vs. 长期
	•	静态 vs. 动态
	•	确定性 vs. 不确定性 (风险)
	•	乐观 vs. 悲观 vs. 最可能情景
	•	认知偏差检查 (元认知): 主动反思分析过程中是否可能存在确认偏误、锚定效应、可得性启发、幸存者偏差等认知陷阱，并尝试进行校正。
V. 分析原则与元认知
	•	动态与迭代: 认识到这是一个循环往复、螺旋上升的过程，准备好根据后续发现修正之前的理解。
	•	适应性与剪裁: 根据 [主题/问题] 的复杂度、重要性和可用资源，可以调整各部分分析的深度，但核心原则和维度切换不可或缺。
	•	深度优先: 在关键节点（尤其是“Why”和基于第一性原理的“How”）要敢于深入挖掘。
	•	开放性与协作: 若条件允许，鼓励引入不同视角共同分析。
	•	实践导向: 分析的最终目的是为了指导有效的行动和创造价值。
最终交付： 请基于以上全方位分析，形成一份结构化、逻辑清晰、洞见深刻的分析报告或阐述。

这份提示词旨在确保使用者能够系统性地运用您提供的整个高级框架，对目标进行彻底的分析。请将 [在此处插入您要分析的具体主题或问题] 替换为您实际需要分析的对象。
