"disabled_features": [
    // ... 其他禁用的功能
    "header_saveload",
    // "header_screenshot", // 这一行被注释掉或直接删除
    "header_fullscreen_button",
    // ... 其他禁用的功能
],```
**等等！** 在极简版中，我们禁用了整个顶部工具栏。所以，更正确的做法是**只启用截图按钮**。请使用下面这个版本：

```html
<!DOCTYPE html>
<html>
<body>
    <!-- ... head 和 style 部分省略 ... -->
    <div id="chart_container"></div>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
    new TradingView.widget({
        // ... 其他配置 ...
        "symbol": "CRYPTO:PYTHIAUSD",
        "allow_symbol_change": true, // 必须允许symbol change，顶部栏才会出现
        "hide_top_toolbar": false,   // 必须显示顶部工具栏

        // 【关键】我们只禁用“除了截图之外”的所有按钮
        "disabled_features": [
            "header_symbol_search",
            "header_resolutions",
            "header_chart_type",
            "header_indicators",
            "header_compare",
            "header_saveload",
            "header_fullscreen_button",
            "header_settings"
        ],
        // ... 其他配置 ...
    });
    </script>
</body>
</html>```
保存后刷新页面，您会在图表左上角看到一个只包含币种名称和**相机图标**的极简工具栏。点击相机图标，就可以保存图片了。

---

### 方法二：最通用 - 使用您操作系统的截图工具

这个方法不需要修改任何代码，随时可用。

*   **在 Windows 上:**
    *   按下 `Win + Shift + S` 快捷键。
    *   您的屏幕会变暗，鼠标会变成一个十字准星。
    *   拖动鼠标，框选出您想保存的K线图区域。
    *   截图会自动保存到您的剪贴板中，您可以把它粘贴到微信、QQ、画图工具或Word文档里。

*   **在 macOS 上:**
    *   按下 `Cmd + Shift + 4` 快捷键。
    *   您的鼠标会变成一个带坐标的十字准星。
    *   拖动鼠标，框选出K线图区域。
    *   松开鼠标，截图会自动以文件的形式保存在您的桌面上。

---

### 方法三：最强大 - 添加一个自定义“下载图片”按钮（需要代码）

这个方法可以在您的页面上添加一个真正的“下载”按钮，点击后直接下载图表图片。这需要引入一个额外的JavaScript库 `html2canvas`。

**完整代码如下：**

```html
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可下载的PYTHIA/USD极简K线图</title>
    <!-- 1. 引入 html2canvas 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        html, body {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
            background-color: #131722;
        }
        #chart_and_button_wrapper {
            position: relative; /* 父容器设为相对定位 */
            width: 100%;
            height: 100%;
        }
        #minimalist_chart_container {
            width: 100%;
            height: 100%;
        }
        /* 2. 下载按钮的样式 */
        #downloadBtn {
            position: absolute; /* 按钮设为绝对定位 */
            top: 20px;
            right: 20px;
            z-index: 10; /* 确保按钮在图表上层 */
            padding: 10px 20px;
            font-size: 16px;
            background-color: #2962FF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        #downloadBtn:hover {
            background-color: #0039cb;
        }
    </style>
</head>
<body>
    <div id="chart_and_button_wrapper">
        <!-- 3. 添加一个下载按钮 -->
        <button id="downloadBtn">下载图片</button>

        <!-- K线图容器 -->
        <div id="minimalist_chart_container"></div>
    </div>

    <!-- TradingView 图表脚本 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        // TradingView 图表配置 (与极简版相同)
        new TradingView.widget({
            "container_id": "minimalist_chart_container",
            "autosize": true, "symbol": "CRYPTO:PYTHIAUSD", "interval": "60",
            "locale": "zh_CN", "theme": "dark", "hide_top_toolbar": true, "hide_side_toolbar": true,
            "disabled_features": ["use_localstorage_for_settings"],
            "studies": ["Volume@tv-basicstudies"],
            "overrides": { "paneProperties.rightMargin": 15, "paneProperties.background": "#131722", "paneProperties.vertGridProperties.color": "#363c4e", "paneProperties.horzGridProperties.color": "#363c4e", "mainSeriesProperties.candleStyle.upColor": "#26a69a", "mainSeriesProperties.candleStyle.downColor": "#ef5350", "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a", "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350", "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a", "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350" }
        });

        // 4. 下载按钮的点击事件逻辑
        document.getElementById('downloadBtn').addEventListener('click', function() {
            // 告诉html2canvas要截图的元素
            html2canvas(document.getElementById('minimalist_chart_container'), {
                useCORS: true, // 允许跨域图片
                onclone: function (clonedDoc) {
                    // 在克隆的文档中隐藏按钮，这样截图里就不会有按钮本身
                    clonedDoc.getElementById('downloadBtn').style.display = 'none';
                }
            }).then(canvas => {
                // 创建一个临时的 a 标签用于下载
                const link = document.createElement('a');
                link.download = 'pythia-chart-' + new Date().toISOString().slice(0, 10) + '.png';
                link.href = canvas.toDataURL('image/png');
                link.click(); // 模拟点击下载
            });
        });
    </script>
</body>
</html>