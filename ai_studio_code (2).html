<!-- TradingView Widget BEGIN -->
<div class="tradingview-widget-container" style="height:100%;width:100%">
  <div id="tradingview_c28ab" style="height:calc(100% - 32px);width:100%"></div>
  <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
  <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
  <script type="text/javascript">
  new TradingView.widget(
  {
  "autosize": true,
  "symbol": "CRYPTO:PYTHIAUSD",
  "interval": "D",
  "timezone": "Etc/UTC",
  "theme": "light",
  "style": "1",
  "locale": "en",
  "enable_publishing": false,
  "allow_symbol_change": true,
  "container_id": "tradingview_c28ab"
}
  );
  </script>
</div>
<!-- TradingView Widget END -->```

**可自定义的选项：**
*   `"symbol": "CRYPTO:PYTHIAUSD"`: 这是您要显示的交易对。
*   `"interval": "D"`: 默认时间周期（"D" 代表每日）。您可以更改为 "1", "5", "15", "30", "60", "240" (小时), "W" (每周), "M" (每月)。
*   `"theme": "light"`: 主题颜色，可以是 "light" (浅色) 或 "dark" (深色)。
*   `"locale": "en"`: 显示语言，"en" 是英语，您可以更改为 "zh_CN" (简体中文)。
*   `"autosize": true`: 设置为 `true` 可以让图表自动适应容器的大小。如果设为 `false`，则需要手动设置 `"width"` 和 `"height"`。

### 2. 符号概览小部件 (Symbol Overview Widget)

这个小部件比较简洁，除了一个简化的图表外，还提供了一些基本的报价信息。

**HTML 代码:**
```html
<!-- TradingView Widget BEGIN -->
<div class="tradingview-widget-container">
  <div class="tradingview-widget-container__widget"></div>
  <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div>
  <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-symbol-overview.js" async>
  {
  "symbols": [
    [
      "CRYPTO:PYTHIAUSD|1D"
    ]
  ],
  "chartOnly": false,
  "width": "100%",
  "height": "100%",
  "locale": "en",
  "colorTheme": "light",
  "autosize": true,
  "showVolume": false,
  "showMA": false,
  "hideDateRanges": false,
  "hideMarketStatus": false,
  "hideSymbolLogo": false,
  "scalePosition": "right",
  "scaleMode": "Normal",
  "fontFamily": "inherit",
  "fontSize": "10",
  "noTimeScale": false,
  "valuesTracking": "1",
  "changeMode": "price-and-percent",
  "chartType": "area",
  "maLineColor": "#2962FF",
  "maLineWidth": 1,
  "maLength": 9,
  "lineWidth": 2,
  "lineType": 0
}
  </script>
</div>
<!-- TradingView Widget END -->