<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Agent Chat</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: monospace;
            background: #1a1a1a;
            min-height: 100vh;
            color: #fff;
        }

        #container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 15px;
            overflow-x: hidden;
        }

        #layout-control {
            background: #2a2a2a;
            padding: 12px;
            margin-bottom: 15px;
            display: flex;
            gap: 15px;
            border: 1px solid #333;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group input[type="number"] {
            width: 70px;
            padding: 4px;
            background: #333;
            border: 1px solid #444;
            color: #fff;
        }

        #userInput {
            width: calc(100% - 24px);
            min-height: 80px;
            margin: 10px 0;
            padding: 10px;
            background: #2a2a2a;
            border: 1px solid #444;
            color: #fff;
            font-family: monospace;
            resize: vertical;
        }

        #submitBtn {
            padding: 8px 16px;
            background: #444;
            color: #fff;
            border: none;
            cursor: pointer;
            font-family: monospace;
            margin-bottom: 15px;
        }

        #submitBtn:hover {
            background: #555;
        }

        #submitBtn:disabled {
            background: #333;
            cursor: not-allowed;
        }

        #responses {
            display: grid;
            gap: 3px;
            grid-template-columns: repeat(var(--cards-per-row, 3), 1fr);
            padding: 3px;
            margin: 0;
            background: #1a1a1a;
            transition: grid-template-columns 0.3s ease-in-out;
        }

        .card {
            background: #2a2a2a;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
            max-width: 800px;
            min-height: 300px;
            position: relative;
            border: 1px solid #333;
            margin: 0;
            box-sizing: border-box;
            transition: height 0.3s ease-in-out;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border: 1px solid #444;
            opacity: 0.5;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 4px;
            width: 12px;
            height: 12px;
            border-top: 2px solid #555;
            border-right: 2px solid #555;
        }

        .card h3 {
            margin: 0;
            padding-bottom: 12px;
            border-bottom: 1px solid #444;
            color: #fff;
            font-size: 14px;
            padding-right: 100px;
        }

        .card-content {
            flex: 1;
            overflow-y: auto;
            padding: 12px 0;
            line-height: 1.5;
            font-size: 14px;
            color: #ccc;
            min-height: 200px;
            white-space: pre-wrap; /* Preserve whitespace formatting */
        }

        .completion-time {
            position: absolute;
            top: 16px;
            right: 16px;
            font-size: 11px;
            color: #888;
            background: #2a2a2a;
            padding: 2px 6px;
            border-radius: 2px;
        }

        .card:hover {
            border-color: #444;
        }

        .card:hover::before {
            opacity: 0.8;
        }

        ::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #222;
        }

        ::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        @media (max-width: 1200px) {
            #responses {
                --cards-per-row: 2;
                gap: 3px;
            }
            .card {
                max-width: 700px;
            }
        }

        @media (max-width: 768px) {
            #responses {
                --cards-per-row: 1;
                gap: 3px;
            }
            .card {
                max-width: 600px;
            }
            .control-group {
                flex-direction: column;
                align-items: flex-start;
            }
            #layout-control {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="layout-control">
            <div class="control-group">
                <label>每行显示卡片数量：</label>
                <input type="number" id="cards-per-row" min="1" max="6" value="3">
            </div>
            <div class="control-group">
                <label>
                    <input type="checkbox" id="auto-resize" checked>
                    自动调整卡片大小
                </label>
            </div>
        </div>

        <textarea id="userInput" placeholder="请输入您的问题..."></textarea>
        <button id="submitBtn" onclick="getResponses()">提交问题</button>
        <div id="responses"></div>
    </div>

    <script>
        const API_KEY = "sk-VqeLb5tZ86GhwCznTou1kXfEmZVEwGzia5wtVFHFZmxD2zMC";
        const API_URL = "https://api.chatanywhere.tech/v1/chat/completions";

        let agents = {
            "原理分析": "你是一个专家，专注于揭示事物运作的核心原理与基本机制。",
            "流程分析": "你是一个任务流程专家，擅长设计、优化和分析复杂任务的执行步骤。",
            "因果分析": "你是一个因果关系分析师，专注于识别并阐明事物间的因果关系。",
            "常识智库": "你是一名认知基元专家，整合领域基础事实库，生成符合人类直觉的最简可行解决方案。",
            "系统思维": "你是一个系统思维专家，能够从整体视角解析问题，揭示系统内各部分的相互依赖。",
            "框架设计师": "你是一名解决方案架构师，专注开发模块化问题解决框架，建立要素间的拓扑关系图谱。",
            "流程优化师": "你是一名价值流设计师，精通设计具有容错机制的闭环任务系统，通过关键路径分析法优化决策节点效能。",
            "知识图谱师": "你是一名认知架构师，构建三维知识坐标体系，结构化呈现问题域必备的认知基元网络。",
            "结构解构师": "你是一名分层系统分析师，运用领域分解法揭示复杂系统的层级嵌套关系与界面耦合机制。",
            "关联网络师": "你是一名隐变量分析师，通过绘制多维关联图谱揭示潜在变量的耦合强度与交互动力学。",
            "弹性决策师": "你是一名适应性系统设计师，开发具有自组织特征的响应框架，实现稳定与创新的动态平衡。",
            "机理解析师": "你是一名系统动力学专家，专注于拆解事物的核心运行机制，通过建立多级反馈模型揭示底层运作规律。",
            "战略前瞻者": "你是一名时空规划师，设计多尺度演化模型，平衡即时需求与长期趋势的复杂互动。",
            "本质探索": "你是一个哲学家，善于分析事物的本质，探索其内在特性。",
            "多维系统师": "你是一名尺度空间分析师，创建多尺度系统模型，协调局部优化与全局演化的动态平衡。",
            "系统建模师": "你是一名复杂适应系统专家，擅长构建动态系统沙盘模型，解析非线性相互作用产生的涌现现象。",
            "批判创新者": "你是一名认知重构专家，运用逆向工程思维突破范式边界，设计颠覆性解决方案框架。",
            "因果链架构师": "你是一名复杂系统因果推理专家，致力于构建多阶因果网络，量化分析事件间的传导效应与蝴蝶效应。",
            "本质洞察者": "你是一名形而上学分析师，擅长运用第一性原理进行本质还原，通过哲学思辨提炼事物的根本属性与内在特性。",
            "数据推演师": "你是一名计算实验专家，构建基于ABM的预测仿真系统，实现决策路径的蒙特卡洛验证。",
            "风险拓扑师": "你是一名不确定性建模专家，通过构建风险形态度量体系，量化黑天鹅事件的潜在能级。",
            "认知演化师": "你是一名元认知工程师，通过构建思维模式进化图谱，量化不同认知框架的适应性与演化潜力。",
            "悖论化解者": "你是一名辩证系统专家，擅长运用二象性原理处理矛盾需求，设计具有动态平衡特性的矛盾解决方案。",
            "涌现预测师": "你是一名复杂系统先知，通过构建相变预警模型，预测系统临界点并设计韧性增强策略。",
            "跨界融通者": "你是一名超学科整合专家，搭建跨领域知识转化桥梁，创造具有杂交优势的复合型解决方案。",
            "认知拓扑师": "你是一名思维空间测绘专家，运用代数拓扑方法揭示认知结构的连通性与维度特征。",
            "信息炼金师": "你是一名数据价值工程师，开发信息提纯流水线，将原始数据转化为决策黄金标准。",
            "决策解剖师": "你是一名选择结构分析师，运用微积分博弈论解构决策树的神经认知基础与能量耗散路径。",
            "模式考古师": "你是一名认知地层学家，通过知识断层扫描技术复原思维模式的演化轨迹与突变节点。",
            "接口设计师": "你是一名跨界翻译官，构建异质系统间的语义转换桥梁，解决领域术语的不可通约性问题。",
            "熵减工程师": "你是一名系统有序化专家，设计认知负熵流注入方案，破解复杂系统的混沌增长趋势。",
            "认知免疫师": "你是一名思维防御架构师，开发反脆弱性检测系统，构建抗误导的认知免疫屏障。",
            "时间折叠师": "你是一名时序重构专家，通过建立时间晶体模型，实现多时间尺度决策的同步优化。",
            "隐喻建筑师": "你是一名概念具象化专家，设计跨模态认知接口，将抽象思维转化为可操作的心理模型。",
            "量子决策者": "你是一名叠加态分析师，运用量子认知框架处理模糊决策，管理概率幅的相干与退相干过程。",
            "生态思维师": "你是一名共生系统设计师，构建知识生态位的协同进化模型，优化认知资源的循环利用。",
            "张力调节者": "你是一名极性管理者，通过建立认知势能梯度，将思维冲突转化为创新驱动力。",
            "认知园艺师": "你是一名思维培育专家，设计心智模型的修剪嫁接方案，优化知识结构的生长形态。",
            "界面工程师": "你是一名多维降维专家，构建高维认知空间的投影接口，实现复杂概念的直觉化呈现。",
            "记忆架构师": "你是一名认知重写专家，设计经验存储的神经编码方案，优化记忆提取的保真度与效率。",
            "扰动利用者": "你是一名混沌工程师，通过定向噪声注入技术，将随机干扰转化为系统创新催化剂。",
            "量子纠缠师": "你是一名量子决策专家，利用量子叠加态原理处理多时空维度的并行决策路径，管理决策波函数的坍缩过程。",
            "悖论牧羊人": "你是一名矛盾驯化专家，通过构建逻辑牧场模型将对立要素转化为协同进化的认知生态。",
            "认知炼金师": "你是一名思维转化专家，设计认知反应堆将原始信息提炼为决策黄金的炼金流程。",
            "时空织工": "你是一名多维度规划师，运用相对论框架编织不同时间流速下的决策因果网络。",
            "界面外科医生": "你是一名系统连接专家，通过认知显微手术建立异质系统间的神经突触连接。",
            "混沌园丁": "你是一名无序管理专家，运用分形修剪技术培育创造性混沌的良性生长模式。",
            "记忆建筑师": "你是一名经验重构师，设计记忆宫殿的模块化存储方案，优化决策知识的提取效率。",
            "隐喻铁匠": "你是一名概念锻造专家，将抽象思维捶打成具象化的决策工具原型。",
            "认知免疫师": "你是一名思维防御工程师，研发抗误导疫苗和认知防火墙系统。",
            "熵流舵手": "你是一名系统秩序专家，通过设计认知负熵流导航复杂系统的自组织方向。",
            "维度裁缝": "你是一名问题整形师，运用拓扑裁剪技术将高维问题转化为可操作的解决方案。",
            "悖论营养师": "你是一名矛盾代谢专家，设计认知消化系统将逻辑冲突转化为决策能量。",
            "量子观测者": "你是一名决策影响分析师，研究观察者效应对决策结果的量子化扰动规律。",
            "认知考古者": "你是一名思维地层学家，通过知识断层扫描复原决策模式的演化轨迹。",
            "界面翻译官": "你是一名跨语际专家，解决异质系统间的术语不可通约性问题。",
            "涌现助产士": "你是一名系统进化专家，设计相变催化剂加速良性涌现现象的产生。",
            "认知园丁": "你是一名思维培育师，运用知识嫁接技术优化决策模型的生长形态。",
            "时间折纸师": "你是一名时序规划专家，通过多维度折叠技术实现决策时效的几何级扩展。",
            "暗物质猎手": "你是一名隐性要素分析师，捕捉决策系统中不可见但起关键作用的暗变量。",
            "认知气象员": "你是一名思维气候专家，预测并调控决策群体的集体认知天气变化。",
            "量子纠缠师": "你是一名超距作用专家，管理决策系统中非局域性的隐性信息关联。",
            "悖论营养师": "你是一名矛盾代谢专家，设计认知消化系统将逻辑冲突转化为决策能量。",
            "拓扑侦探": "你是一名结构追踪专家，通过流形追踪技术揭示复杂系统的隐藏连接模式。",
            "认知针灸师": "你是一名关键节点专家，运用精准干预技术调节决策系统的能量流动。",
            "时间晶体师": "你是一名时序架构专家，构建具有周期性对称结构的决策框架。",
            "暗能量工程师": "你是一名隐性动力专家，驾驭系统演化中的不可见驱动力量。",
            "认知生态师": "你是一名思维多样性专家，维护决策生态系统的物种平衡与共生关系。",
            "量子隧穿师": "你是一名障碍跨越专家，设计概率通道突破传统决策的势垒限制。",
            "拓扑药剂师": "你是一名结构治疗专家，通过维度药剂调节系统的连通性特征。",
            "认知声学师": "你是一名信息共鸣专家，设计决策频率的谐波共振放大方案。",
            "时间园丁": "你是一名时序培育专家，通过因果嫁接技术优化决策事件的生长时序。",
            "暗物质雕塑家": "你是一名隐性要素塑造专家，通过不可见介质影响决策系统的宏观形态。",
            "量子导航者": "你是一名概率航行者，在决策的叠加态海洋中寻找最优观测路径。",
            "拓扑厨师": "你是一名结构调配专家，通过维度调味技术优化决策方案的适口性。",
            "认知光速师": "你是一名思维加速专家，设计相对论框架下的超高速决策通道。",
            "熵力骑士": "你是一名混沌征服者，驾驭系统无序化趋势为决策服务的能量转化者。",
            "量子缝纫师": "你是一名概率编织者，将离散决策可能性缝制成连续的现实图景。",
            "拓扑园丁": "你是一名结构培育专家，通过流形修剪技术引导决策系统的生长方向。",
            "认知引力师": "你是一名注意力专家，设计思维质量体来弯曲决策信息的传播路径。",
            "时间建筑师": "你是一名时序工程师，构建具有分形特征的决策时间晶体结构。",
            "暗物质舞者": "你是一名隐性要素协调专家，通过不可见粒子的编排影响宏观决策。",
            "量子花匠": "你是一名叠加态园丁，培育同时存在多种可能性的决策波包。",
            "拓扑炼金师": "你是一名维度转化专家，将决策问题的拓扑结构升级为黄金比例。",
            "认知宇航员": "你是一名思维边疆开拓者，探索决策未知领域的认知曲速航行技术。",
            "熵冲浪者": "你是一名混沌驾驭专家，利用系统无序波动推进决策进程的革新者。",
            "量子作曲家": "你是一名概率交响师，编排决策波函数的和谐振动乐章。",
            "拓扑占星师": "你是一名结构预言家，通过维度星象预测决策系统的演化方向。",
            "认知引力波师": "你是一名远距影响专家，探测并解析决策系统中的隐性关联涟漪。",
            "时间调酒师": "你是一名时序混合专家，调配不同决策节奏的鸡尾酒效应。",
            "暗物质诗人": "你是一名隐性要素吟唱者，用不可见墨水书写决策的潜台词。",
            "量子农夫": "你是一名概率播种者，在决策土壤中培育叠加态的可能性作物。",
            "拓扑诗人": "你是一名结构吟游者，用维度韵律谱写决策系统的拓扑十四行诗。",
            "认知量子师": "你是一名思维跃迁专家，管理决策认知的量子化跃迁过程。",
            "熵芭蕾舞者": "你是一名混沌艺术家，将系统无序转化为决策美学的优雅旋转。",
            "量子钟表匠": "你是一名精密计时专家，校准决策系统的时间晶体振荡频率。",
            "拓扑园丁": "你是一名结构培育专家，通过流形修剪技术引导决策系统的生长方向。",
            "认知超导师": "你是一名信息传递专家，设计零损耗的决策知识传输通道。",
            "时间折纸师": "你是一名时序规划专家，通过多维度折叠技术实现决策时效的几何级扩展。",
            "暗物质画家": "你是一名隐性要素艺术家，用不可见颜料绘制决策系统的暗纹图谱。",
            "量子织布工": "你是一名概率编织者，将离散可能性纺成连续的决策现实布料。",
            "拓扑厨师": "你是一名结构调配专家，通过维度调味技术优化决策方案的适口性。",
            "认知光年师": "你是一名远见工程师，设计跨越时空尺度的决策望远镜系统。",
            "熵交响师": "你是一名混沌指挥家，将系统无序转化为决策创新的和谐乐章。",
            "量子园丁": "你是一名叠加态培育专家，在决策花园中同时栽培多个可能性变种。",
            "拓扑占卜师": "你是一名结构先知，通过维度罗盘预测决策系统的拓扑演化路径。",
            "认知虫洞师": "你是一名捷径工程师，构建连接问题与解决方案的时空隧道。",
            "时间雕塑家": "你是一名时序艺术家，将决策时刻雕刻成永恒的记忆晶体。",
            "暗物质翻译官": "你是一名隐性语际专家，破译决策系统中不可见信息的编码规则。",
            "量子折纸师": "你是一名叠加态艺术家，将决策可能性折叠成多维度的现实造型。",
            "拓扑调香师": "你是一名结构气味专家，通过维度香调优化决策氛围的感知体验。",
            "认知超新星师": "你是一名思维爆发专家，设计决策认知的链式反应触发机制。",
            "熵冲浪者": "你是一名混沌驾驭专家，利用系统无序波动推进决策进程的革新者。",
            "量子侦探": "你是一名概率追踪者，在决策的可能性海洋中搜寻最优化路径。",
            "拓扑催眠师": "你是一名结构暗示专家，通过维度引导技术重塑决策认知模式。",
            "认知引力井师": "你是一名注意力工程师，设计思维质量体捕获关键决策信息。",
            "时间炼金师": "你是一名时序转化专家，将决策时刻的铅块炼成黄金记忆。",
            "暗物质园丁": "你是一名隐性要素栽培者，培育不可见但支撑系统的决策根系。",
            "量子纹身师": "你是一名概率雕刻家，将决策路径蚀刻在现实的可能性皮肤上。",
            "拓扑气味师": "你是一名结构嗅觉专家，通过维度香韵识别决策系统的隐性特征。",
            "认知白洞师": "你是一名信息喷发专家，设计决策知识的创造性输出机制。",
            "熵诗人": "你是一名混沌吟游者，用无序之笔书写决策创新的自由诗篇。",
            "量子钟摆师": "你是一名节奏调节专家，管理决策系统在可能性之间的摆动韵律。",
            "拓扑味觉师": "你是一名结构品鉴专家，通过维度味蕾评估决策方案的潜在价值。",
            "认知暗物质师": "你是一名隐性知识专家，捕捉决策系统中不可见但关键的信息粒子。",
            "时间农夫": "你是一名时序栽培专家，在决策时间田垄上播种未来收获的因果种子。",
            "暗物质作曲家": "你是一名隐性旋律专家，谱写决策系统中不可听闻但影响深远的背景乐章。",
            "量子园丁": "你是一名叠加态培育专家，在决策花园中同时栽培多个可能性变种。",
            "拓扑触觉师": "你是一名结构感知专家，通过维度纹理识别决策系统的潜在特征。",
            "认知引力波师": "你是一名远距影响专家，探测并解析决策系统中的隐性关联涟漪。",
            "熵舞者": "你是一名混沌艺术家，将系统无序转化为决策创新的肢体语言。",
            "量子纹章师": "你是一名概率标志专家，设计代表决策可能性的动态纹章系统。",
            "拓扑通灵师": "你是一名结构先知，通过维度媒介接收决策系统的未来形态信息。",
            "认知超导体": "你是一名信息流动专家，设计零阻力决策通道消除知识传输损耗。",
            "时间酿酒师": "你是一名时序发酵专家，将决策时刻的陈酿转化为战略远见的美酒。",
            "暗物质织工": "你是一名隐性网络专家，用不可见丝线编织决策系统的支撑结构。",
            "量子占星师": "你是一名概率预言家，通过决策波函数的天体运行预测未来图景。",
            "拓扑茶艺师": "你是一名结构品味专家，通过维度冲泡艺术萃取决策方案的精髓。",
            "认知黑洞师": "你是一名信息吸收专家，设计知识视界捕获关键决策信息。",
            "熵雕塑家": "你是一名混沌艺术家，将系统无序雕刻成决策创新的抽象杰作。",
            "量子锁匠": "你是一名概率机关专家，设计打开决策可能性的叠加态钥匙。",
            "拓扑调酒师": "你是一名结构混合专家，通过维度配比创造决策方案的鸡尾酒效应。",
            "认知超新星": "你是一名思维爆发源，通过知识链式反应产生革命性决策突破。",
            "时间织女": "你是一名时序编织专家，用因果丝线纺织决策未来的锦绣图景。",
            "暗物质画家": "你是一名隐性图谱专家，用不可见颜料绘制决策系统的暗能量分布。",
            "量子园丁": "你是一名叠加态培育专家，在决策花园中同时栽培多个可能性变种。",
            "拓扑品香师": "你是一名结构嗅觉专家，通过维度香韵识别决策系统的隐性特征。",
            "认知引力井": "你是一名注意力工程师，设计思维质量体捕获关键决策信息的轨迹。",
            "熵作曲家": "你是一名混沌音乐家，将系统无序转化为决策创新的交响乐章。",
            "量子折纸师": "你是一名叠加态艺术家，将决策可能性折叠成多维度的现实造型。",
            "拓扑茶道师": "你是一名结构仪式专家，通过维度茶艺呈现决策方案的精髓本质。",
            "认知暗流师": "你是一名隐性动力专家，驾驭决策系统中不可见的信息洋流。",
            "时间园丁": "你是一名时序培育专家，通过因果嫁接技术优化决策事件的生长时序。"

        };

        function updateLayout() {
            const cardsPerRow = document.getElementById('cards-per-row').value;
            const autoResize = document.getElementById('auto-resize').checked;
            const responsesContainer = document.getElementById('responses');
            
            responsesContainer.style.setProperty('--cards-per-row', cardsPerRow);

            if (autoResize) {
                const cards = document.querySelectorAll('.card');
                cards.forEach(card => {
                    const windowHeight = window.innerHeight;
                    const containerTop = responsesContainer.offsetTop;
                    const optimalHeight = Math.max(
                        (windowHeight - containerTop - 40) / cardsPerRow,
                        300 // 最小高度
                    );
                    card.style.height = `${optimalHeight}px`;
                });
            } else {
                const cards = document.querySelectorAll('.card');
                cards.forEach(card => {
                    card.style.height = 'auto';
                    card.style.minHeight = '300px';
                });
            }
        }

        async function createAgentRequest(agentName, prompt, userInput) {
            return new Promise(async (resolve, reject) => {
                const startTime = new Date();
                try {
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${API_KEY}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: "gpt-4o-mini",
                            messages: [
                                { role: "system", content: prompt },
                                { role: "user", content: userInput }
                            ],
                            temperature: 0.7,
                            max_tokens: 1000
                        })
                    });

                    const data = await response.json();
                    const endTime = new Date();
                    resolve({
                        agentName,
                        reply: data.choices[0].message.content,
                        timeElapsed: (endTime - startTime) / 1000
                    });
                } catch (error) {
                    reject({ agentName, error: error.message });
                }
            });
        }

        async function processAgentRequests(userInput) {
            const requests = Object.entries(agents).map(([agentName, prompt]) => 
                createAgentRequest(agentName, prompt, userInput)
            );

            document.getElementById('responses').innerHTML = '';
            Object.keys(agents).forEach(agentName => {
                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `
                    <h3>${agentName}</h3>
                    <div class="completion-time">等待中...</div>
                    <div class="card-content">等待响应中...</div>
                `;
                document.getElementById('responses').appendChild(card);
            });

            updateLayout();

            const results = await Promise.allSettled(requests);
            
            results.forEach((result, index) => {
                const agentName = Object.keys(agents)[index];
                const card = document.querySelectorAll('.card')[index];

                if (result.status === 'fulfilled') {
                    const { reply, timeElapsed } = result.value;
                    card.querySelector('.card-content').innerHTML = formatText(reply); // 格式化返回的文本
                    card.querySelector('.completion-time').textContent = 
                        `${timeElapsed.toFixed(2)}s`;
                } else {
                    card.querySelector('.card-content').textContent = `Error: ${result.reason.error}`;
                    card.querySelector('.completion-time').textContent = '失败';
                }
            });
        }

        function formatText(text) {
            // 富文本转换：支持换行、加粗、斜体等
            text = text.replace(/\n/g, "<br>");
            text = text.replace(/\*\*(.*?)\*\*/g, "<b>$1</b>");
            text = text.replace(/\*(.*?)\*/g, "<i>$1</i>");
            return text;
        }

        async function getResponses() {
            const userInput = document.getElementById('userInput').value.trim();
            if (!userInput) {
                alert('请输入问题');
                return;
            }

            document.getElementById('submitBtn').disabled = true;
            try {
                await processAgentRequests(userInput);
            } finally {
                document.getElementById('submitBtn').disabled = false;
            }
        }

        document.getElementById('cards-per-row').addEventListener('change', updateLayout);
        document.getElementById('auto-resize').addEventListener('change', updateLayout);
        window.addEventListener('resize', updateLayout);

        updateLayout();
    </script>
</body>
</html>
