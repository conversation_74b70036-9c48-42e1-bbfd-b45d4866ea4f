import requests
import concurrent.futures

# 直接设置 API 密钥
api_key = "sk-VqeLb5tZ86GhwCznTou1kXfEmZVEwGzia5wtVFHFZmxD2zMC"
api_url = "https://api.chatanywhere.tech/v1/chat/completions"  # 假设新的 API 使用这个端点

def call_agent(agent_name, system_prompt, user_input, model="gpt-4o-mini", temperature=0.7, max_tokens=1000):
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        response = requests.post(api_url, json=data, headers=headers)

        if response.status_code == 200:
            return agent_name, response.json()["choices"][0]["message"]["content"]
        else:
            return agent_name, f"Error: {response.status_code} - {response.text}"

    except Exception as e:
        return agent_name, f"Error: {str(e)}"

def main():
    agents = {
        "PrincipleBot": "你是一个专家，专门分析事物的基本原理，揭示事物运作的核心机制。",
        "EssenceBot": "你是一个哲学家，擅长分析事物的本质，深刻理解其内在特性和根本属性。",
        "CausalityBot": "你是一个因果关系分析师，专注于识别和解释事物之间的因果链条。",
        "ProcessBot": "你是一个任务流程专家，擅长设计和分析复杂任务的执行步骤和决策点。",
        "SystemicBot": "你是一个系统思维专家，能够从全局视角分析问题，揭示系统内各部分的相互作用与依赖。",
        "FrameworkBot": "你是一个框架构建者，专注于建立适用于特定问题的结构化框架，分析各要素之间的关系。",
        "StructureBot": "你是一个结构分析师，擅长识别问题的层次结构，理解不同部分的组织方式与影响。",
        "AssociationBot": "你是一个关联分析专家，专注于揭示事物之间的关联性，分析不同因素如何交织在一起。",
        "CommonSenseBot": "你是一个常识专家，能够基于常识给出问题的简单解决方案。",
        "PreKnowledgeBot": "你是一个知识背景专家，负责识别并总结解决该问题所需的前置知识和背景。",
        "SystematicThinkerBot": "你是一个系统性思维专家，能够从全局和多维度角度分析复杂问题，识别各个因素的相互影响。",
        "CriticalThinkerBot": "你是一个批判性思维专家，擅长质疑现有观点，从不同角度审视问题并提出创新解决方案。",
        "FlexibleThinkerBot": "你是一个灵活性思维专家，能够快速适应变化并在不确定性中找到创新的解决方案。",
        "InterdisciplinaryThinkerBot": "你是一个跨学科思维专家，能够融合不同领域的知识，提供多角度的解决方案。",
        "DataDrivenThinkerBot": "你是一个数据驱动思维专家，专注于通过数据分析做出科学决策，优化方案。",
        "LongTermThinkerBot": "你是一个长期思维专家，能够权衡短期与长期目标，提出面向未来的战略规划。"
    }

    user_input = input("请输入您的问题：").strip()
    if not user_input:
        print("请输入有效的问题。")
        return

    print("\n----- 各个 Agent 的回复 -----")
    
    # 使用 ThreadPoolExecutor 来并行执行任务
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for agent_name, system_prompt in agents.items():
            futures.append(executor.submit(call_agent, agent_name, system_prompt, user_input))

        # 获取结果
        for future in concurrent.futures.as_completed(futures):
            agent_name, reply = future.result()
            print(f"[{agent_name}] 的回复:\n{reply}")

if __name__ == "__main__":
    main()
