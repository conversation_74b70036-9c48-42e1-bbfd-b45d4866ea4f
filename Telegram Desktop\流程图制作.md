流程图制作

“使用XML语法绘制[替换为主题]流程图，包含开始、决策节点、操作步骤和结束标志。”

示例

{
<mxGraphModel dx="1050" dy="600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
  <root>
    <mxCell id="0" />
    <mxCell id="1" parent="0" />
    <mxCell id="start-node" value="开始：环境刺激" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
      <mxGeometry x="365" y="40" width="120" height="60" as="geometry" />
    </mxCell>
    <mxCell id="process-sensation" value="操作：感觉器官接收（眼、耳、鼻、舌、身）" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
      <mxGeometry x="340" y="140" width="170" height="70" as="geometry" />
    </mxCell>
    <mxCell id="decision-attention" value="决策：是否引起注意？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
      <mxGeometry x="350" y="250" width="150" height="80" as="geometry" />
    </mxCell>
    <mxCell id="process-interpretation" value="操作：大脑解释和组织感觉信息（结合经验、期望）" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
      <mxGeometry x="340" y="370" width="170" height="80" as="geometry" />
    </mxCell>
    <mxCell id="decision-response" value="决策：是否需要响应？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
      <mxGeometry x="350" y="490" width="150" height="80" as="geometry" />
    </mxCell>
    <mxCell id="process-action" value="操作：执行行为响应" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
      <mxGeometry x="560" y="495" width="120" height="70" as="geometry" />
    </mxCell>
    <mxCell id="end-perception-action" value="结束：形成知觉并响应" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
      <mxGeometry x="560" y="610" width="120" height="60" as="geometry" />
    </mxCell>
    <mxCell id="end-perception-only" value="结束：形成知觉（无显式行为）" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
      <mxGeometry x="350" y="610" width="150" height="60" as="geometry" />
    </mxCell>
    <mxCell id="end-ignored" value="结束：刺激被忽略/过滤" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
      <mxGeometry x="160" y="260" width="140" height="60" as="geometry" />
    </mxCell>
    <mxCell id="edge-start-sensation" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="start-node" target="process-sensation">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>
    <mxCell id="edge-sensation-attention" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="process-sensation" target="decision-attention">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>
    <mxCell id="edge-attention-interpretation" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;align=left;verticalAlign=bottom;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="decision-attention" target="process-interpretation">
      <mxGeometry relative="1" as="geometry">
        <mxPoint x="425" y="350" as="targetPoint" />
        <Array as="points">
          <mxPoint x="425" y="350" />
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="edge-interpretation-response" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="process-interpretation" target="decision-response">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>
    <mxCell id="edge-response-action" value="是" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;align=right;verticalAlign=middle;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="decision-response" target="process-action">
      <mxGeometry relative="1" as="geometry">
        <mxPoint x="530" y="530" as="targetPoint" />
        <Array as="points">
          <mxPoint x="530" y="530" />
        </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="edge-action-end" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="process-action" target="end-perception-action">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>
    <mxCell id="edge-response-end" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;align=left;verticalAlign=bottom;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="decision-response" target="end-perception-only">
      <mxGeometry relative="1" as="geometry">
         <Array as="points">
           <mxPoint x="425" y="590"/>
         </Array>
      </mxGeometry>
    </mxCell>
    <mxCell id="edge-attention-ignored" value="否" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;align=right;verticalAlign=middle;endArrow=classic;endFill=1;strokeColor=#000000;" edge="1" parent="1" source="decision-attention" target="end-ignored">
      <mxGeometry relative="1" as="geometry">
        <mxPoint x="300" y="290" as="targetPoint" />
        <Array as="points">
           <mxPoint x="300" y="290"/>
         </Array>
      </mxGeometry>
    </mxCell>
  </root>
</mxGraphModel>
}