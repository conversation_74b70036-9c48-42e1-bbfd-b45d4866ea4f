package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/go-co-op/gocron"
)

// Config struct 定义配置结构体
type Config struct {
	WSURL               string `json:"ws_url"`
	InfluxDBURL         string `json:"influx_db_url"`
	InfluxDBToken       string `json:"influx_db_token"`
	InfluxDBOrg         string `json:"influx_db_org"`
	InfluxDBBucket      string `json:"influx_db_bucket"`
	ChunkSize           int    `json:"chunk_size"`
	ReconnectInterval   string `json:"reconnect_interval"`
	PingInterval        string `json:"ping_interval"`
	ReadTimeout         string `json:"read_timeout"`
	SymbolCheckInterval string `json:"symbol_check_interval"`
}

// ParsedConfig 结构体用于存储解析后的配置
type ParsedConfig struct {
	WSURL               string
	InfluxDBURL         string
	InfluxDBToken       string
	InfluxDBOrg         string
	InfluxDBBucket      string
	ChunkSize           int
	ReconnectInterval   time.Duration
	PingInterval        time.Duration
	ReadTimeout         time.Duration
	SymbolCheckInterval time.Duration
}

var (
	symbols []string
	config  ParsedConfig
)

// 读取配置文件，如果没有找到文件或解析失败，使用默认值
func loadConfig(filename string) ParsedConfig {
	defaultConfig := ParsedConfig{
		WSURL:               "wss://fstream.binance.com/ws",
		InfluxDBURL:         "http://localhost:8086",
		InfluxDBToken:       "YOUR_INFLUXDB_TOKEN",
		InfluxDBOrg:         "YOUR_INFLUXDB_ORG",
		InfluxDBBucket:      "YOUR_INFLUXDB_BUCKET",
		ChunkSize:           150,
		ReconnectInterval:   5 * time.Second,
		PingInterval:        30 * time.Second,
		ReadTimeout:         35 * time.Second,
		SymbolCheckInterval: 1 * time.Hour,
	}

	file, err := os.Open(filename)
	if err != nil {
		log.Printf("Could not open config file: %v, using default values.", err)
		return defaultConfig
	}
	defer file.Close()

	var config Config
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		log.Printf("Error decoding config file: %v, using default values.", err)
		return defaultConfig
	}

	parsedConfig := ParsedConfig{
		WSURL:          config.WSURL,
		InfluxDBURL:    config.InfluxDBURL,
		InfluxDBToken:  config.InfluxDBToken,
		InfluxDBOrg:    config.InfluxDBOrg,
		InfluxDBBucket: config.InfluxDBBucket,
		ChunkSize:      config.ChunkSize,
	}

	// 解析时间持续字段
	var parseErr error
	parsedConfig.ReconnectInterval, parseErr = time.ParseDuration(config.ReconnectInterval)
	if parseErr != nil {
		log.Printf("Error parsing ReconnectInterval: %v, using default value.", parseErr)
		parsedConfig.ReconnectInterval = defaultConfig.ReconnectInterval
	}

	parsedConfig.PingInterval, parseErr = time.ParseDuration(config.PingInterval)
	if parseErr != nil {
		log.Printf("Error parsing PingInterval: %v, using default value.", parseErr)
		parsedConfig.PingInterval = defaultConfig.PingInterval
	}

	parsedConfig.ReadTimeout, parseErr = time.ParseDuration(config.ReadTimeout)
	if parseErr != nil {
		log.Printf("Error parsing ReadTimeout: %v, using default value.", parseErr)
		parsedConfig.ReadTimeout = defaultConfig.ReadTimeout
	}

	parsedConfig.SymbolCheckInterval, parseErr = time.ParseDuration(config.SymbolCheckInterval)
	if parseErr != nil {
		log.Printf("Error parsing SymbolCheckInterval: %v, using default value.", parseErr)
		parsedConfig.SymbolCheckInterval = defaultConfig.SymbolCheckInterval
	}

	log.Printf("Config loaded and parsed: %+v", parsedConfig)
	return parsedConfig
}

// 获取币安的交易对信息
func fetchBinanceSymbols() ([]string, error) {
	resp, err := http.Get("https://fapi.binance.com/fapi/v1/exchangeInfo")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}

	var perpetualSymbols []string
	for _, s := range result["symbols"].([]interface{}) {
		symbolInfo := s.(map[string]interface{})
		if symbolInfo["contractType"] == "PERPETUAL" &&
			symbolInfo["status"] == "TRADING" &&
			symbolInfo["quoteAsset"] == "USDT" {
			perpetualSymbols = append(perpetualSymbols, symbolInfo["symbol"].(string))
		}
	}
	return perpetualSymbols, nil
}

// 写入到InfluxDB
func writeToInfluxDB(symbol string, data map[string]interface{}) {
	client := influxdb2.NewClient(config.InfluxDBURL, config.InfluxDBToken)
	defer client.Close()

	writeAPI := client.WriteAPIBlocking(config.InfluxDBOrg, config.InfluxDBBucket)

	ctx := context.Background()

	open, _ := strconv.ParseFloat(data["o"].(string), 64)
	high, _ := strconv.ParseFloat(data["h"].(string), 64)
	low, _ := strconv.ParseFloat(data["l"].(string), 64)
	close, _ := strconv.ParseFloat(data["c"].(string), 64)
	volume, _ := strconv.ParseFloat(data["v"].(string), 64)
	quoteVolume, _ := strconv.ParseFloat(data["q"].(string), 64)
	takerBuyVolume, _ := strconv.ParseFloat(data["V"].(string), 64)
	takerBuyQuoteVolume, _ := strconv.ParseFloat(data["Q"].(string), 64)
	count := int(data["n"].(float64))
	closeTime := strconv.FormatInt(int64(data["T"].(float64)), 10) // close_time 转为字符串

	p := influxdb2.NewPoint("kline_1m",
		map[string]string{"symbol": symbol},
		map[string]interface{}{
			"open":                   open,
			"high":                   high,
			"low":                    low,
			"close":                  close,
			"volume":                 volume,
			"quote_volume":           quoteVolume,
			"taker_buy_volume":       takerBuyVolume,
			"taker_buy_quote_volume": takerBuyQuoteVolume,
			"count":                  count,
			"close_time":             closeTime, // close_time 作为字符串字段
		},
		time.Unix(0, int64(data["t"].(float64))*1_000_000),
	)

	if err := writeAPI.WritePoint(ctx, p); err != nil {
		log.Printf("Error writing to InfluxDB: %v\n", err)
	}
}

// 处理WebSocket消息
func handleMessage(message []byte) {
	var data map[string]interface{}
	if err := json.Unmarshal(message, &data); err != nil {
		log.Printf("Error parsing message: %v\n", err)
		return
	}

	if event, ok := data["e"].(string); ok && event == "kline" {
		kline := data["k"].(map[string]interface{})
		if kline["x"].(bool) {
			symbol := data["s"].(string)
			go writeToInfluxDB(symbol, kline)
		}
	} else {
		log.Printf("Received non-kline message: %s\n", message)
	}
}

// 为每个分组创建 WebSocket 连接并订阅，自动重连
func connectWebSocketForChunk(chunk []string, id int) {
	for {
		u, err := url.Parse(config.WSURL)
		if err != nil {
			log.Printf("Error parsing ws_url: %v. Using default value.\n", err)
			u = &url.URL{Scheme: "wss", Host: "fstream.binance.com", Path: "/ws"}
		}

		conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
		if err != nil {
			log.Printf("Failed to connect to WebSocket for chunk %d: %v. Retrying in %v...\n", id, err, config.ReconnectInterval)
			time.Sleep(config.ReconnectInterval)
			continue
		}
		defer conn.Close()

		params := make([]string, len(chunk))
		for j, symbol := range chunk {
			params[j] = fmt.Sprintf("%s@kline_1m", strings.ToLower(symbol))
		}

		subscribeMessage := map[string]interface{}{
			"method": "SUBSCRIBE",
			"params": params,
			"id":     id,
		}

		if err := conn.WriteJSON(subscribeMessage); err != nil {
			log.Printf("Error subscribing to symbols in chunk %d: %v\n", id, err)
			time.Sleep(config.ReconnectInterval)
			continue
		} else {
			log.Printf("Subscribed to symbols in chunk %d: %v\n", id, chunk)
		}

		pingTicker := time.NewTicker(config.PingInterval)
		defer pingTicker.Stop()

		readTimer := time.NewTimer(config.ReadTimeout)
		defer readTimer.Stop()

		messageChan := make(chan []byte)
		errorChan := make(chan error)

		go func() {
			for {
				_, message, err := conn.ReadMessage()
				if err != nil {
					errorChan <- err
					return
				}
				messageChan <- message
			}
		}()

		for {
			select {
			case <-pingTicker.C:
				if err := conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil {
					log.Printf("Error sending ping in chunk %d: %v. Reconnecting...\n", id, err)
					goto reconnect
				}
			case message := <-messageChan:
				if !readTimer.Stop() {
					<-readTimer.C
				}
				readTimer.Reset(config.ReadTimeout)

				go handleMessage(message)
			case err := <-errorChan:
				log.Printf("Error reading message in chunk %d: %v. Reconnecting...\n", id, err)
				goto reconnect
			case <-readTimer.C:
				log.Printf("Read timeout in chunk %d. Reconnecting...\n", id)
				goto reconnect
			}
		}

	reconnect:
		conn.Close()
		time.Sleep(config.ReconnectInterval)
	}
}

// 订阅交易对的K线，每个分组使用一个 WebSocket 连接
func subscribeToSymbols(symbolsToSubscribe []string) {
	for i := 0; i < len(symbolsToSubscribe); i += config.ChunkSize {
		end := i + config.ChunkSize
		if end > len(symbolsToSubscribe) {
			end = len(symbolsToSubscribe)
		}
		chunk := symbolsToSubscribe[i:end]

		go connectWebSocketForChunk(chunk, i/config.ChunkSize+1)
	}
}

// 定时检查新的交易对
func checkForNewSymbols(currentSymbols []string) {
	log.Printf("Checking for new symbols at %s\n", time.Now())

	newSymbols, err := fetchBinanceSymbols()
	if err != nil {
		log.Printf("Error fetching symbols: %v\n", err)
		return
	}

	addedSymbols := difference(newSymbols, currentSymbols)

	if len(addedSymbols) > 0 {
		log.Printf("New symbols detected: %v, updating subscription...\n", addedSymbols)
		symbols = append(symbols, addedSymbols...)
		subscribeToSymbols(addedSymbols)
	} else {
		log.Println("No new symbols detected.")
	}
}

// 计算两个切片的差集 (a - b)
func difference(a, b []string) []string {
	mb := make(map[string]struct{}, len(b))
	for _, x := range b {
		mb[x] = struct{}{}
	}
	var diff []string
	for _, x := range a {
		if _, found := mb[x]; !found {
			diff = append(diff, x)
		}
	}
	return diff
}

// 主函数
func main() {
	// 加载配置
	config = loadConfig("config.json")

	// 获取初始交易对并启动WebSocket
	var err error
	symbols, err = fetchBinanceSymbols()
	if err != nil {
		log.Fatalf("Error fetching symbols: %v\n", err)
	}

	subscribeToSymbols(symbols) // 初始订阅所有交易对

	// 将初始交易对列表传递给 checkForNewSymbols 函数
	checkForNewSymbols(symbols)

	// 启动定时任务
	s := gocron.NewScheduler(time.UTC)
	s.Every(config.SymbolCheckInterval).Do(func() { checkForNewSymbols(symbols) })
	s.StartAsync()

	// 保持程序运行
	select {}
}
