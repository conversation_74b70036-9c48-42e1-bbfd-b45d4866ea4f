<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【详细版】多指标技术分析图表</title>
    <style>
        /* 确保图表容器和body占满屏幕，无边距 */
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: #131722; /* 设置背景色与图表主题一致 */
        }
        /* 定义图表容器的尺寸 */
        .tv-chart-container {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>

    <!-- 1. 用于承载 TradingView 图表的 HTML 元素 -->
    <div id="all_in_one_chart" class="tv-chart-container"></div>

    <!-- 2. 引入 TradingView 官方的 tv.js 库 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    
    <!-- 3. 初始化并配置图表小部件 -->
    <script type="text/javascript">
        new TradingView.widget({
            // --- 核心配置 ---
            "container_id": "all_in_one_chart", // 必须和上面div的id一致
            "symbol": "BINANCE:BTCUSDT",         // 默认交易对，您可以换成任何您想分析的币种
            "interval": "60",                    // 【关键】默认时间周期: "60" 代表 60分钟 (1小时)
            "autosize": true,                    // 图表将自动填充容器大小
            "locale": "zh_CN",                   // 界面语言: 中文
            "theme": "dark",                     // 主题: 深色
            "style": "1",                        // K线样式: 1=K线, 2=空心K线, 3=线形图

            // --- 功能开关 ---
            "allow_symbol_change": true,         // 允许用户在图表上搜索和切换交易对
            "enable_publishing": false,          // 禁用图表发布按钮
            "hide_side_toolbar": false,          // 不隐藏左侧的绘图工具栏
            "details": true,                     // 显示商品详情（开/高/低/收价格）
            
            // --- 【玩法核心】预加载的技术指标 ---
            "studies": [
                "Moving Average Exponential@tv-basicstudies", // 指数移动平均线 (EMA) 1
                "Moving Average Exponential@tv-basicstudies", // 指数移动平均线 (EMA) 2
                "BollingerBands@tv-basicstudies",             // 布林带
                "RSI@tv-basicstudies",                        // 相对强弱指数
                "MACD@tv-basicstudies",                       // MACD
                "IchimokuCloud@tv-basicstudies"               // 一目均衡表 (云图)
                // 注意: 成交量(Volume)通常会默认加载，如果未显示可在此添加 "Volume@tv-basicstudies"
            ],

            // --- 【玩法核心】自定义技术指标的参数 ---
            "studies_overrides": {
                // 第一个EMA的设置 (快线)
                "moving average exponential.ma.length": 21,
                "moving average exponential.ma.color": "#FFEB3B", // 黄色
                "moving average exponential.ma.linewidth": 2,

                // 第二个EMA的设置 (慢线) - 通过 plot_1 区分
                "moving average exponential.ma_1.length": 55,
                "moving average exponential.ma_1.color": "#2196F3", // 蓝色
                "moving average exponential.ma_1.linewidth": 2,

                // RSI 设置
                "relative strength index.rsi.length": 14,
                "relative strength index.rsi.color": "#FF9800", // 橙色

                // MACD 颜色设置
                "moving average convergence divergence.fast length": 12,
                "moving average convergence divergence.slow length": 26,
                "moving average convergence divergence.macd.color": "#4CAF50", // MACD线 (绿色)
                "moving average convergence divergence.signal.color": "#F44336", // 信号线 (红色)

                // 布林带颜色设置
                "bollinger bands.basis.color": "#9C27B0",  // 中轨线 (紫色)
                "bollander bands.upper.color": "#FFFFFF", // 上轨线 (白色)
                "bollander bands.lower.color": "#FFFFFF"  // 下轨线 (白色)
            },
            
            // --- 【玩法核心】深度自定义图表外观 ---
            "overrides": {
                // K线居中 (在图表右侧留出15%的空白)
                "paneProperties.rightMargin": 15,

                // 主要窗格和K线颜色
                "paneProperties.background": "#131722", // 主背景色
                "paneProperties.vertGridProperties.color": "#363c4e", // 垂直网格线
                "paneProperties.horzGridProperties.color": "#363c4e", // 水平网格线
                "mainSeriesProperties.candleStyle.upColor": "#26a69a",   // 阳线颜色 (涨)
                "mainSeriesProperties.candleStyle.downColor": "#ef5350", // 阴线颜色 (跌)
                "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a", // 上影线
                "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350"  // 下影线
            }
        });
    </script>

</body>
</html>