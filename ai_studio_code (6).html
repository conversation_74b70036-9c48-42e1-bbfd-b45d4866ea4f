<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PYTHIA/USD (720x480) - K线图</title>
    <style>
        /* 关键CSS: 使用Flexbox实现页面居中 */
        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            display: flex;           /* 启用Flexbox布局 */
            justify-content: center; /* 水平居中 */
            align-items: center;     /* 垂直居中 */
            background-color: #1e222d; /* 设置一个背景色，衬托图表 */
        }
    </style>
</head>
<body>

    <!-- 用于承载图表的容器 -->
    <div id="chart_container"></div>

    <!-- 引入 TradingView 官方的 tv.js 库 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    
    <!-- 初始化并配置图表小部件 -->
    <script type="text/javascript">
        new TradingView.widget({
            // --- 【关键修改】尺寸配置 ---
            "width": 720,
            "height": 480,
            "autosize": false, // 必须设置为 false 才能使用上面的自定义宽高

            // --- 核心配置 ---
            "container_id": "chart_container",
            "symbol": "CRYPTO:PYTHIAUSD",
            "interval": "60", // 1小时周期
            "locale": "zh_CN",
            "theme": "dark",

            // --- UI功能配置 (只保留截图按钮) ---
            "hide_top_toolbar": false,
            "disabled_features": [
                "header_symbol_search",
                "header_resolutions",
                "header_chart_type",
                "header_indicators",
                "header_compare",
                "header_saveload",
                "header_fullscreen_button",
                "header_settings",
                "left_toolbar"
            ],
            
            // --- 预加载的技术指标 ---
            "studies": [
                "Volume@tv-basicstudies"
            ],
            
            // --- 视觉样式配置 ---
            "overrides": {
                "paneProperties.rightMargin": 15,
                "paneProperties.background": "#131722",
                "paneProperties.vertGridProperties.color": "#363c4e",
                "paneProperties.horzGridProperties.color": "#363c4e",
                "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350"
            }
        });
    </script>

</body>
</html>