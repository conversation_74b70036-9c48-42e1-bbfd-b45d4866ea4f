# 如何开启你的AI生涯
*一份简明指南*

**吴恩达 (<PERSON> Ng) 精华集锦**
**DeepLearning.AI 创始人**

> “人工智能是新的电力。它将改变和改善人类生活的方方面面。”
> —— 吴恩达

## 目录

### 学习
*   **引言**：AI编程是新时代的“读写能力”。
*   **第一章**：职业发展三步走。
*   **第二章**：为前景广阔的AI职业学习技术技能。
*   **第三章**：为了在AI领域找到工作，你需要学习数学吗？

### 项目
*   **第四章**：规划成功的AI项目。
*   **第五章**：寻找与你职业目标相辅相成的项目。
*   **第六章**：构建一个能展示技能进阶的项目作品集。

### 求职
*   **第七章**：开启你AI求职之路的简单框架。
*   **第八章**：利用信息访谈找到合适的工作。
*   **第九章**：为你找到合适的AI工作。
*   **第十章**：打造AI职业生涯的关键要素。
*   **第十一章**：克服冒名顶替综合症。
*   **结语**：让每一天都有价值。

# 引言
## AI编程是新时代的“读写能力”
今天，我们认为很多人会读会写是理所当然的。我希望有一天，人们会编写代码——特别是为AI编写代码——也会变得同样普遍。

几百年前，社会并不认为语言读写能力是一项必要的技能。只有少数人学习阅读和写作，而其他人则让他们代劳。读写能力的普及花费了几个世纪，而现在我们的社会因此变得更加富裕。

语言促成了人与人之间深度的沟通。代码则是人与机器之间最深层次的沟通形式。随着机器在日常生活中变得越来越重要，这种沟通也变得愈发关键。

传统的软件工程——编写明确告诉计算机执行步骤序列的程序——一直是通往代码读写能力的主要路径。许多入门编程课程都以制作视频游戏或建立网站为例。但是，AI、机器学习和数据科学提供了一种新的范式，计算机可以从数据中提取知识。这项技术为学习编程提供了一条更好的途径。

许多个周日，我都会去邻里的披萨店买一块披萨。柜台后的那位先生没什么理由去学习如何制作视频游戏或编写自己的网站软件（除了个人成长和获得新技能的乐趣）。

但是，即便是对于一位披萨师傅来说，AI和数据科学也具有巨大的价值。一个线性回归模型或许能帮助他更好地预估需求，从而优化餐厅的人员配置和供应链。他可以更好地预测夏威夷披萨——我的最爱！——的销量，这样他就能提前准备更多的夏威夷披萨，减少顾客等待的时间。

几乎任何产生数据的场景都能找到AI和数据科学的用武之地。因此，相比于传统的软件工程，各行各业的专业人士能为定制化的AI应用和数据驱动的洞察找到更多的用途。这使得AI导向的编程能力比传统的编程能力更有价值。它可以让无数人利用数据使自己的生活更加富裕。

我希望，构建基础AI应用的愿景，甚至比构建基础传统软件的愿景更能鼓励人们学习编程。如果社会能像拥抱读写能力一样拥抱这种新的能力，我们所有人都将受益。

# 第一章：职业发展三步走
AI的迅速崛起导致了AI工作岗位的激增，许多人正在这个领域开启激动人心的职业生涯。职业是一段长达数十年的旅程，其道路并非一帆风顺。多年来，我有幸见证了成千上万的学生以及大小公司的工程师们在AI领域的职业导航。

这里有一个规划你自己路线的框架。

职业成长的三个关键步骤是：**学习基础技能**、**参与项目实践**（以深化技能、构建作品集并创造影响），以及**找到一份工作**。这些步骤是层层递进的：

### 学习
初期，你专注于**学习基础技能**。带有“学习”图标的章节涵盖了关于学习基础技术技能的主题。

### 项目
在掌握了基础技术技能后，你将开始**参与项目实践**。在此期间，你也会持续学习。带有“项目”图标的章节专注于项目。

### 求职
之后，你将致力于**找到一份工作**。在此过程中，你将继续学习并参与有意义的项目。带有“求职”图标的章节专注于求职。

这些阶段适用于广泛的职业，但AI领域有其独特的要素。例如：

### 学习基础技能是一个贯穿职业生涯的过程：
AI尚处于萌芽阶段，许多技术仍在不断发展。虽然机器学习和深度学习的基础日趋成熟——通过课程学习是掌握它们的有效方式——但在这些基础之上，与日新月异的技术保持同步，在AI领域比在那些更成熟的领域更为重要。

### 从事项目通常意味着与缺乏AI专业知识的利益相关者合作：
这使得寻找合适的项目、评估项目的时间表和投资回报，以及设定预期变得具有挑战性。此外，AI项目高度迭代的特性给项目管理带来了特殊的挑战：当你事先不知道需要多长时间才能达到目标准确率时，你该如何制定一个系统构建计划？即使系统达到了目标，为了应对部署后的“漂移”，可能还需要进一步的迭代。

### 对AI技能和工作角色的看法不一：
虽然在AI领域找工作与其他行业类似，但也存在重要差异。许多公司仍在试图弄清楚他们需要哪些AI技能，以及如何招聘拥有这些技能的人才。你做过的项目可能与面试官见过的任何东西都有显著不同，你可能需要向潜在雇主解释你工作中的某些元素。

在你经历每一步时，你也应该建立一个互助的社群。拥有能够帮助你——并且你也努力去帮助——的朋友和盟友，会让这条路变得更容易。无论你是刚刚起步，还是已经在这条路上走了很多年，这都是真理。

# 第二章：为前景广阔的AI职业学习技术技能
在上一章中，我介绍了构建AI职业生涯的三个关键步骤：学习基础技术技能、参与项目实践和找到一份工作，所有这些都离不开社群的支持。在本章中，我想更深入地探讨第一步：学习基础技能。

关于AI的研究论文已经发表得比任何人一生能读完的都多。因此，在学习时，优先选择主题至关重要。我相信，对于一个技术性的机器学习职业来说，最重要的主题是：

**基础机器学习技能：** 例如，理解线性回归、逻辑回归、神经网络、决策树、聚类和异常检测等模型非常重要。除了具体模型，更重要的是理解机器学习工作原理的核心概念，如偏差/方差、成本函数、正则化、优化算法和误差分析。

**深度学习：** 这已经成为机器学习中如此大的一部分，以至于没有对它的一些理解就很难在该领域脱颖而出！了解神经网络的基础知识、使其工作的实用技巧（如超参数调整）、卷积网络、序列模型和Transformer是很有价值的。

**与机器学习相关的数学：** 关键领域包括线性代数（向量、矩阵及其各种操作）以及概率与统计（包括离散和连续概率、标准概率分布、独立性和贝叶斯法则等基本规则，以及假设检验）。此外，探索性数据分析（EDA）——使用可视化和其他方法系统地探索数据集——是一项被低估的技能。我发现EDA在以数据为中心的AI开发中特别有用，其中分析错误和获得洞察力确实有助于推动进展！最后，对微积分的基本直观理解也会有所帮助。做好机器学习所需的数学知识一直在变化。例如，虽然某些任务需要微积分，但改进的自动微分软件使得在不懂任何微积分的情况下发明和实现新的神经网络架构成为可能。这在十年前几乎是不可能的。

**软件开发：** 虽然仅凭机器学习建模技能你就可以找到工作并做出巨大贡献，但如果你还能编写优秀的软件来实现复杂的AI系统，你的工作机会将会增加。这些技能包括编程基础、数据结构（特别是与机器学习相关的，如数据帧）、算法（包括与数据库和数据操作相关的）、软件设计、熟悉Python，以及熟悉TensorFlow、PyTorch和scikit-learn等关键库。

### 要学的东西很多！
即使你掌握了这份清单上的所有内容，我希望你仍能继续学习，深化你的技术知识。我认识许多机器学习工程师，他们从自然语言处理或计算机视觉等应用领域，或从概率图模型或构建可扩展软件系统等技术领域的更深层次技能中受益。

### 你如何获得这些技能？
互联网上有很多好的内容，理论上，阅读几十个网页是可行的。但当目标是深入理解时，阅读零散的网页是低效的，因为它们往往相互重复，使用不一致的术语（这会减慢你的速度），质量参差不齐，并留下知识空白。这就是为什么一门好的课程——其中知识体系被组织成一个连贯且逻辑性强的形式——通常是掌握有意义知识体系的最省时的方式。当你吸收了课程中可用的知识后，你可以转向研究论文和其他资源。

最后，没有人能在一个周末甚至一个月内把所有需要知道的东西都塞进脑子里。我认识的每一个在机器学习领域表现出色的人都是终身学习者。鉴于我们这个领域变化如此之快，如果你想跟上步伐，除了不断学习，别无选择。

### 你如何能年复一年地保持稳定的学习节奏？
如果你能培养每周学习一点的习惯，你就能以看似不费力的方式取得重大进展。

## 建立新习惯的最佳方式
我最喜欢的书之一是BJ Fogg的《微习惯：小改变带来大成就》。Fogg解释说，建立新习惯的最佳方式是“小处着手，获得成功”，而不是“贪大求全，导致失败”。例如，与其尝试每天锻炼30分钟，他建议立志只做一个俯卧撑，并坚持下去。

这种方法可能对那些想花更多时间学习的人有所帮助。如果你开始要求自己每天看一段教育视频（比如说10秒钟），并且你坚持这样做，那么每天学习的习惯就会自然而然地养成。即使你在这10秒钟内什么也没学到，你也在建立每天学习一点的习惯。在某些日子里，你可能会最终学习一个小时或更长时间。

# 第三章：为了在AI领域找到工作，你需要学习数学吗？
你需要了解多少数学知识才能成为一名机器学习工程师？
数学是AI的一项基础技能吗？多了解一些数学总是好的！但是要学的东西太多了，现实地说，必须分清主次。你可以这样来加强你的数学背景。

为了弄清楚什么是重要的，我发现问自己“你需要知道什么才能做出你想做的工作所需要的决定”是很有用的。在DeepLearning.AI，我们经常问：“一个人需要知道什么才能完成他们的目标？”这个目标可能是构建一个机器学习模型、设计一个系统架构，或是通过一次工作面试。

理解你使用的算法背后的数学通常很有帮助，因为它能让你调试它们。但是，有用的知识深度会随着时间而变化。随着机器学习技术变得成熟、可靠和“交钥匙”，它们需要的调试就越少，对相关数学的较浅理解可能就足以让它们工作。

例如，在机器学习的早期时代，用于求解线性方程组（用于线性回归）的线性代数库还不成熟。我必须了解这些库是如何工作的，这样我才能在不同的库之间进行选择，并避免数值舍入的陷阱。但随着数值线性代数库的成熟，这一点变得不那么重要了。

深度学习仍然是一项新兴技术，所以当你训练一个神经网络而优化算法难以收敛时，理解梯度下降、动量和Adam优化算法背后的数学将帮助你做出更好的决定。同样，如果你的神经网络表现得很奇怪——比如说，它对某种分辨率的图像做出了错误的预测，而对其他分辨率的则不然——理解神经网络架构背行的数学会让你处于一个更有利的位置来找出该怎么做。

当然，我也鼓励由好奇心驱动的学习。如果某件事让你感兴趣，就去学吧，不管它最终会有多大用处！也许这会带来一个创造性的火花或技术上的突破。

# 第四章：规划成功的AI项目
AI架构师最重要的技能之一是能够识别值得投入的想法。接下来的几章将讨论如何寻找和执行项目，以便你获得经验并建立你的作品集。

多年来，我在制造业、医疗保健、气候变化、农业、电子商务、广告和其他行业应用机器学习，乐在其中。一个并非所有这些领域的专家的人，如何能在其中找到有意义的项目呢？这里有五个步骤可以帮助你规划项目。

### 步骤 1
识别一个商业问题（而不是AI问题）。我喜欢找一个领域专家问：“你最希望哪三件事能做得更好？为什么它们现在还没做好？”例如，如果你想将AI应用于气候变化，你可能会发现电网运营商无法准确预测像风能和太阳能这样的间歇性能源未来会产生多少电力。

### 步骤 2
头脑风暴AI解决方案。在我年轻的时候，我常常一有激动人心的想法就立刻付诸行动。有时这样做效果不错，但有时我最终会错过一个可能不需要更多努力就能构建的更好的想法。一旦你理解了一个问题，你就能更有效地进行头脑风暴，寻找潜在的解决方案。例如，为了预测间歇性能源的发电量，我们可能会考虑使用卫星图像更准确地绘制风力涡轮机的位置，使用卫星图像估计风力涡轮机的高度和发电能力，或者使用天气数据更好地预测云层覆盖从而预测太阳辐照度。有时并没有好的AI解决方案，那也没关系。

### 步骤 3
评估潜在解决方案的可行性和价值。你可以通过查看已发表的研究、竞争对手的做法，或者可能通过构建一个快速的概念验证实现来确定一种方法在技术上是否可行。你可以通过咨询领域专家（比如电网运营商，他们可以就上述潜在解决方案的效用提供建议）来确定其价值。

### 步骤 4
确定里程碑。一旦你认为一个项目足够有价值，下一步就是确定要达到的指标。这既包括机器学习指标（如准确率），也包括业务指标（如收入）。机器学习团队通常对学习算法可以优化的指标最为熟悉。但我们可能需要走出我们的舒适区，提出与用户参与度、收入等相关的业务指标。不幸的是，并非每个业务问题都可以简化为优化测试集准确率！如果你无法确定合理的里程碑，这可能是一个信号，表明你需要更多地了解这个问题。一个快速的概念验证可以帮助提供缺失的视角。

### 步骤 5
为资源做预算。仔细考虑完成项目所需的一切，包括数据、人员、时间，以及你可能需要从其他团队获得任何集成或支持。例如，如果你需要资金购买卫星图像，请确保这在预算之内。

从事项目是一个迭代的过程。如果在任何一步，你发现当前的方向是不可行的，就回到前一个步骤，用你的新理解继续前进。有没有一个让你兴奋的领域，AI可能会在其中发挥作用？我希望这些步骤能引导你通过项目工作来探索它——即使你在这个领域还没有深厚的专业知识。AI不会解决所有问题，但作为一个社群，让我们寻找方法，在任何我们能产生积极影响的地方做出贡献。

# 第五章：寻找与你职业目标相辅相成的项目
不言而喻，我们只应该从事那些负责任、合乎道德且对人们有益的项目。但这些限制之外，仍有多种多样的选择。在上一章中，我写了如何识别和规划AI项目。本章和下一章的重点略有不同：着眼于职业发展来挑选和执行项目。

一个富有成果的职业生涯将包括许多项目，希望这些项目在范围、复杂性和影响力上都能随时间增长。因此，**从小处着手是可以的**。利用早期项目来学习，并随着技能的增长逐步挑战更宏大的项目。

当你刚开始时，不要指望别人会把好点子或资源送到你面前。许多人都是从在业余时间做小项目开始的。随着初期的成功——即便是小成功——在你手下积累，你不断增长的技能会提升你提出更好想法的能力，并且说服他人帮助你挑战更大项目也会变得更容易。

### 如果你没有任何项目想法怎么办？
这里有几种产生想法的方法：

*   **加入现有项目。** 如果你发现别人有好点子，就请求加入他们的项目。
*   **持续阅读和与人交流。** 每当我花大量时间阅读、上课或与领域专家交谈时，我都会有新想法。我相信你也会。
*   **专注于一个应用领域。** 许多研究人员正试图推进基础AI技术——比如，通过发明下一代Transformer或进一步扩大语言模型——所以，虽然这是一个令人兴奋的方向，但它也非常困难。但机器学习尚未被应用的应用领域非常广泛！我很幸运能够将神经网络应用于从自动直升机飞行到在线广告的各种领域，部分原因是我在相对较少人从事这些应用时就投身其中。如果你的公司或学校关心某个特定的应用，就去探索机器学习的可能性。这可以让你初窥一个潜在的创造性应用——一个你可以做别人还没做过的独特工作的领域。
*   **发展副业。** 即使你有一份全职工作，一个有趣的项目，无论它最终能否发展成更大的事业，都可以激发创造力并加强与合作者的联系。当我是一名全职教授时，从事在线教育并非我“工作”（即做研究和教学）的一部分。这是一个出于对教育的热情而经常投入的有趣爱好。我早期在家录制视频的经历，后来帮助我以更实质性的方式从事在线教育工作。硅谷充满了从副业开始的创业故事。只要不与你的雇主产生利益冲突，这些项目就可以成为通往更重要事业的垫脚石。

### 有了几个项目想法，你应该投身于哪一个？
这里有一个快速的因素清单供你考虑：

*   **这个项目能帮助你在技术上成长吗？** 理想情况下，它应该足够有挑战性，能够拓展你的技能，但又不能太难，以至于你几乎没有成功的机会。这将使你走上掌握更高技术复杂性的道路。
*   **你有好的队友合作吗？** 如果没有，有没有人可以与你讨论事情？我们从周围的人身上学到很多，好的合作者将对你的成长产生巨大影响。
*   **它能成为一块垫脚石吗？** 如果项目成功，它的技术复杂性和/或商业影响是否会使其成为通向更大项目的有意义的垫脚石？如果这个项目比你以前做过的都大，那么它很可能成为这样一块垫脚石。

最后，避免“分析瘫痪”。花一个月时间来决定是否要做一个需要一周才能完成的项目是没有意义的。在你职业生涯的过程中，你会从事多个项目，所以你将有充足的机会来完善你对什么是有价值的思考。鉴于AI项目可能性巨大，与其采用传统的“准备、瞄准、开火”的方法，你可以通过“准备、开火、瞄准”来加速你的进程。

## 准备、开火、瞄准
从事项目需要在构建什么以及如何进行方面做出艰难的选择。这里有两种截然不同的风格：

*   **准备、瞄准、开火 (Ready, Aim, Fire)：** 仔细计划并进行周密的验证。只有在你对方向有高度信心时才投入并执行。
*   **准备、开火、瞄准 (Ready, Fire, Aim)：** 直接投入开发并开始执行。这让你能够快速发现问题，并在必要时沿途调整。

假设你为零售商构建了一个客服聊天机器人，你认为它也能帮助餐厅。你是应该花时间研究餐厅市场，然后再开始开发，虽然行动缓慢但降低了浪费时间和资源的风险？还是应该立即行动，快速前进，并接受更高的转型或失败风险？

两种方法都有其拥护者，最佳选择取决于具体情况。

当执行成本高昂，且一项研究能揭示一个项目是否有用或有价值时，“准备、瞄准、开火”往往更优越。例如，如果你能头脑风暴出其他几个用例（餐厅、航空公司、电信公司等）并评估这些案例以确定最有希望的一个，那么在投入方向之前花额外的时间可能是值得的。

如果你能以低成本执行，并通过这样做来确定方向是否可行并发现能使其成功的调整，那么“准备、开火、瞄准”往往更好。例如，如果你能快速构建一个原型来弄清楚用户是否想要这个产品，并且在少量工作后取消或转型是可以接受的，那么考虑快速行动是有道理的。当“开一枪”的成本不高时，多开几枪也是有道理的。在这种情况下，这个过程实际上是“准备、开火、瞄准、开火、瞄准、开火、瞄准”。

在就项目方向达成一致后，当涉及到构建作为产品一部分的机器学习模型时，我偏向于“准备、开火、瞄准”。构建模型是一个**迭代过程**。对于许多应用来说，训练和进行误差分析的成本并非高得令人望而却步。此外，进行一项能够揭示合适模型、数据和超参数的研究是非常困难的。因此，快速构建一个端到端的系统并修改它直到它工作良好是有道理的。

但是，当投入一个方向意味着进行昂贵的投资或进入一扇“单向门”（即一个难以逆转的决定）时，提前花更多时间以确保这确实是一个好主意通常是值得的。

# 第六章：构建一个能展示技能进阶的项目作品集
在职业生涯中，你很可能会接连不断地从事项目，每个项目在范围和复杂性上都会有所增长。例如：

1.  **课堂项目：** 最初的几个项目可能是范围狭窄、有预定正确答案的家庭作业。这些通常是很好的学习经历！
2.  **个人项目：** 你可能会接着独自或与朋友一起做一些小规模的项目。例如，你可能会重新实现一个已知的算法，将机器学习应用于一个爱好（比如预测你最喜欢的运动队是否会赢），或者在业余时间在工作中构建一个虽小但有用的系统（比如一个基于机器学习的脚本，帮助同事自动化他们的一些工作）。参加像Kaggle组织的那些竞赛也是一种获得经验的方式。
3.  **创造价值：** 最终，你将获得足够的技能来构建他人能看到更具体价值的项目。这为获得更多资源打开了大门。例如，与其在业余时间开发机器学习系统，它可能会成为你工作的一部分，你可能会获得更多的设备、计算时间、标注预算或人力。
4.  **范围和复杂性的提升：** 成功会相互促进，为更多的技术成长、更多的资源和日益重要的项目机会打开大门。

每个项目都只是漫长旅程中的一步，希望是具有积极影响的一步。此外：

**不要担心起步太小。** 我最早的机器学习研究项目之一是训练一个神经网络，看它能多好地模仿sin(x)函数。它不是很有用，但它是一次很棒的学习经历，使我能够转向更宏大的项目。

**沟通是关键。** 如果你想让别人看到你工作的价值，并信任你，把资源投入到更大的项目中，你需要能够解释你的想法。要启动一个项目，沟通你希望构建的东西的价值将有助于吸引同事、导师和管理者的支持，并帮助他们指出你推理中的缺陷。在你完成之后，清晰地解释你所取得的成就将有助于说服他人为更大的项目敞开大门。

**领导力不仅仅是管理者的事。** 当你达到从事需要团队合作的更大型AI项目的阶段时，你的领导能力将变得更加重要，无论你是否处于正式的领导岗位。我的许多朋友成功地追求了技术而非管理的职业道路，他们通过应用深刻的技术见解来帮助引导项目的能力——例如，何时投资于新的技术架构或收集某种类型的更多数据——使他们成长为领导者，并显著地改进了项目。

> 构建一个项目作品集，特别是能展示你从简单到复杂任务的进步过程的作品集，在找工作时会大有裨益。

# 第七章：开启你AI求职之路的简单框架
找工作有几个可预测的步骤，包括选择你想申请的公司，为面试做准备，最后挑选一个职位并协商薪水和福利。在本章中，我想重点介绍一个对许多AI求职者都很有用的框架，特别是那些从不同领域进入AI的人。

如果你正在考虑你的下一份工作，问问自己：
*   **你是在转换角色吗？** 例如，如果你是一名软件工程师、大学毕业生或物理学家，希望成为一名机器学习工程师，那就是角色转换。
*   **你是在转换行业吗？** 例如，如果你在一家医疗保健公司、金融服务公司或政府机构工作，想去一家软件公司工作，那就是行业转换。

[视觉描述：一个图表展示了一个2x2的“求职”网格。列是“分析师”和“机器学习工程师”。行是“金融服务”和“科技”。水平移动是“角色转换”，垂直移动是“行业转换”，对角线移动是“角色与行业双重转换”。]

一家科技创业公司的产品经理成为同一家公司（或另一家公司）的数据科学家，这是转换了角色。一家制造公司的市场营销人员成为一家科技公司的市场营销人员，这是转换了行业。一家金融服务公司的分析师成为一家科技公司的机器学习工程师，这是既转换了角色又转换了行业。

如果你正在寻找你在AI领域的第一份工作，你可能会发现转换角色或行业比两者同时进行要容易。假设你是那位在金融服务公司工作的分析师：
*   如果你在金融服务领域找到一份数据科学或机器学习的工作，你可以继续利用你的领域特定知识，同时获得AI领域的知识和专长。在这个岗位上工作一段时间后，你将更有能力转换到一家科技公司（如果那仍然是你的目标）。
*   或者，如果你成为一家科技公司的分析师，你可以继续运用你作为分析师的技能，但将它们应用于一个不同的行业。作为科技公司的一员，也让你更容易向同事学习AI的实际挑战、成功的关键技能等等。

如果你正在考虑转换角色，创业公司可能是比大公司更容易实现的地方。虽然有例外，但创业公司通常没有足够的人手来完成所有期望的工作。如果你能帮助完成AI任务——即使那不是你的正式工作——你的工作很可能会受到赞赏。这为可能的角色转换奠定了基础，而无需离开公司。相比之下，在大公司，一个僵化的奖励体系更可能奖励你做好你的本职工作（以及你的经理支持你做你被雇佣来做的工作），但不太可能奖励你在你工作范围之外的贡献。

在你期望的角色和行业（例如，一家科技公司的机器学习工程师）工作一段时间后，你将对该行业中更高级别的该角色的要求有很好的了解。你还将在该行业内建立一个网络来帮助你。因此，未来的求职——如果你选择坚持这个角色和行业——可能会更容易。

换工作时，你正迈向未知，特别是如果你同时转换角色或行业。最有用的工具之一是信息访谈，它可以让你更熟悉一个新的角色和/或行业。我将在下一章中更多地分享这一点。

我感谢FourthBrain（一家DeepLearning.AI的附属机构）的首席执行官Salwa Nur Muhammad，为本章提供了一些想法。

## 克服不确定性
关于未来，我们有很多不知道的事情：我们什么时候能治愈阿尔茨海默病？谁会赢得下一次选举？或者，在商业背景下，我们明年会有多少客户？

世界上发生了这么多变化，许多人对未来感到压力，特别是在找工作方面。我有一种做法可以帮助我重新获得控制感。面对不确定性，我尝试这样做：

1.  列出一份可能发生的情景清单，承认我不知道哪个会成真。
2.  为每种情景制定一个行动计划。
3.  开始执行那些看起来合理的行动。
4.  随着未来的焦点逐渐清晰，定期回顾情景和计划。

例如，在2020年3月的新冠疫情期间，我做了这个情景规划练习。我想象了从新冠疫情中快速（三个月）、中等（一年）和缓慢（两年）的恢复，并为每种情况制定了管理计划。这些计划帮助我优先考虑我能在哪里投入精力。

同样的方法也适用于个人生活。如果你不确定自己是否能通过考试、获得工作机会或被授予签证——所有这些都可能带来压力——你可以写下你在每种可能情景下会怎么做。思考各种可能性并遵循计划可以帮助你有效地驾驭未来，无论发生什么。

**额外收获：** 通过AI和统计学的训练，你可以为每种情景计算一个概率。我是“超级预测”方法的粉丝，在这种方法中，许多专家的判断被综合成一个概率估计。

# 第八章：利用信息访谈找到合适的工作
如果你正准备转换角色（比如，第一次从事机器学习工程师的工作）或行业（比如，第一次在AI科技公司工作），那么关于你的目标工作，你可能有很多不了解的地方。一种被称为信息访谈的技巧是学习的好方法。

信息访谈是指找到你希望了解更多的公司或职位的人，并与他们非正式地谈论他们的工作。这类对话与求职是分开的。实际上，在你准备启动求职之前很久，与那些职位与你的兴趣相符的人进行访谈是很有帮助的。

*   信息访谈与AI尤其相关。因为这个领域在不断发展，许多公司以不一致的方式使用职位头衔。在一家公司，数据科学家的主要工作可能是分析业务数据并在幻灯片上展示结论。在另一家公司，他们可能需要编写和维护生产代码。信息访谈可以帮助你弄清楚某个特定公司的AI人员实际上在做什么。
*   随着AI领域机会的迅速扩大，许多人将是第一次从事AI工作。在这种情况下，信息访谈对于了解工作内容和做好工作所需的技能非常有价值。例如，你可以了解到某个特定公司使用什么算法、部署流程和软件栈。你可能会惊讶——如果你还不熟悉以数据为中心的AI运动——了解到大多数机器学习工程师花多少时间迭代地清洗数据集。

通过提前研究面试对象和公司来为信息访谈做准备，这样你就可以带着深思熟虑的问题去参加。你可能会问：

*   你典型的一周或一天是怎么过的？
*   在这个角色中，最重要的任务是什么？
*   成功最重要的技能是什么？
*   你的团队如何协同工作以实现其目标？
*   招聘流程是怎样的？
*   回顾过去脱颖而出的候选人，是什么让他们闪耀？

找到一个可以面试的人并不总是那么容易，但是今天许多处于高级职位的人在他们刚入行时都曾得到过前辈的帮助，并且许多人渴望将这份善意传递下去。如果你能联系到你网络中已经有人——也许是一个比你先完成转型的朋友，或者是一个和你上同一所学校的人——那就太好了！像Pie & AI这样的聚会也可以帮助你建立你的网络。

最后，要礼貌和专业，并感谢你面试过的人。当你有机会时，也请将这份善意传递下去，帮助后来者。如果你收到来自DeepLearning.AI社群某人的信息访谈请求，我希望你能伸出援手，帮助他们更上一层楼！如果你有兴趣了解更多关于信息访谈的信息，我推荐这篇来自加州大学伯克利分校职业中心的文章。

我曾多次提到你的网络和社群的重要性。你遇到的人，除了提供有价值的信息外，还可以通过将你推荐给潜在雇主而发挥宝贵的作用。

# 第九章：为你找到合适的AI工作
在本章中，我想讨论一些找工作的细节。

典型的求职过程遵循一个相当可预测的路径。
*   通过在线或与朋友交谈来研究职位和公司。
*   可选地，与吸引你的公司里的人安排非正式的信息访谈。
*   直接申请，或者，如果可以的话，从内部人士那里获得推荐。
*   与给你发出邀请的公司进行面试。
*   收到一个或多个录用通知并选择一个。或者，如果你没有收到录用通知，向面试官、人力资源部门、在线讨论区或你网络中任何能帮助你规划下一步的人寻求反馈。

尽管这个过程可能很熟悉，但每次求职都是不同的。这里有一些技巧，可以增加你找到一个支持你事业蓬勃发展并使你能够持续成长的职位的几率。

**关注基础。** 一份引人注目的简历、技术项目作品集和出色的面试表现将为你打开大门。即使你从公司内部某人那里得到了推荐，简历和作品集也将是你与许多不了解你的人的第一次接触。更新你的简历，并确保它清晰地展示了与你想要的职位相关的教育和经验。为你申请的每家公司定制你的沟通方式，以解释为什么你是一个合适的人选。面试前，询问招聘人员面试的预期内容。花时间回顾和练习常见面试问题的答案，复习关键技能，并学习技术材料以确保它们在你脑海中是新鲜的。之后，做笔记以帮助你记住说了些什么。

**以尊重和负责任的态度行事。** 以双赢的心态对待面试和录用谈判。在社交媒体上，愤怒比理性传播得更快，所以一个关于雇主如何低薪对待某人的故事会被放大，而关于雇主如何公平对待某人的故事则不会。绝大多数雇主是道德和公平的，所以不要让关于少数被不公对待个体的故事影响你的方法。如果你要离职，请体面地离开。给你的雇主充足的通知，在你在职的最后一小时里全力以赴，尽你所能地交接未完成的工作，并以一种尊重你被赋予的责任的方式离开。

**选择与谁共事。** 因为你将从事的项目而接受一个职位是很诱人的。但是你将与之共事的队友至少同样重要。我们受到周围人的影响，所以你的同事会产生很大的不同。例如，如果你的朋友吸烟，你吸烟的几率就会增加。我不知道有研究表明这一点，但我很确定，如果你大多数同事都努力工作、持续学习并构建AI以造福全人类，你很可能也会这样做。（顺便说一句，一些大公司在你接受录用通知之前不会告诉你你的队友是谁。在这种情况下，要坚持不懈地去识别并与潜在的队友交谈。严格的政策可能使他们无法满足你的要求，但在我看来，这增加了接受录用通知的风险，因为它增加了你最终与一个不合适的经理或队友共事的几率。）

**从你的社群中获取帮助。** 我们大多数人在职业生涯中只找几次工作，所以我们中很少有人能很熟练地做这件事。然而，总的来说，你身边的社群里的人可能有很多经验。不要害羞地向他们求助。朋友和同事可以提供建议、分享内部知识，并将你推荐给可能提供帮助的其他人。当我申请我的第一个教职时，我从支持我的朋友和导师那里得到了很多帮助，他们给我的许多建议都非常有帮助。

我知道求职过程可能会令人望而生畏。与其把它看作一次巨大的飞跃，不如考虑一种循序渐进的方法。从确定可能的角色和进行几次信息访谈开始。如果这些对话告诉你，在准备申请之前你还有更多的东西要学，那太好了！至少你有一条清晰的前进道路。任何旅程中最重要的部分是迈出第一步，而这一步可以是很小的一步。

# 第十章：打造AI职业生涯的关键要素
AI领域的职业成功之路比我在这本简短的电子书中所能涵盖的要复杂。希望前面的章节能给你前进的动力。
在你规划成功之路时，这里有一些额外的事情需要考虑：

1.  **团队合作：** 当我们处理大型项目时，团队合作比单打独斗更容易成功。与他人协作、影响他人以及被他人影响的能力至关重要。因此，人际交往和沟通技巧真的非常重要。（顺便说一句，我曾经是一个相当糟糕的沟通者。）
2.  **人脉网络：** 我讨厌社交！作为一个内向的人，去派对上微笑并尽可能多地握手是一种近乎恐怖的活动。我宁愿待在家里看书。尽管如此，我很幸运在AI领域找到了许多真诚的朋友；那些我愿意为之两肋插刀，也同样信赖的人。没有人是一座孤岛，拥有一个强大的专业网络可以在你需要帮助或建议的时刻推动你前进。为了取代社交，我发现建立一个社群更有帮助。因此，我不再试图建立我的个人网络，而是专注于建立我所属的社群。这有一个副作用，就是帮助我认识更多的人，并结交更多的朋友。
3.  **求职：** 在建立职业生涯的所有步骤中，这一步往往最受关注。不幸的是，互联网上关于这个有很多不好的建议。（例如，许多文章敦促对潜在雇主采取敌对态度，我认为这没有帮助。）尽管找到一份工作似乎是最终目标，但它只是漫长职业旅程中的一小步。
4.  **个人纪律：** 很少有人会知道你是在周末学习，还是在狂看电视——但随着时间的推移，他们会注意到其中的差别。许多成功人士在饮食、锻炼、睡眠、人际关系、工作、学习和自我照顾方面养成了良好的习惯。这样的习惯帮助他们在保持健康的同时前进。
5.  **利他主义：** 我发现，那些在自己旅程的每一步都致力于提升他人的人，往往会为自己取得更好的结果。在我们为自己打造激动人心的职业生涯的同时，我们如何能帮助他人呢？

# 第十一章：克服冒名顶替综合症
在我们深入本书最后一章之前，我想谈一个严肃的问题：AI领域的新人有时会经历冒名顶替综合症，即某人——无论其在该领域的成功如何——会怀疑自己是不是个骗子，是否真的属于AI社群。我想确保这不会阻碍你或任何其他人在AI领域的成长。

> 让我说清楚：如果你想成为AI社群的一员，那么我张开双臂欢迎你。如果你想加入我们，你就完全属于我们！

据估计，有70%的人在某个时候会经历某种形式的冒名顶替综合症。许多才华横溢的人都曾公开谈论过这种经历，包括前Facebook首席运营官雪莉·桑德伯格、美国前第一夫人米歇尔·奥巴马、演员汤姆·汉克斯和Atlassian联合首席执行官迈克·坎农-布鲁克斯。即使在有成就的人中间，这种情况也发生在我们社群中。如果你自己从未经历过，那太好了！我希望你能加入我，鼓励和欢迎所有想加入我们社群的人。

AI技术复杂，确实有一批聪明能干的人。但人们很容易忘记，要在任何事情上做得好，第一步都是做得很差。如果你在AI领域“栽跟头”成功了——恭喜你，你已经上路了！

我曾经努力理解线性回归背后的数学。当逻辑回归在我的数据上表现异常时，我感到困惑，花了好几天才在我实现的一个基本神经网络中找到一个错误。今天，我仍然觉得许多研究论文难以阅读，而且我最近在调整一个神经网络超参数时犯了一个明显的错误（幸运的是被一个同事工程师发现并修正了）。

所以，如果你也觉得AI的某些部分具有挑战性，没关系。我们都经历过。我保证，每一位发表过开创性AI论文的人，在某个时候都曾与类似的技术挑战作斗争。

### 这里有一些可以帮助你的事情。
*   **你有支持你的导师或同伴吗？** 如果还没有，可以参加Pie & AI或其他活动，使用讨论区，并努力找到一些。如果你的导师或经理不支持你的成长，就去找那些支持你的人。我也在努力研究如何发展一个支持性的AI社群，并希望让寻找和给予支持对每个人来说都更容易。
*   **没有人是万事通。** 认识到你擅长什么。如果你擅长的是理解并向你的朋友解释《The Batch》中十分之一的文章，那么你就已经在路上了！让我们努力让你理解十分之二的文章。

我三岁的女儿（她几乎数不到12）经常试图教我一岁的儿子一些事情。无论你进步到什么程度——如果你至少和一个三岁孩子一样知识渊博——你都可以鼓励和提升你身后的人。这样做也会帮助你，因为你身后的人会认识到你的专业知识，并鼓励你继续发展。当你邀请他人加入AI社群时——我希望你会这样做——这也会减少你对自己是否已经是我们一员的任何疑虑。

AI是我们世界如此重要的一部分，我希望每个想成为其中一员的人都能在我们的社群中感到宾至如归。让我们共同努力，实现这一目标。

# 结语
## 让每一天都有价值
每年我的生日，我都会思考过去的日子和未来的时光。

也许你数学很好；我相信你能通过快速计算回答下面的问题。但请允许我问你一个问题，并请**凭直觉回答，不要计算。**

> 一个典型的人类寿命有多少天？
>
> 20,000天
>
> 100,000天
>
> 100万天
>
> 500万天

当我问朋友们时，许多人选择了一个几十万的数字。（许多其他人忍不住要计算答案，这让我很恼火！）

当我还是一名研究生时，我记得把我的统计数据输入一个死亡率计算器，来计算我的预期寿命。计算器说我可以预期总共活27,649天。这个数字如此之小，让我感到震惊。我用大号字体把它打印出来，贴在我的办公室墙上，作为每日提醒。

这就是我们所有能用来与亲人共度、学习、为未来建设和帮助他人的日子。无论你今天在做什么，它是否值得你生命中三万分之一的价值？