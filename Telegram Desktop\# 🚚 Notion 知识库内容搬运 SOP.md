# 🚚 Notion 知识库内容搬运 SOP

### 📌 目标

将其他博主在 Notion 上发布的知识内容，**高效、合规地整理与转录**到自己的 Notion 知识库中，形成个性化的学习或资料系统。

---

## 🧰 使用工具

* 🧠 **AI 工具**

  * [Google AI Studio](https://aistudio.google.com/prompts/new_chat)
  * [ChatGPT](https://chatgpt.com/)
* 📄 **Notion 在线版**

---

## 🪜 步骤一：准备阶段

### ✅ 1. 打开网页

### ✅ 2. 创建搬运模版（Notion & Docs）

* 在你的 Notion 中新建一个“搬运项目”数据库或模板页，包含以下字段：

  * 原始链接
  * 原作者名（如能获取）
  * 搬运时间
  * 是否已加工/重写
  * 是否标注出处

---

## 🪜 步骤二：信息采集与提取

### ✅ 1. 访问博主原始 Notion 链接

* 使用浏览器打开目标 Notion 页面，检查其复制内容，直接使用鼠标复制。

### ✅ 2. 将内容复制到 Notion

* 将整个页面内容粘贴至 Notion。
* 使用「工具 > 探索」或 AI 插件初步分析结构。
* 可在 Docs 内用不同颜色标注：

  * 重要结构（标题、目录）
  * 内容精华（重点、案例）
  * 可删内容（无关引用、重复）

---

## 🪜 步骤三：内容精炼与加工（使用 AI）

```

### ✅ 1. 内容抄写

> 在 AI 工具中使用 prompt：

```
你是一位内容再创专家，精通将现有文案转化为独特且富有吸引力的新版本。请处理用户输入的文案，运用你的专业技能（如同义词替换、句式变换、语序调整、结构重组等），在不改变核心意义的基础上，赋予其全新的表达方式和风格，以达到显著的原创效果，杜绝任何抄袭嫌疑。
```

### ✅ 2. AI 输出

* 将内容粘贴到使用上面 prompt 的 AI：

  * AI输出内容，点击对话框左下角的复制按钮

---

## 🪜 步骤四：搬运至 Notion（整理入库）

### ✅ 1. 建立新页面或数据库条目

* 粘贴已经加工好的内容

### ✅ 2. 插入图片

* 封面、图片等增强可读性

---

## 🪜 步骤四：检查所有元素是否已经转换为归零食堂的内容

---