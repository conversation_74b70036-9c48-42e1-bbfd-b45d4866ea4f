# claude详细分步操作流程与操作使用手册

请按照以下步骤操作，即可成功安装和使用 Claude Code。

### 安装视频教程，只需要看到安装好 claude code 这一步就行，

https://www.youtube.com/watch?v=n6uk9riIQWg

### 免费领 100u claude code 额度的网站，注册以后点击主界面左边目录的API令牌，然后添加令牌，复制key就行，每天登录免费领 25u 的额度，之前可以github注册的，选现在不行了，能用linux.do注册：

https://anyrouter.top/register?aff=Z5CC

在开始之前，请注意：
您的 ANTHROPIC_AUTH_TOKEN (以 sk- 开头的密钥) 是您的个人凭证，请绝对不要与任何人分享或上传到任何公共网站，例如 GitHub。

第一到第二步骤看视频就行

### 使用技巧：

*   启动虚拟机这里的"lenovo"替换为你的虚拟机用户名
    ```
    wsl -d Ubuntu-24.04 -u lenovo
    ```

*   免授权模式启动，不用点击确认了全自动执行
    ```
    claude --dangerously-skip-permissions
    ```

*   切换到上次对话
    ```
    claude -c
    ```

*   查看对话历史并选择切换
    ```
    claude -r
    ```

*   历史记录指令
    ```
    claude --resume
    ```

---

### 第一步：环境准备 - 安装 Node.js

首先，我们需要确认您的电脑是否安装了 Node.js，并且版本号大于等于 18.0。

1.  打开您的终端（Terminal）。
2.  检查 Node.js 版本，输入以下命令并按回车：
    ```bash
    node --version
    ```
3.  判断结果：
    *   如果显示 v18.x.x 或更高的版本（如 `v20.x.x`），说明您的环境已准备就绪，可以直接跳到第二步。
    *   如果提示 `command not found` (命令未找到) 或版本低于 `v18.0`，请根据您的操作系统执行相应的安装命令：

    *   适用于 Ubuntu / Debian (Linux 用户):
        ```bash
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo bash -
        sudo apt-get install -y nodejs
        ```

    *   适用于 macOS 用户 (使用 Homebrew):
        ```bash
        brew install node
        ```
        *如果您的 macOS 没有安装 Homebrew，终端会引导您安装，您也可以先运行以下命令安装 Homebrew：*
        ```bash
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        ```
4.  安装完成后，再次运行 `node --version` 确认版本号正确。

---

### 第二步：安装 Claude Code 主程序

现在，我们使用 Node.js 的包管理器 (npm) 来安装 Claude Code。

1.  在终端中，输入以下命令并按回车：
    ```bash
    npm install -g @anthropic-ai/claude-code
    ```
2.  安装完成后，运行以下命令验证是否成功：
    ```bash
    claude --version
    ```
    如果能成功显示版本号，说明安装成功。

---

### 第三步：获取密钥并首次运行

在运行程序前，您需要获取一个 API 密钥。

1.  获取 Auth Token：
    *   根据指南，您需要先注册一个账户，然后在“API令牌”页面创建一个新的令牌。
    *   这个令牌（Token）是一串以 `sk-` 开头的字符。请复制并妥善保管好这个令牌。

2.  配置并首次启动：
    *   首先，进入您希望使用 Claude Code 的项目文件夹。例如，如果您的项目在 `~/Documents/my-project`，则运行：
        ```bash
        cd ~/Documents/my-project
        ```
    *   然后，在终端中依次输入并执行以下三条命令。请务必将 `sk-...` 替换为您自己在上一步获得的真实令牌：
        ```bash
        export ANTHROPIC_AUTH_TOKEN=sk-...
        export ANTHROPIC_BASE_URL=https://anyrouter.top
        claude
        ```    *   运行 `claude` 后，程序会启动并进行一些首次配置，根据提示按回车（Enter）确认即可：
        *   选择主题
        *   确认安全须知
        *   使用默认 Terminal 配置
        *   信任当前工作目录

完成这些步骤后，您就可以开始在终端里与 Claude Code 互动了。

---

### 第四步：(推荐) 永久配置，方便后续使用

为了避免每次打开新的终端窗口时都重复输入密钥和地址，建议您将它们设为永久环境变量。

1.  根据您的终端类型，选择一条命令执行。
    *   如果您使用的是 macOS (较新版本) 或 Zsh，请运行：
        ```bash
        echo -e '\nexport ANTHROPIC_AUTH_TOKEN=sk-...' >> ~/.zshrc
        echo -e '\nexport ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.zshrc
        ```
    *   如果您使用的是 Linux 或 Bash，请运行：
        ```bash
        echo -e '\nexport ANTHROPIC_AUTH_TOKEN=sk-...' >> ~/.bashrc
        echo -e '\nexport ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bashrc
        ```
    *   再次提醒： 将 sk-... 替换为您的真实令牌。

2.  重启您的终端，或者运行 source ~/.zshrc (或 source ~/.bashrc) 来让配置生效。

3.  验证永久配置是否成功：
    *   完全关闭并重新打开一个新的终端窗口。
    *   直接进入您的项目目录：`cd /path/to/your/project`
    *   直接运行：`claude`
    *   如果程序能正常启动而没有提示缺少 API Key，说明永久配置成功！

现在，您已经完成了所有的安装和配置流程。

# 全面掌控 Claude Code：命令 + 参数 + 快捷键一文全整理（建议收藏）

发表于： 2025-07-10 | 更新于： 2025-07-11 | 分类： Claude Code

> 近日，随着Cursor套餐定价的风波，Claude Code 无疑成为了最近颇受欢迎的代码助手，不仅支持多种编程语言，还比Cursor更能理解复杂的上下文逻辑，极受广大开发者的青睐。不过，与其他AI编程助手不同的是，Claude Code 以 CLI 形式运行在本地终端，对于开发者来说，部署操作没什么，但对于代码小白来说，想真正用顺手，还是得熟悉它的命令和用法。为更方便大家使用，为此我整理了一份 Claude Code 的实用命令速查表，涵盖了常用场景和技巧，方便大家在工作中随时查阅。

说明：

-   未进入Claude Code前使用： CLI 命令、CLI 参数（Flags）、交互模式快捷键
-   进入Claude Code后使用： 斜杠命令、高级功能与实用补充

---

## 1. 斜杠命令（Slash Commands）

| 命令 | 功能说明 |
| --- | --- |
| `/add-dir <path>` | 添加额外工作目录供 Claude 访问 |
| `/bug` | 上报错误给 Anthropic |
| `/clear` | 清除当前会话上下文 |
| `/compact [说明]` | 整理对话内容，减少 token 使用 |
| `/config` | 查看或修改配置 |
| `/cost` | 查看 token 使用统计 |
| `/doctor` | 检查 Claude Code 安装状态 |
| `/help` | 获取命令帮助列表 |
| `/init` | 创建 `CLAUDE.md`，初始化项目上下文，存储重要的项目信息、约定和常用命令 |
| `/login`、`/logout` | 登录／登出 Anthropic 账户 |
| `/mcp` | 管理 Model Context Protocol（MCP）连接 |
| `/memory` | 编辑项目记忆文件（`CLAUDE.md`） |
| `/model` | 切换当前使用模型（如 Sonnet/Opus） |
| `/permissions` | 查看/修改权限设置 |
| `/pr_comments` | 查看 Pull Request 评论 |
| `/review` | 请求代码审查 |
| `/status` | 查看账户与系统状态 |
| `/terminal-setup` | 一键配置 `Shift+Enter` 换行 |
| `/ls-sessions` | 查看对话session-id（未来上线） |
| `/vim` | 进入 vim 编辑模式 |

注： 自定义 slash 命令可在 `.claude/commands` 或 `~/.claude/commands` 中定义。

---

## 2. CLI 命令

| 命令 | 作用 | 示例 |
| --- | --- | --- |
| `claude` | 启动 REPL 交互式会话 | `claude` |
| `claude "..."` | 带初始提示进入会话 | `claude "explain this project"` |
| `claude -p "..."` | 非交互式打印结果后退出 | `claude -p "explain this function"` |
| `cat file \| claude -p "..."` | 处理管道输入文件 | `cat main.py \| claude -p "summarize this code"` |
| `claude -c` | 继续最近一次会话 |  |
| `claude -r <session-id> "..."` | 恢复指定会话 |  |
| `claude update` | 更新 Claude Code 到最新版本 |  |
| `claude mcp` | 配置 MCP 服务器 |  |

### 如何查看指定会话的session-id？

-   查看本地缓存目录: `ls ~/.claude/sessions/`
-   继续上次会话: 使用 `claude -c` 自动连接上次会话。
-   查看 shell 历史: `grep claude ~/.bash_history`
-   未来功能 (可能): `/sessions` 或 `/history` 等命令。

### 怎么配置MCP服务器？

步骤 1：创建 MCP 配置文件（JSON 格式）
创建一个 `.json` 文件 (例如 `servers.json`)，结构如下：

```json
[
  {
    "name": "filesystem",
    "tool": "filesystem",
    "config": {
      "root": "/users/yourname/dev/project",
      "writable": true
    }
  },
  {
    "name": "bash",
    "tool": "bash",
    "config": {
      "allowedCommands": ["git log", "npm install"]
    }
  }
]```

步骤2： 加载 MCP 配置
通过命令 `claude --mcp-config servers.json` 或 `claude mcp` 加载配置文件。

步骤 3（可选）：设置默认 MCP 配置路径
将配置文件保存到默认位置，例如 `~/.claude/mcp/servers.json`。

---

## 3. CLI 参数（Flags）

| 参数 | 说明 | 示例 |
| --- | --- | --- |
| `--add-dir` | 添加工作目录 | `claude --add-dir ../lib` |
| `--allowedTools` | 允许工具列表 | `"Bash(git log:*)"` |
| `--disallowedTools` | 禁用工具列表 | `"Edit"` |
| `--print, -p` | 以非交互模式运行 | `claude -p "..."` |
| `--output-format` | 输出格式（text/json/stream-json） | `--output-format json` |
| `--input-format` | 输入格式控制 | `--input-format stream-json` |
| `--verbose` | 开启详细日志 | `claude --verbose` |
| `--max-turns` | 设置最大对话轮次 | `--max-turns 3` |
| `--model` | 指定使用模型 | `--model claude-sonnet-4-20250514` |
| `--permission-mode` | 设置权限模式 | `--permission-mode plan` |
| `--permission-prompt-tool` | 非交互模式下使用 MCP 工具 | `--permission-prompt-tool mcp_auth_tool` |
| `--resume` | 恢复指定会话 | `--resume abc123` |
| `--continue` | 继续最近会话（同 -c） | `--continue` |
| `--dangerously-skip-permissions` | 跳过权限提示（需谨慎） |  |

---

## 4. 交互模式快捷键

| 快捷键 | 功能 |
| --- | --- |
| `Ctrl+C` | 取消输入/中断生成 |
| `Ctrl+D` | 退出 Claude Code 会话 |
| `Ctrl+L` | 清屏（保留历史） |
| `Ctrl+R` | 历史反向搜索（若终端支持） |
| `↑`/`↓` | 浏览输入历史 |
| `Esc` + `Esc` | 编辑上条消息 |
| `\\`+`Enter` 或 `Option`+`Enter`（macOS）或 `Shift`+`Enter`（需配置） | 多行输入 |
| `#` 开头 | 快速添加记忆到 `CLAUDE.md` |
| `/` 开头 | 激活 Slash 命令提示 |

---

## 5. 高级功能与实用补充

-   [CLAUDE.md](http://claude.md/) 文件机制： 项目上下文记忆文件，自动加载增强对话效果。
-   自定义 Slash 命令： 允许通过 `.claude/commands/*.md` 或 `~/.claude/commands/*.md` 实现自定义工作流脚本。
-   MCP（Model Context Protocol）能力： 集成例如 Puppeteer、数据库、Stripe 等外部服务，实现网页操作、数据库查询、API 调用等。
-   Hooks 支持： 可定义代码推送、合并时自动触发脚本／操作（7 月初新增）。
-   图像操作能力（最新实践）： 支持拖放/粘贴图像、结合 MCP 截图、UI 反馈循环等。
-   多实例并行支持： 可在多个工作目录中分别运行多个 Claude Code 实例，互不干扰。

---

文章作者: Ztop
文章链接: [https://www.zeker.top/posts/claude-code-full-command-list/](https://www.zeker.top/posts/claude-code-full-command-list/)
版权声明: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请注明来自 Ztop の 空间站！