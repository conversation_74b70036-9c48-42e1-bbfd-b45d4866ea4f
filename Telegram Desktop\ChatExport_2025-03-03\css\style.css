body {
    margin: 0;
    font: 12px/18px 'Open Sans',"Lucida Grande","Lucida Sans Unicode",Arial,Helvetica,Verdana,sans-serif;
}
strong {
    font-weight: 700;
}
code, kbd, pre, samp {
    font-family: Menlo,Monaco,Consolas,"Courier New",monospace;
}
code {
    padding: 2px 4px;
    font-size: 90%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px;
}
pre {
    display: block;
    margin: 0;
    line-height: 1.42857143;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    background-color: #f5f5f5;
    border-radius: 4px;
    overflow: auto;
    padding: 3px;
    border: 1px solid #eee;
    max-height: none;
    font-size: inherit;
}
.clearfix:after {
    content: " ";
    visibility: hidden;
    display: block;
    height: 0;
    clear: both;
}
.pull_left {
    float: left;
}
.pull_right {
    float: right;
}
.page_wrap {
    background-color: #ffffff;
    color: #000000;
}
.page_wrap a {
    color: #168acd;
    text-decoration: none;
}
.page_wrap a:hover {
    text-decoration: underline;
}
.page_header {
    position: fixed;
    z-index: 10;
    background-color: #ffffff;
    width: 100%;
    border-bottom: 1px solid #e3e6e8;
}
.page_header .content {
    width: 480px;
    margin: 0 auto;
    border-radius: 0 !important;
}
.page_header a.content {
    background-repeat: no-repeat;
    background-position: 24px 21px;
    background-size: 24px 24px;
}
.bold {
    color: #212121;
    font-weight: 700;
}
.details {
    color: #70777b;
}
.page_header .content .text {
    padding: 24px 24px 22px 24px;
    font-size: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.page_header a.content .text {
    padding: 24px 24px 22px 82px;
}
.page_body {
    padding-top: 64px;
    width: 480px;
    margin: 0 auto;
}
.page_about {
    padding: 24px 24px;
}
.with_divider {
    border-top: 1px solid #e3e6e8;
}
.userpic_link {
    display: block;
    text-decoration: none;
}
.userpic_link:hover {
    text-decoration: none;
}
.userpic {
    display: block;
    border-radius: 50%;
    overflow: hidden;
}
.story {
    display: block;
    border-radius: 4px;
    overflow: hidden;
}
.userpic .initials {
    display: block;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    user-select: none;
}
.color_red,
.userpic1,
.media_call .fill,
.media_file .fill,
.media_live_location .fill {
    background-color: #ff5555;
}
.color_green,
.userpic2,
.media_call.success .fill,
.media_photo .fill {
    background-color: #64bf47;
}
.color_yellow,
.userpic3,
.media_venue .fill {
    background-color: #ffab00;
}
.color_blue,
.userpic4,
.media_audio_file .fill,
.media_voice_message .fill {
    background-color: #4f9cd9;
}
.color_purple,
.userpic5,
.media_game .fill {
    background-color: #9884e8;
}
.color_pink,
.userpic6,
.media_invoice .fill {
    background-color: #e671a5;
}
.color_sea,
.userpic7,
.media_location .fill,
.media_video .fill {
    background-color: #47bcd1;
}
.color_orange,
.userpic8,
.media_contact .fill {
    background-color: #ff8c44;
}
.personal_info {
    padding: 24px;
}
.personal_info .userpic .initials {
    font-size: 30px;
}
.personal_info .rows {
    float: left;
    padding-right: 24px;
}
.personal_info .names {
    width: 164px;
}
.personal_info .info {
    width: 124px;
}
.personal_info .bio {
    width: 400px;
}
.personal_info .row {
    padding-bottom: 16px;
}
a.block_link {
    display: block;
    text-decoration: none !important;
    border-radius: 4px;
}
a.block_link:hover {
    text-decoration: none !important;
    background-color: #f5f7f8;
}
a.expanded {
    padding: 2px 8px;
    margin: -2px -8px;
}
.sections {
    padding: 11px 0;
}
.section {
    height: 48px;
    background-position: 24px 12px;
    background-repeat: no-repeat;
    background-size: 24px 24px;
}
.section .counter {
    float: right;
    padding: 14px 24px 0;
    font-size: 15px;
}
.section .label {
    padding: 15px 0 0 82px;
    font-size: 15px;
}
.list_page .page_about {
    padding: 16px 24px 0;
    font-size: 11px;
}
.list_page .entry_list {
    padding: 16px 0;
}
.list_page .entry {
    padding: 10px 16px;
}
.list_page .entry .userpic .initials {
    font-size: 18px;
}
.list_page .entry .body {
    margin-left: 66px;
}
.list_page .entry .name {
    padding: 4px 0 2px;
    font-size: 14px;
}
.list_page .entry .subname {
    padding-top: 4px;
}
.list_page .entry .details_entry {
    padding-top: 4px;
}
.list_page .entry .info {
    font-size: 11px;
    padding-top: 5px;
}
.history {
    padding: 16px 0;
}
.message {
    margin: 0 -10px;
    transition: background-color 2.0s ease;
}
div.selected {
    background-color: rgba(242,246,250,255);
    transition: background-color 0.5s ease;
}
.service {
    padding: 10px 24px;
}
.service .body {
    text-align: center;
}
.service .userpic_wrap {
    padding-top: 10px;
}
.service .userpic {
    margin: 0 auto;
}
.service .userpic .initials {
    font-size: 24px;
}
.message .userpic .initials {
    font-size: 16px;
}
.default {
    padding: 10px;
}
.default.joined {
    margin-top: -10px;
}
.default .from_name {
    color: #3892db;
    font-weight: 700;
    padding-bottom: 5px;
}
.default .from_name .details {
    font-weight: normal;
}
.default .body {
    margin-left: 60px;
}
.default .text {
    word-wrap: break-word;
    line-height: 150%;
    unicode-bidi: plaintext;
    text-align: start;
}
.default .reply_to,
.default .media_wrap {
    padding-bottom: 5px;
}
.default .media {
    margin: 0 -10px;
    padding: 5px 10px;
}
.default .media .fill,
.default .media .thumb {
    width: 48px;
    height: 48px;
    border-radius: 50%;
}
.default .media .fill {
    background-repeat: no-repeat;
    background-position: 12px 12px;
    background-size: 24px 24px;
}
.default .media .title,
.default .media_poll .question {
    padding-top: 4px;
    font-size: 14px;
}
.default .media .description {
    color: #000000;
    padding-top: 4px;
    font-size: 13px;
}
.default .media .status {
    padding-top: 4px;
    font-size: 13px;
}
.default .video_file_wrap,
.default .animated_wrap {
    position: relative;
}
.default .video_file,
.default .animated,
.default .photo,
.default .sticker {
    display: block;
}
.video_duration {
    background: rgba(0, 0, 0, .4);
    padding: 0px 5px;
    position: absolute;
    z-index: 2;
    border-radius: 2px;
    right: 3px;
    bottom: 3px;
    color: #ffffff;
    font-size: 11px;
}
.video_play_bg {
    background: rgba(0, 0, 0, .4);
    width: 40px;
    height: 40px;
    line-height: 0;
    position: absolute;
    z-index: 2;
    border-radius: 50%;
    overflow: hidden;
    margin: -20px auto 0 -20px;
    top: 50%;
    left: 50%;
    pointer-events: none;
}
.video_play {
    position: absolute;
    display: inline-block;
    top: 50%;
    left: 50%;
    margin-left: -5px;
    margin-top: -9px;
    z-index: 1;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 9px 0 9px 14px;
    border-color: transparent transparent transparent #fff;
}
.gif_play {
    font-weight: 700;
    color: #FFF;
    display: block;
    line-height: 40px;
    font-size: 13px;
    text-align: center;
}
.pagination {
    text-align: center;
    padding: 20px;
    font-size: 16px;
}

.toast_container {
    position: fixed;
    left: 50%;
    top: 50%;
    opacity: 0;
    transition: opacity 3.0s ease;
}
.toast_body {
    margin: 0 -50%;
    float: left;
    border-radius: 15px;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.7);
    color: #ffffff;
}
div.toast_shown {
    opacity: 1;
    transition: opacity 0.4s ease;
}

.section.calls {
    background-image: url(../images/section_calls.png);
}
.section.chats {
    background-image: url(../images/section_chats.png);
}
.section.contacts {
    background-image: url(../images/section_contacts.png);
}
.section.frequent {
    background-image: url(../images/section_frequent.png);
}
.section.photos {
    background-image: url(../images/section_photos.png);
}
.section.sessions {
    background-image: url(../images/section_sessions.png);
}
.section.stories {
    background-image: url(../images/section_stories.png);
}
.section.web {
    background-image: url(../images/section_web.png);
}
.section.other {
    background-image: url(../images/section_other.png)
}
.page_header a.content {
    background-image: url(../images/back.png);
}
.media_call .fill {
    background-image: url(../images/media_call.png)
}
.media_contact .fill {
    background-image: url(../images/media_contact.png)
}
.media_file .fill {
    background-image: url(../images/media_file.png)
}
.media_game .fill {
    background-image: url(../images/media_game.png)
}
.media_live_location .fill,
.media_location .fill,
.media_venue .fill {
    background-image: url(../images/media_location.png)
}
.media_audio_file .fill {
    background-image: url(../images/media_music.png)
}
.media_invoice .fill {
    background-image: url(../images/media_shop.png)
}
.media_voice_message .fill {
    background-image: url(../images/media_voice.png)
}
.media_photo .fill {
    background-image: url(../images/media_photo.png)
}
.media_video .fill {
    background-image: url(../images/media_video.png)
}

@media only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-device-pixel-ratio: 2) {
.section.calls {
    background-image: url(../images/<EMAIL>);
}
.section.chats {
    background-image: url(../images/<EMAIL>);
}
.section.contacts {
    background-image: url(../images/<EMAIL>);
}
.section.frequent {
    background-image: url(../images/<EMAIL>);
}
.section.photos {
    background-image: url(../images/<EMAIL>);
}
.section.sessions {
    background-image: url(../images/<EMAIL>);
}
.section.stories {
    background-image: url(../images/<EMAIL>);
}
.section.web {
    background-image: url(../images/<EMAIL>);
}
.section.other {
    background-image: url(../images/<EMAIL>);
}
.page_header a.content {
    background-image: url(../images/<EMAIL>);
}
.media_call .fill {
    background-image: url(../images/<EMAIL>)
}
.media_contact .fill {
    background-image: url(../images/<EMAIL>)
}
.media_file .fill {
    background-image: url(../images/<EMAIL>)
}
.media_game .fill {
    background-image: url(../images/<EMAIL>)
}
.media_live_location .fill,
.media_location .fill,
.media_venue .fill {
    background-image: url(../images/<EMAIL>)
}
.media_audio_file .fill {
    background-image: url(../images/<EMAIL>)
}
.media_invoice .fill {
    background-image: url(../images/<EMAIL>)
}
.media_voice_message .fill {
    background-image: url(../images/<EMAIL>)
}
.media_photo .fill {
    background-image: url(../images/<EMAIL>)
}
.media_video .fill {
    background-image: url(../images/<EMAIL>)
}
}

.spoiler {
    background: #e8e8e8;
}
.spoiler.hidden {
    background: #a9a9a9;
    cursor: pointer;
    border-radius: 3px;
}
.spoiler.hidden span {
    opacity: 0;
    user-select: none;
}

.bot_buttons_table {
    border-spacing: 0px 2px;
    width: 100%;
}
.bot_button {
    border-radius: 8px;
    text-align: center;
    vertical-align: middle;
    background-color: #168acd40;
}
.bot_button_row {
    display: table;
    table-layout: fixed;
    padding: 0px;
    width:100%;
}
.bot_button_row div {
    display: table-cell;
}
.bot_button_column_separator {
    width: 2px
}

.reactions {
    margin: 5px 0;
}

.reactions .reaction {
    display: inline-flex;
    height: 20px;
    border-radius: 15px;
    background-color: #e8f5fc;
    color: #168acd;
    font-weight: bold;
    margin-bottom: 5px;
}

.reactions .reaction.active {
    background-color: #40a6e2;
    color: #fff;
}

.reactions .reaction.paid {
    background-color: #fdf6e1;
    color: #c58523;
}

.reactions .reaction.active.paid {
    background-color: #ecae0a;
    color: #fdf6e1;
}

.reactions .reaction .emoji {
    line-height: 20px;
    margin: 0 5px;
    font-size: 15px;
}

.reactions .reaction .userpic:not(:first-child) {
    margin-left: -8px;
}

.reactions .reaction .userpic {
    display: inline-block;
}

.reactions .reaction .userpic .initials {
    font-size: 8px;
}

.reactions .reaction .count {
    margin-right: 8px;
    line-height: 20px;
}