<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #f0f0f0;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"font": {"color": "black"}, "group": "\u8bb0\u5f55", "id": "\u8bb0\u5f55_1", "label": "\u7ed3\u679c 1", "shape": "dot", "size": 15, "title": "\u6765\u6e90: eryaosu\nID: hbejqJYBTFif7k0l_I8B"}, {"font": {"color": "black"}, "group": "\u59d3\u540d", "id": "f64342d3f7370821", "label": "\u9648\u6cf0\u5929", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u59d3\u540d\n\u503c: \u9648\u6cf0\u5929"}, {"font": {"color": "black"}, "group": "\u8eab\u4efd\u8bc1", "id": "2d13feaa4703692c", "label": "320104200310221637", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u8eab\u4efd\u8bc1\n\u503c: 320104200310221637"}, {"font": {"color": "black"}, "group": "\u8bb0\u5f55", "id": "\u8bb0\u5f55_2", "label": "\u7ed3\u679c 2", "shape": "dot", "size": 15, "title": "\u6765\u6e90: shga_3000w\nID: 6fMir5YBTFif7k0lhzpZ"}, {"font": {"color": "black"}, "group": "\u6027\u522b", "id": "e7ce6af869541a3e", "label": "\u7537", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u6027\u522b\n\u503c: \u7537"}, {"font": {"color": "black"}, "group": "\u7efc\u5408\u4fe1\u606f", "id": "5d2c5b86f8a4c936", "label": "\u6c5f\u82cf\u7701\u5357\u4eac\u5e02\u79e6\u6dee\u533a   18 03 2003", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u7efc\u5408\u4fe1\u606f\n\u503c: \u6c5f\u82cf\u7701\u5357\u4eac\u5e02\u79e6\u6dee\u533a   18 03 2003"}, {"font": {"color": "black"}, "group": "\u51fa\u751f\u5730", "id": "07c002a235e050a2", "label": "\u6c5f\u82cf\u7701\u5357\u4eac\u5e02\u79e6\u6dee\u533a", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u51fa\u751f\u5730\n\u503c: \u6c5f\u82cf\u7701\u5357\u4eac\u5e02\u79e6\u6dee\u533a"}, {"font": {"color": "black"}, "group": "\u51fa\u751f\u5e74\u4efd", "id": "9384f652389e2aba", "label": "2003", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u51fa\u751f\u5e74\u4efd\n\u503c: 2003"}, {"font": {"color": "black"}, "group": "\u5e74\u9f84", "id": "6988624fcc475ea7", "label": "18", "shape": "dot", "size": 10, "title": "\u5b57\u6bb5: \u5e74\u9f84\n\u503c: 18"}]);
                  edges = new vis.DataSet([{"from": "\u8bb0\u5f55_1", "title": "\u5305\u542b \u59d3\u540d", "to": "f64342d3f7370821"}, {"from": "\u8bb0\u5f55_1", "title": "\u5305\u542b \u8eab\u4efd\u8bc1", "to": "2d13feaa4703692c"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u6027\u522b", "to": "e7ce6af869541a3e"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u7efc\u5408\u4fe1\u606f", "to": "5d2c5b86f8a4c936"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u51fa\u751f\u5730", "to": "07c002a235e050a2"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u51fa\u751f\u5e74\u4efd", "to": "9384f652389e2aba"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u8eab\u4efd\u8bc1", "to": "2d13feaa4703692c"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u59d3\u540d", "to": "f64342d3f7370821"}, {"from": "\u8bb0\u5f55_2", "title": "\u5305\u542b \u5e74\u9f84", "to": "6988624fcc475ea7"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {
    "configure": {
        "enabled": false
    },
    "edges": {
        "color": {
            "inherit": true
        },
        "smooth": {
            "enabled": true,
            "type": "dynamic"
        }
    },
    "interaction": {
        "dragNodes": true,
        "hideEdgesOnDrag": false,
        "hideNodesOnDrag": false
    },
    "physics": {
        "barnesHut": {
            "avoidOverlap": 0.3,
            "centralGravity": 0.1,
            "damping": 0.09,
            "gravitationalConstant": -10000,
            "springConstant": 0.01,
            "springLength": 180
        },
        "enabled": true,
        "stabilization": {
            "enabled": true,
            "fit": true,
            "iterations": 1000,
            "onlyDynamicEdges": false,
            "updateInterval": 50
        }
    }
};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>