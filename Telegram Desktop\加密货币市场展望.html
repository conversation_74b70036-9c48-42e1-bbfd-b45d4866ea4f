<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币市场展望：宏观与微观因素分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #001219;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 400px;
            max-height: 50vh;
        }
        .kpi-card {
            background-color: #005F73;
            color: #E9D8A6;
            border-left: 5px solid #EE9B00;
        }
        .flowchart-step {
            position: relative;
            padding-left: 2rem;
        }
        .flowchart-step:not(:last-child)::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 1.75rem;
            width: 2px;
            height: calc(100% + 2rem);
            background-color: #0A9396;
        }
        .flowchart-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 9999px;
            background-color: #EE9B00;
            border: 3px solid #005F73;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -0.4rem;
            top: 0;
            width: 1.2rem;
            height: 1.2rem;
            background-color: #94D2BD;
            border-radius: 9999px;
            border: 4px solid #005F73;
        }
    </style>
</head>
<body class="text-gray-200">

    <header class="text-center py-10 px-4 bg-gray-900 bg-opacity-30">
        <h1 class="text-4xl md:text-5xl font-bold text-white tracking-wider">加密货币市场展望</h1>
        <p class="mt-4 text-xl text-[#94D2BD]">2025年7月 | 宏观与微观因素深度分析</p>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="macro-environment" class="mb-16">
            <div class="text-center max-w-4xl mx-auto mb-10">
                <h2 class="text-3xl font-bold text-white mb-3">宏观经济环境：利率与通胀的博弈</h2>
                <p class="text-lg text-gray-400">全球宏观经济，特别是美国的货币政策，是影响加密货币等风险资产价格的关键。高利率和持续的通胀压力对市场构成了重大挑战，但政策转向的预期也带来了机遇。</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center mb-12">
                <div class="kpi-card p-6 rounded-lg shadow-xl">
                    <p class="text-xl font-semibold text-[#94D2BD]">美国核心CPI (YoY)</p>
                    <p class="text-5xl font-bold mt-2">3.1%</p>
                    <p class="text-md mt-2 text-gray-300">通胀压力有所缓解，但仍高于目标</p>
                </div>
                <div class="kpi-card p-6 rounded-lg shadow-xl">
                    <p class="text-xl font-semibold text-[#94D2BD]">美联储联邦基金利率</p>
                    <p class="text-5xl font-bold mt-2">5.25%</p>
                    <p class="text-md mt-2 text-gray-300">处于高位，市场关注降息信号</p>
                </div>
                <div class="kpi-card p-6 rounded-lg shadow-xl">
                    <p class="text-xl font-semibold text-[#94D2BD]">美元指数 (DXY)</p>
                    <p class="text-5xl font-bold mt-2">106.5</p>
                    <p class="text-md mt-2 text-gray-300">强势美元压制风险资产表现</p>
                </div>
            </div>

            <div class="bg-[#003049] rounded-lg shadow-2xl p-4 md:p-8">
                <h3 class="text-xl font-bold text-center mb-4 text-[#E9D8A6]">利率周期与比特币价格的负相关性</h3>
                <p class="text-center text-gray-400 max-w-3xl mx-auto mb-6">历史数据显示，美联储加息周期往往对比特币价格构成压力，而降息预期则可能成为下一轮牛市的催化剂。下图展示了过去两年中比特币价格与联邦基金利率的走势对比。</p>
                <div class="chart-container">
                    <canvas id="btcVsFedRateChart"></canvas>
                </div>
            </div>
        </section>

        <section id="micro-signals" class="mb-16">
            <div class="text-center max-w-4xl mx-auto mb-10">
                <h2 class="text-3xl font-bold text-white mb-3">链上与市场信号：微观视角洞察</h2>
                <p class="text-lg text-gray-400">抛开宏观背景，加密市场自身的链上数据、资金流向和衍生品市场活动，为我们提供了判断市场情绪和趋势的独特视角。</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-[#003049] rounded-lg shadow-2xl p-4 md:p-8">
                    <h3 class="text-xl font-bold text-center mb-4 text-[#E9D8A6]">加密货币市值分布</h3>
                    <p class="text-center text-gray-400 max-w-3xl mx-auto mb-6">比特币和以太坊仍然是市场的主导力量，但其他公链和生态应用的崛起正在逐渐改变市场格局。了解市值分布有助于评估市场的集中度与多元化程度。</p>
                    <div class="chart-container" style="height: 380px; max-height: 45vh;">
                        <canvas id="marketDominanceChart"></canvas>
                    </div>
                </div>

                <div class="bg-[#003049] rounded-lg shadow-2xl p-4 md:p-8">
                    <h3 class="text-xl font-bold text-center mb-4 text-[#E9D8A6]">交易所资金净流入/流出</h3>
                     <p class="text-center text-gray-400 max-w-3xl mx-auto mb-6">交易所的资金流向是衡量投资者情绪的重要指标。持续的资金净流出通常被视为看涨信号，表明投资者倾向于长期持有而非出售。</p>
                    <div class="chart-container" style="height: 380px; max-height: 45vh;">
                        <canvas id="exchangeFlowsChart"></canvas>
                    </div>
                </div>

                <div class="bg-[#003049] rounded-lg shadow-2xl p-4 md:p-8 md:col-span-2">
                    <h3 class="text-xl font-bold text-center mb-4 text-[#E9D8A6]">稳定币市值增长趋势</h3>
                    <p class="text-center text-gray-400 max-w-3xl mx-auto mb-6">稳定币市值的增长反映了进入加密生态系统的新增资金量。它是衡量市场流动性和购买潜力的关键先行指标。</p>
                    <div class="chart-container">
                        <canvas id="stablecoinGrowthChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section id="regulation" class="mb-16">
            <div class="text-center max-w-4xl mx-auto mb-10">
                <h2 class="text-3xl font-bold text-white mb-3">监管动态：合规化进程加速</h2>
                <p class="text-lg text-gray-400">全球监管框架的逐步明晰是市场走向成熟的必经之路。欧盟的MiCA法案和美国对现货ETF的批准是里程碑事件，未来的监管政策将持续塑造行业格局。</p>
            </div>

            <div class="bg-[#003049] rounded-lg shadow-2xl p-8 max-w-3xl mx-auto">
                <div class="relative">
                    <div class="flowchart-step mb-8">
                        <div class="flowchart-dot"></div>
                        <h4 class="text-xl font-bold text-[#E9D8A6]">欧盟MiCA法案</h4>
                        <p class="text-green-300 font-semibold mt-1">状态：已全面实施</p>
                        <p class="text-gray-400 mt-2">为加密资产服务提供商提供了统一的法律框架，增强了投资者保护和市场透明度。</p>
                    </div>
                    <div class="flowchart-step mb-8">
                        <div class="flowchart-dot"></div>
                        <h4 class="text-xl font-bold text-[#E9D8A6]">美国现货ETF</h4>
                        <p class="text-green-300 font-semibold mt-1">状态：比特币与以太坊已批准</p>
                        <p class="text-gray-400 mt-2">为传统金融投资者打开了合规的投资渠道，显著增加了市场的流动性和合法性。</p>
                    </div>
                    <div class="flowchart-step">
                        <div class="flowchart-dot"></div>
                        <h4 class="text-xl font-bold text-[#E9D8A6]">美国稳定币法案</h4>
                        <p class="text-yellow-400 font-semibold mt-1">状态：仍在审议中</p>
                        <p class="text-gray-400 mt-2">旨在为稳定币发行方建立联邦层面的监管标准，是未来市场基础设施的关键一环。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="future-events">
            <div class="text-center max-w-4xl mx-auto mb-10">
                <h2 class="text-3xl font-bold text-white mb-3">未来展望：值得关注的关键事件</h2>
                <p class="text-lg text-gray-400">市场的未来走向将由一系列关键事件驱动。从宏观经济决策到行业内部的技术升级，这些节点将是重要的市场催化剂或风险点。</p>
            </div>

            <div class="bg-[#003049] rounded-lg shadow-2xl p-8 max-w-4xl mx-auto">
                <div class="border-l-4 border-[#0A9396] pl-8 space-y-12">
                    <div class="relative timeline-item">
                        <p class="text-sm text-[#94D2BD]">2025年 第三季度</p>
                        <h4 class="text-xl font-bold mt-1 text-white">美联储议息会议 (FOMC)</h4>
                        <p class="text-gray-400 mt-2">市场高度关注9月份的会议，以寻找有关降息时间表的明确信号。任何鸽派或鹰派的言论都将引发市场剧烈波动。</p>
                    </div>
                    <div class="relative timeline-item">
                        <p class="text-sm text-[#94D2BD]">2025年 第四季度</p>
                        <h4 class="text-xl font-bold mt-1 text-white">以太坊“布拉格/Electra”升级</h4>
                        <p class="text-gray-400 mt-2">此次升级预计将引入Verkle树、EIP-3074等重要改进，旨在提升网络的可扩展性和用户体验，可能对ETH及其生态产生积极影响。</p>
                    </div>
                    <div class="relative timeline-item">
                        <p class="text-sm text-[#94D2BD]">每月发布</p>
                        <h4 class="text-xl font-bold mt-1 text-white">美国消费者物价指数 (CPI) 数据</h4>
                        <p class="text-gray-400 mt-2">通胀数据是美联储决策的核心依据。每月的CPI报告都是市场的焦点，直接影响利率预期和资产价格。</p>
                    </div>
                    <div class="relative timeline-item">
                        <p class="text-sm text-[#94D2BD]">2026年 第二季度</p>
                        <h4 class="text-xl font-bold mt-1 text-white">Consensus 2026 大会</h4>
                        <p class="text-gray-400 mt-2">作为全球最具影响力的加密行业盛会之一，届时发布的项目公告和行业领袖的观点将为市场提供新的叙事和方向。</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="text-center py-8 mt-12 border-t border-gray-700">
        <p class="text-gray-500">本信息图表数据基于截至2025年7月的公开信息和市场分析。</p>
        <p class="text-gray-600 text-sm mt-2">由Canvas Infographics生成</p>
    </footer>

    <script>
        const colorPalette = {
            blue: '#0A9396',
            teal: '#94D2BD',
            yellow: '#E9D8A6',
            orange: '#EE9B00',
            darkOrange: '#CA6702',
            red: '#AE2012',
            text: 'rgba(229, 231, 235, 0.8)',
            grid: 'rgba(255, 255, 255, 0.1)',
        };

        function wrapLabel(label) {
            if (typeof label === 'string' && label.length > 16) {
                const words = label.split(' ');
                const lines = [];
                let currentLine = '';
                for (const word of words) {
                    if ((currentLine + word).length > 16) {
                        lines.push(currentLine.trim());
                        currentLine = '';
                    }
                    currentLine += word + ' ';
                }
                lines.push(currentLine.trim());
                return lines;
            }
            return label;
        }
        
        const defaultChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: colorPalette.text,
                        font: {
                            size: 14
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                              return label.join(' ');
                            } else {
                              return label;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: colorPalette.text,
                        font: { size: 12 }
                    },
                    grid: {
                        color: colorPalette.grid,
                        borderColor: colorPalette.grid
                    }
                },
                y: {
                    ticks: {
                        color: colorPalette.text,
                        font: { size: 12 }
                    },
                    grid: {
                        color: colorPalette.grid,
                        borderColor: colorPalette.grid
                    }
                }
            }
        };

        new Chart(document.getElementById('btcVsFedRateChart'), {
            type: 'line',
            data: {
                labels: ['2023 Q3', '2023 Q4', '2024 Q1', '2024 Q2', '2024 Q3', '2024 Q4', '2025 Q1', '2025 Q2'],
                datasets: [
                    {
                        label: '比特币价格 (USD)',
                        data: [27000, 42000, 69000, 61000, 68000, 75000, 92000, 85000],
                        borderColor: colorPalette.orange,
                        backgroundColor: 'rgba(238, 155, 0, 0.1)',
                        fill: true,
                        yAxisID: 'yBtc',
                        tension: 0.4
                    },
                    {
                        label: '美联储利率 (%)',
                        data: [5.25, 5.25, 5.25, 5.25, 5.25, 5.25, 5.25, 5.25],
                        borderColor: colorPalette.teal,
                        backgroundColor: 'transparent',
                        yAxisID: 'yFed',
                        tension: 0.4,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                ...defaultChartOptions,
                scales: {
                    yBtc: {
                        type: 'linear',
                        position: 'left',
                        title: { display: true, text: '比特币价格 (USD)', color: colorPalette.text },
                        ticks: { color: colorPalette.text },
                        grid: { color: colorPalette.grid }
                    },
                    yFed: {
                        type: 'linear',
                        position: 'right',
                        title: { display: true, text: '美联储利率 (%)', color: colorPalette.text },
                        min: 4,
                        max: 6,
                        ticks: { color: colorPalette.text },
                        grid: { drawOnChartArea: false }
                    },
                     x: { ticks: { color: colorPalette.text }, grid: { color: colorPalette.grid } }
                }
            }
        });
        
        new Chart(document.getElementById('marketDominanceChart'), {
            type: 'doughnut',
            data: {
                labels: ['比特币 (BTC)', '以太坊 (ETH)', '稳定币', '其他公链', 'DeFi & GameFi'],
                datasets: [{
                    label: '市值占比',
                    data: [51, 18, 9, 12, 10],
                    backgroundColor: [
                        colorPalette.orange,
                        colorPalette.blue,
                        colorPalette.teal,
                        colorPalette.yellow,
                        colorPalette.darkOrange,
                    ],
                    borderColor: '#003049',
                    borderWidth: 4,
                }]
            },
            options: {
                ...defaultChartOptions,
                plugins: {
                    ...defaultChartOptions.plugins,
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: colorPalette.text,
                            padding: 15
                        }
                    }
                },
                scales: { x: { display: false }, y: { display: false } }
            }
        });
        
        new Chart(document.getElementById('exchangeFlowsChart'), {
            type: 'bar',
            data: {
                labels: ['2024 Q3', '2024 Q4', '2025 Q1', '2025 Q2'],
                datasets: [
                    {
                        label: '比特币净流量 (亿美元)',
                        data: [-20, -50, -120, -90],
                        backgroundColor: colorPalette.orange,
                        borderColor: colorPalette.darkOrange,
                        borderWidth: 2
                    },
                    {
                        label: '以太坊净流量 (亿美元)',
                        data: [-15, -30, -80, -65],
                        backgroundColor: colorPalette.blue,
                        borderColor: '#005F73',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                ...defaultChartOptions,
                 scales: {
                    ...defaultChartOptions.scales,
                    y: { ...defaultChartOptions.scales.y, title: { display: true, text: '资金净流量 (亿美元)', color: colorPalette.text } }
                }
            }
        });

        new Chart(document.getElementById('stablecoinGrowthChart'), {
            type: 'line',
            data: {
                labels: ['2024/07', '2024/09', '2024/11', '2025/01', '2025/03', '2025/05', '2025/07'],
                datasets: [
                    {
                        label: 'USDT 市值',
                        data: [1120, 1150, 1190, 1250, 1310, 1380, 1450],
                        borderColor: colorPalette.teal,
                        backgroundColor: 'rgba(148, 210, 189, 0.2)',
                        fill: true,
                        tension: 0.3
                    },
                    {
                        label: 'USDC 市值',
                        data: [320, 310, 330, 350, 380, 420, 460],
                        borderColor: colorPalette.blue,
                        backgroundColor: 'rgba(10, 147, 150, 0.2)',
                        fill: true,
                        tension: 0.3
                    }
                ]
            },
            options: {
                 ...defaultChartOptions,
                 scales: {
                    ...defaultChartOptions.scales,
                    y: { ...defaultChartOptions.scales.y, title: { display: true, text: '市值 (亿美元)', color: colorPalette.text } }
                }
            }
        });
    </script>
</body>
</html>
