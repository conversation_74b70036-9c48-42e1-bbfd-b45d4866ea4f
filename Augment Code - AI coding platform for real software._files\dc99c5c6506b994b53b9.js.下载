(()=>{"use strict";var e,t,r={},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e](o,o.exports,i),o.exports}i.m=r,i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>e+"/431110629a9fe8297174.js",i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="Destination:",i.l=(r,n,o,a)=>{if(e[r])e[r].push(n);else{var s,d;if(void 0!==o)for(var l=document.getElementsByTagName("script"),c=0;c<l.length;c++){var u=l[c];if(u.getAttribute("src")==r||u.getAttribute("data-webpack")==t+o){s=u;break}}s||(d=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,i.nc&&s.setAttribute("nonce",i.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[n];var p=(t,n)=>{s.onerror=s.onload=null,clearTimeout(f);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach((e=>e(n))),t)return t(n)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=p.bind(null,s.onerror),s.onload=p.bind(null,s.onload),d&&document.head.appendChild(s)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&!e;)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e+"../"})(),(()=>{var e={209:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=o);var a=i.p+i.u(t),s=new Error;i.l(a,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,n[1](s)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,o,[a,s,d]=r,l=0;if(a.some((t=>0!==e[t]))){for(n in s)i.o(s,n)&&(i.m[n]=s[n]);if(d)d(i)}for(t&&t(r);l<a.length;l++)o=a[l],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkDestination=self.webpackChunkDestination||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var o={};i.d(o,{default:()=>d});const a="rdt_cid_seg",s={get:e=>window.localStorage.getItem(e),set:(e,t)=>window.localStorage.setItem(e,t)},d=function(e){const t=async t=>(await i.e(845).then(i.bind(i,3962))).generatePlugins(e,t,t.subscriptions||[]);return t.pluginName=e.name,t}({name:"Reddit Plugins",mode:"device",settings:{},actions:{redditPlugin:{title:"Reddit Browser Plugin",description:"Enriches Segment payloads with data from the Reddit Pixel",platform:"web",hidden:!1,defaultSubscription:'type = "track" or type = "identify" or type = "page" or type = "group" or type = "alias"',fields:{},lifecycleHook:"enrichment",perform:(e,{context:t,analytics:r})=>{const n=r.storage??s,i=n.get("_rdt_uuid"),o=n.get(a);if(i||o){const e={};o&&(e.click_id=o),i&&(e.uuid=i),(!1!==t.event.integrations?.All||t.event.integrations["Reddit Conversions Api"])&&t.updateEvent("integrations.Reddit Conversions Api",e)}}}},initialize:async({analytics:e})=>{const t=e.storage??s,r=new URLSearchParams(window.location.search).get("rdt_cid")||null;return r&&t.set(a,r),{}}});window["reddit-pluginsDestination"]=o.default})();
//# sourceMappingURL=dc99c5c6506b994b53b9.js.map