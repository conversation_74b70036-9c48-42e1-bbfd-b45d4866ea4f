{"name": "My workflow 2", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [260, -140], "id": "206fd5bc-50f3-43e4-a28f-a5dc2bf80bc5", "name": "When clicking ‘Test workflow’"}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://www.newsminimalist.com/?sort=significance", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [440, 0], "id": "3137bc06-a7b6-49fa-8d13-3b4226113847", "name": "FireCrawl", "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.data }}", "hasOutputParser": true, "options": {"systemMessage": "请你总结新闻，以简短的中文语言，按照重要程度排序，以纯文本格式输出，不要markdown语法,需要在新闻前增加重要程度，按照这种格式：x.y：新闻内容"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [660, 0], "id": "842c147f-fb59-41c8-9163-b0623a6b91cd", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [660, 440], "id": "bde0c119-8e9c-4b98-b3a4-00107ccd2d2f", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "ewpcdsep525MvybV", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"chatId": "**********", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1020, 0], "id": "726aff39-20ea-4f40-be76-8a3dec0dc17a", "name": "Telegram", "webhookId": "1d093cff-75e4-441a-ab2e-705b28b2cadb", "credentials": {"telegramApi": {"id": "INbwWOij7J3Za6dt", "name": "Telegram account"}}}, {"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [260, 0], "id": "672f7156-6838-4aac-925a-175956d249ae", "name": "Schedule Trigger"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "FireCrawl", "type": "main", "index": 0}]]}, "FireCrawl": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "FireCrawl", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "619a807f-62ca-4c01-b778-acfe4736419a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ab79ca41e488f6e0ed0db336857a0eb9ff536462f80fb4ab7db693a56ec15574"}, "id": "8YFC9JraJv2t82KF", "tags": []}